#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的列间关系配置功能
"""

import pandas as pd
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

try:
    from metadata_manager import metadata_manager
    from metadata_ui import MetadataUI
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_relationship_analysis():
    """测试智能关系分析功能"""
    print("🧠 测试智能关系分析功能")
    print("=" * 50)
    
    # 测试数据集1：销售数据
    sales_columns = ['日期', '产品名称', '销售额', '单价', '销量', '地区', '销售员']
    print(f"📊 销售数据列名: {sales_columns}")
    
    suggestions = MetadataUI._analyze_column_relationships(sales_columns)
    
    print(f"💡 智能建议数量: {len(suggestions)}")
    for i, suggestion in enumerate(suggestions, 1):
        print(f"  {i}. {suggestion['type']}: {suggestion['description']}")
        print(f"     涉及列: {', '.join(suggestion['columns'])}")
        print(f"     源列: {suggestion['source']} → 目标列: {', '.join(suggestion['targets'])}")
    
    print("\n" + "-" * 50)
    
    # 测试数据集2：财务数据
    finance_columns = ['月份', '收入', '成本', '利润', '部门', '预算']
    print(f"📊 财务数据列名: {finance_columns}")
    
    suggestions = MetadataUI._analyze_column_relationships(finance_columns)
    
    print(f"💡 智能建议数量: {len(suggestions)}")
    for i, suggestion in enumerate(suggestions, 1):
        print(f"  {i}. {suggestion['type']}: {suggestion['description']}")
        print(f"     涉及列: {', '.join(suggestion['columns'])}")
        print(f"     源列: {suggestion['source']} → 目标列: {', '.join(suggestion['targets'])}")
    
    print("\n" + "-" * 50)
    
    # 测试数据集3：库存数据
    inventory_columns = ['商品编号', '商品名称', '库存数量', '单价', '总价', '供应商', '仓库位置']
    print(f"📊 库存数据列名: {inventory_columns}")
    
    suggestions = MetadataUI._analyze_column_relationships(inventory_columns)
    
    print(f"💡 智能建议数量: {len(suggestions)}")
    for i, suggestion in enumerate(suggestions, 1):
        print(f"  {i}. {suggestion['type']}: {suggestion['description']}")
        print(f"     涉及列: {', '.join(suggestion['columns'])}")
        print(f"     源列: {suggestion['source']} → 目标列: {', '.join(suggestion['targets'])}")

def test_relationship_conversion():
    """测试关系配置转换功能"""
    print("\n🔄 测试关系配置转换功能")
    print("=" * 50)
    
    # 模拟结构化关系配置
    relationships_config = [
        {
            "source_column": "销售额",
            "target_columns": ["单价", "销量"],
            "relationship_type": "计算关系",
            "description": "销售额 = 单价 × 销量"
        },
        {
            "source_column": "地区",
            "target_columns": ["销售额"],
            "relationship_type": "分组关系", 
            "description": "按地区统计销售额"
        },
        {
            "source_column": "利润",
            "target_columns": ["收入", "成本"],
            "relationship_type": "计算关系",
            "description": "利润 = 收入 - 成本"
        }
    ]
    
    print("📋 原始结构化配置:")
    for i, config in enumerate(relationships_config, 1):
        print(f"  {i}. 源列: {config['source_column']}")
        print(f"     目标列: {', '.join(config['target_columns'])}")
        print(f"     类型: {config['relationship_type']}")
        print(f"     描述: {config['description']}")
        print()
    
    # 转换为兼容格式
    converted_relationships = {}
    for config in relationships_config:
        source = config["source_column"]
        targets = config["target_columns"]
        rel_type = config["relationship_type"]
        description = config["description"]
        
        if source and targets and description:
            rel_key = f"{source}_{'_'.join(targets)}"
            rel_value = f"[{rel_type}] {description}"
            converted_relationships[rel_key] = rel_value
    
    print("🔄 转换后的兼容格式:")
    for key, value in converted_relationships.items():
        print(f"  {key}: {value}")

def test_relationship_templates():
    """测试关系模板功能"""
    print("\n📋 测试关系模板功能")
    print("=" * 50)
    
    # 模拟关系模板
    relationship_templates = {
        "计算关系": {
            "销售额_单价_销量": "销售额 = 单价 × 销量",
            "利润_收入_成本": "利润 = 收入 - 成本",
            "总价_数量_单价": "总价 = 数量 × 单价"
        },
        "分组关系": {
            "地区_销售额": "按地区统计销售额",
            "产品_销量": "按产品分组统计销量",
            "时间_指标": "按时间维度分组分析"
        }
    }
    
    # 测试列名
    test_columns = ['销售额', '单价', '销量', '地区', '产品名称', '日期']
    
    print(f"📊 测试列名: {test_columns}")
    print()
    
    for template_type, templates in relationship_templates.items():
        print(f"🔧 {template_type}模板:")
        
        for template_key, template_desc in templates.items():
            template_parts = template_key.split('_')
            
            if len(template_parts) >= 2:
                source = template_parts[0]
                targets = template_parts[1:]
                
                # 检查列是否存在
                source_exists = source in test_columns
                targets_exist = all(t in test_columns for t in targets)
                
                status = "✅ 可应用" if source_exists and targets_exist else "❌ 不适用"
                
                print(f"  - {template_desc}")
                print(f"    模板: {template_key}")
                print(f"    状态: {status}")
                
                if not source_exists:
                    print(f"    缺少源列: {source}")
                if not targets_exist:
                    missing_targets = [t for t in targets if t not in test_columns]
                    print(f"    缺少目标列: {', '.join(missing_targets)}")
                print()

def create_test_data_with_relationships():
    """创建包含关系的测试数据"""
    print("\n📊 创建测试数据并配置关系")
    print("=" * 50)
    
    # 创建销售数据
    sales_data = pd.DataFrame({
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑'],
        '单价': [8500, 6200, 3200],
        '销量': [2, 3, 5],
        '销售额': [17000, 18600, 16000],  # 单价 × 销量
        '地区': ['北京', '上海', '广州'],
        '销售员': ['张三', '李四', '王五']
    })
    
    table_name = "sales_with_relationships"
    
    # 注册表格
    print(f"📝 注册表格: {table_name}")
    metadata_manager.register_table(table_name, sales_data, use_smart_inference=True)
    
    # 获取表格元数据
    table_metadata = metadata_manager.get_table_metadata(table_name)
    
    print(f"✅ 表格注册成功")
    print(f"列名: {list(table_metadata.columns.keys())}")
    
    # 模拟配置关系
    relationships_config = [
        {
            "source_column": "销售额",
            "target_columns": ["单价", "销量"],
            "relationship_type": "计算关系",
            "description": "销售额 = 单价 × 销量"
        },
        {
            "source_column": "地区",
            "target_columns": ["销售额"],
            "relationship_type": "分组关系",
            "description": "按地区统计销售额"
        }
    ]
    
    # 转换并保存关系
    new_relationships = {}
    for config in relationships_config:
        source = config["source_column"]
        targets = config["target_columns"]
        rel_type = config["relationship_type"]
        description = config["description"]
        
        rel_key = f"{source}_{'_'.join(targets)}"
        rel_value = f"[{rel_type}] {description}"
        new_relationships[rel_key] = rel_value
    
    # 更新表格元数据
    table_metadata.relationships = new_relationships
    metadata_manager._save_configurations()
    
    print(f"🔗 配置的关系:")
    for key, value in new_relationships.items():
        print(f"  {key}: {value}")
    
    # 生成LLM上下文验证
    context = metadata_manager.generate_llm_context(table_name, sales_data)
    
    print(f"\n📄 生成的LLM上下文片段:")
    context_lines = context.split('\n')
    relationship_section = False
    
    for line in context_lines:
        if "列间关系:" in line:
            relationship_section = True
        
        if relationship_section:
            print(line)
            if line.strip() == "":
                break

def main():
    """主测试函数"""
    print("🚀 开始测试改进后的列间关系配置功能")
    print("=" * 60)
    
    try:
        # 1. 测试智能关系分析
        test_relationship_analysis()
        
        # 2. 测试关系配置转换
        test_relationship_conversion()
        
        # 3. 测试关系模板
        test_relationship_templates()
        
        # 4. 创建测试数据并配置关系
        create_test_data_with_relationships()
        
        print("\n" + "=" * 60)
        print("🎉 列间关系配置功能测试完成！")
        
        print("\n✅ 测试结果:")
        print("- 智能关系分析功能正常")
        print("- 关系配置转换功能正常")
        print("- 关系模板功能正常")
        print("- 数据兼容性验证通过")
        
        print("\n🎯 功能改进效果:")
        print("- 用户无需手动输入复杂格式")
        print("- 提供智能建议和模板")
        print("- 结构化配置界面更友好")
        print("- 保持与现有系统兼容")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
