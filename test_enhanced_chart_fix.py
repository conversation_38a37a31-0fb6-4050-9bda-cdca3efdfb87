#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版LLM的图表修复功能
"""

import pandas as pd
from enhanced_tongyi_integration import EnhancedTongyiQianwenLLM

def test_enhanced_chart_conversion():
    """测试增强版LLM的图表转换功能"""
    print("🧪 测试增强版LLM的Streamlit原生图表转换")
    print("=" * 60)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [8000, 4500, 15000, 1800, 3200],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    # 创建增强版LLM实例
    llm = EnhancedTongyiQianwenLLM()
    llm.set_current_data("test_data", df)
    
    # 测试查询（类似您遇到的问题）
    test_query = "请分析2024年各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {test_query}")
    print("-" * 40)
    
    try:
        # 调用LLM生成代码
        code = llm.call(test_query, df.to_string())
        
        print("\n生成的代码:")
        print(code)
        print()
        
        # 检查是否使用了Streamlit原生方法
        uses_streamlit_native = any(method in code for method in [
            'st.bar_chart', 'st.line_chart', 'st.scatter_chart', 'st.area_chart'
        ])
        
        uses_matplotlib = 'import matplotlib.pyplot as plt' in code
        
        if uses_streamlit_native:
            print("✅ 成功！使用了Streamlit原生图表方法")
            print("💡 这样可以避免图表不显示的问题")
        elif uses_matplotlib:
            print("❌ 仍在使用matplotlib，转换机制可能有问题")
        else:
            print("ℹ️ 没有生成图表代码")
            
        # 检查代码语法
        try:
            compile(code, '<string>', 'exec')
            print("✅ 代码语法正确")
        except SyntaxError as e:
            print(f"❌ 语法错误: {e}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_direct_conversion():
    """测试直接转换功能"""
    print("\n🔄 测试直接转换功能")
    print("=" * 40)
    
    llm = EnhancedTongyiQianwenLLM()
    
    # 模拟您遇到的matplotlib代码
    matplotlib_code = """import matplotlib.pyplot as plt
product_sales = df.groupby('产品名称')['销售额'].sum().reset_index()       
print(product_sales)
plt.figure(figsize=(12, 8))
plt.bar(product_sales['产品名称'], product_sales['销售额'], color='skyblue')
plt.title('2024年各产品销售额对比', fontsize=16, fontweight='bold')        
plt.xlabel('产品名称', fontsize=12)
plt.ylabel('销售额 (元)', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('temp_chart.png')"""

    print("原始matplotlib代码:")
    print(matplotlib_code)
    print()
    
    # 测试转换
    converted_code = llm.enforce_streamlit_native_charts(
        matplotlib_code, 
        "请分析2024年各产品销售额，用柱状图展示"
    )
    
    print("转换后的代码:")
    print(converted_code)
    print()
    
    # 检查转换结果
    if 'st.bar_chart' in converted_code:
        print("✅ 成功转换为Streamlit原生柱状图")
        print("💡 现在图表应该能正常显示了！")
    else:
        print("❌ 转换失败")

if __name__ == "__main__":
    test_enhanced_chart_conversion()
    test_direct_conversion()
    
    print("\n" + "=" * 60)
    print("🎯 修复总结")
    print("=" * 60)
    print("问题：图表不显示，日志显示matplotlib代码但前端无图表")
    print()
    print("根本原因：")
    print("1. 应用使用的是EnhancedTongyiQianwenLLM，不是TongyiQianwenLLM")
    print("2. 增强版LLM没有强制转换机制")
    print("3. AI生成matplotlib代码，但在Streamlit中渲染有问题")
    print()
    print("解决方案：")
    print("1. ✅ 在EnhancedTongyiQianwenLLM中添加了强制转换机制")
    print("2. ✅ 自动检测matplotlib代码并转换为Streamlit原生")
    print("3. ✅ 使用st.bar_chart等原生方法，确保图表正常显示")
    print()
    print("💡 现在您的应用应该能正常显示图表了！")
