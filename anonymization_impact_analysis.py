#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脱敏对LLM结果影响分析
分析脱敏后是否会误导大模型返回错误结果
"""

import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_anonymization_impact():
    """分析脱敏对LLM结果的影响"""
    print("🔍 脱敏对LLM结果影响分析")
    print("=" * 60)
    
    # 加载原始数据
    df_original = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 创建脱敏数据
    from data_anonymization_solution import DataAnonymizer
    anonymizer = DataAnonymizer()
    df_anonymized, report = anonymizer.anonymize_dataframe(df_original)
    
    print("📊 原始数据示例:")
    print(df_original.head(3))
    print("\n🔒 脱敏后数据示例:")
    print(df_anonymized.head(3))
    
    # 分析潜在问题
    analyze_potential_issues(df_original, df_anonymized)
    
    return df_original, df_anonymized

def analyze_potential_issues(df_original, df_anonymized):
    """分析潜在的误导问题"""
    print("\n⚠️ 潜在问题分析")
    print("=" * 60)
    
    print("1️⃣ 产品名称脱敏的影响:")
    print("-" * 40)
    
    # 原始产品名称
    original_products = df_original['产品名称'].unique()
    anonymized_products = df_anonymized['产品名称'].unique()
    
    print("原始产品名称:", original_products)
    print("脱敏后名称:", anonymized_products)
    
    print("\n❌ 可能的问题:")
    print("- LLM返回结果会使用脱敏后的名称（如'电脑类产品A'）")
    print("- 用户看到的结果与实际产品名称不符")
    print("- 无法直接对应到真实的产品")
    
    print("\n2️⃣ 员工姓名脱敏的影响:")
    print("-" * 40)
    
    original_employees = df_original['销售员'].unique()
    anonymized_employees = df_anonymized['销售员'].unique()
    
    print("原始员工姓名:", original_employees)
    print("脱敏后代号:", anonymized_employees)
    
    print("\n❌ 可能的问题:")
    print("- LLM返回'员工258业绩最好'，但用户不知道这是谁")
    print("- 无法进行实际的人员管理决策")
    
    print("\n3️⃣ 财务数据脱敏的影响:")
    print("-" * 40)
    
    original_sales = df_original['销售额'].sum()
    anonymized_sales = df_anonymized['销售额'].sum()
    scale_factor = anonymized_sales / original_sales
    
    print(f"原始总销售额: {original_sales:,}")
    print(f"脱敏后总销售额: {anonymized_sales:,}")
    print(f"缩放比例: {scale_factor:.2%}")
    
    print("\n❌ 可能的问题:")
    print("- LLM返回的金额数字与实际不符")
    print("- 需要用户自己换算回真实金额")

def demonstrate_llm_result_issues():
    """演示LLM结果中的问题"""
    print("\n🤖 LLM结果问题演示")
    print("=" * 60)
    
    print("📝 查询: '哪个产品销量最高？'")
    print()
    
    print("✅ 使用原始数据的LLM回答:")
    print("根据数据分析，笔记本电脑的总销量最高，共销售了16台。")
    print()
    
    print("❌ 使用脱敏数据的LLM回答:")
    print("根据数据分析，电脑类产品A的总销量最高，共销售了16台。")
    print()
    
    print("🔍 问题分析:")
    print("- 用户无法知道'电脑类产品A'对应的真实产品")
    print("- 需要额外的映射步骤才能理解结果")
    print("- 影响决策的直观性和可操作性")

def propose_solutions():
    """提出解决方案"""
    print("\n💡 解决方案")
    print("=" * 60)
    
    print("🎯 方案1: 结果反脱敏（推荐）")
    print("-" * 40)
    print("✅ 发送脱敏数据给LLM")
    print("✅ LLM返回脱敏结果")
    print("✅ 自动将结果中的脱敏名称替换回真实名称")
    print("✅ 用户看到真实的产品名称和员工姓名")
    print()
    
    print("示例:")
    print("LLM返回: '电脑类产品A销量最高'")
    print("反脱敏后: '笔记本电脑销量最高'")
    print()
    
    print("🎯 方案2: 智能脱敏策略")
    print("-" * 40)
    print("✅ 对索引类数据（产品名称、员工姓名）使用可逆脱敏")
    print("✅ 对数值类数据（金额）使用不可逆脱敏")
    print("✅ 保持映射关系，支持结果还原")
    print()
    
    print("🎯 方案3: 分层脱敏")
    print("-" * 40)
    print("✅ 发送给LLM时脱敏")
    print("✅ 在本地保存映射关系")
    print("✅ 结果展示时自动还原关键信息")
    print("✅ 财务数据保持脱敏状态")

def create_reverse_anonymization_demo():
    """创建反脱敏演示"""
    print("\n🔄 反脱敏功能演示")
    print("=" * 60)
    
    # 创建映射关系
    mapping = {
        "电脑类产品A": "笔记本电脑",
        "电脑类产品B": "台式电脑", 
        "电脑类产品C": "平板电脑",
        "通讯设备A": "手机",
        "智能设备A": "智能手表",
        "音频设备A": "耳机",
        "外设A": "键盘",
        "外设B": "鼠标",
        "显示设备A": "显示器",
        "员工258": "张三",
        "员工349": "李四",
        "员工482": "王五",
        "员工320": "赵六"
    }
    
    # 模拟LLM返回的脱敏结果
    llm_response = """
根据数据分析：
1. 电脑类产品A的总销量最高，达到16台
2. 员工258的销售业绩最好，总销售额为5847元
3. 华北地区的销售额最高，占总销售额的32%
4. 电脑类产品A在华北地区最受欢迎
"""
    
    print("🤖 LLM原始返回结果:")
    print(llm_response)
    
    # 执行反脱敏
    restored_response = llm_response
    for anonymized, original in mapping.items():
        restored_response = restored_response.replace(anonymized, original)
    
    print("\n✨ 反脱敏后的结果:")
    print(restored_response)
    
    print("\n✅ 优势:")
    print("- 用户看到真实的产品名称和员工姓名")
    print("- 保持了数据安全（LLM端仍是脱敏数据）")
    print("- 结果直观可操作")

def analyze_financial_data_handling():
    """分析财务数据的处理方式"""
    print("\n💰 财务数据处理分析")
    print("=" * 60)
    
    print("🔍 财务数据的特殊性:")
    print("- 财务数据通常不用于索引，主要用于计算和比较")
    print("- 相对关系比绝对数值更重要")
    print("- 可以保持脱敏状态，不影响分析结论")
    print()
    
    print("💡 建议处理方式:")
    print("1. 保持财务数据脱敏状态")
    print("2. 在结果中添加说明：'金额已按比例缩放'")
    print("3. 提供缩放比例，用户可自行换算")
    print("4. 重点展示相对关系和趋势")

if __name__ == "__main__":
    analyze_anonymization_impact()
    demonstrate_llm_result_issues()
    propose_solutions()
    create_reverse_anonymization_demo()
    analyze_financial_data_handling()
