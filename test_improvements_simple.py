#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的UI改进测试应用
"""

import streamlit as st
import pandas as pd
import os
from pathlib import Path
from datetime import datetime

# 页面配置 - 测试侧边栏默认折叠
st.set_page_config(
    page_title="UI改进测试",
    page_icon="🧪",
    layout="wide",
    initial_sidebar_state="collapsed"  # 关键改进1：侧边栏默认折叠
)

def create_test_data():
    """创建测试数据"""
    return pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '类别': ['手机', '平板', '笔记本', '配件', '配件'],
        '价格': [6999, 4599, 14999, 1899, 3199],
        '销量': [1200, 800, 400, 1500, 1000],
        '销售额': [8398800, 3679200, 5999600, 2848500, 3199000]
    })

def auto_load_recent_file():
    """关键改进2：自动加载最近文件"""
    upload_dir = Path("uploaded_files")
    if not upload_dir.exists():
        return None
    
    files = []
    for file_path in upload_dir.iterdir():
        if file_path.is_file() and file_path.suffix.lower() in ['.csv', '.xlsx']:
            files.append({
                'name': file_path.name,
                'path': file_path,
                'modified': datetime.fromtimestamp(file_path.stat().st_mtime)
            })
    
    if files:
        # 按修改时间排序，获取最新文件
        recent_file = sorted(files, key=lambda x: x['modified'], reverse=True)[0]
        try:
            if recent_file['path'].suffix.lower() == '.csv':
                df = pd.read_csv(recent_file['path'])
            else:
                df = pd.read_excel(recent_file['path'])
            return df, recent_file['name']
        except Exception:
            return None
    return None

def init_session_state():
    """初始化会话状态"""
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = []
    
    if 'current_data' not in st.session_state:
        st.session_state.current_data = None
        
    if 'current_file' not in st.session_state:
        st.session_state.current_file = None
    
    if 'auto_loaded' not in st.session_state:
        st.session_state.auto_loaded = False
        
    # 自动加载最近文件
    if not st.session_state.auto_loaded and st.session_state.current_data is None:
        result = auto_load_recent_file()
        if result:
            df, filename = result
            st.session_state.current_data = df
            st.session_state.current_file = filename
            st.session_state.auto_loaded = True
            st.session_state.auto_load_message = f"✅ 已自动加载最近文件: {filename}"

def display_chat_with_results():
    """关键改进3：聊天输出集成"""
    st.subheader("💬 智能对话")
    
    # 显示聊天历史
    for message in st.session_state.chat_history:
        if message['role'] == 'user':
            with st.chat_message("user"):
                st.write(message['content'])
        else:
            with st.chat_message("assistant"):
                st.write(message['content'])
                
                # 如果有分析结果，直接在聊天中显示
                if message.get('analysis_result'):
                    result = message['analysis_result']
                    
                    # 显示数据表格
                    if isinstance(result, pd.DataFrame):
                        st.dataframe(result, use_container_width=True)
                    
                    # 显示图表
                    elif isinstance(result, dict) and 'chart_data' in result:
                        st.bar_chart(result['chart_data'])

def simple_analysis(df, query):
    """简化的数据分析"""
    query = query.lower()
    
    if '总销售额' in query or '销售额总计' in query:
        total = df['销售额'].sum()
        return f"总销售额为: ¥{total:,.0f}", None
    
    elif '最高销量' in query or '销量最高' in query:
        max_row = df.loc[df['销量'].idxmax()]
        return f"销量最高的产品是: {max_row['产品名称']} (销量: {max_row['销量']:,})", None
    
    elif '产品销量' in query or '各产品销量' in query:
        chart_data = df.set_index('产品名称')['销量']
        return "各产品销量分布如下:", {'chart_data': chart_data}
    
    elif '数据概览' in query or '显示数据' in query:
        return "数据概览如下:", df.head()
    
    else:
        return "抱歉，我还不能理解这个查询。请尝试：'总销售额'、'最高销量'、'产品销量'、'数据概览'", None

def main():
    """主函数"""
    init_session_state()
    
    st.title("🧪 UI/UX 改进测试应用")
    
    # 侧边栏
    with st.sidebar:
        st.title("📁 文件管理")
        
        # 显示自动加载消息
        if hasattr(st.session_state, 'auto_load_message'):
            st.info(st.session_state.auto_load_message)
            del st.session_state.auto_load_message
        
        # 创建测试数据
        if st.button("创建测试数据"):
            upload_dir = Path("uploaded_files")
            upload_dir.mkdir(exist_ok=True)
            
            df = create_test_data()
            test_file = upload_dir / "test_data.csv"
            df.to_csv(test_file, index=False, encoding='utf-8')
            
            st.success("✅ 测试数据已创建")
            st.info("💡 刷新页面测试自动加载功能")
        
        # 手动上传文件
        uploaded_file = st.file_uploader("上传CSV文件", type=['csv'])
        if uploaded_file:
            df = pd.read_csv(uploaded_file)
            st.session_state.current_data = df
            st.session_state.current_file = uploaded_file.name
            st.success(f"✅ 文件已加载: {uploaded_file.name}")
    
    # 主内容区
    if st.session_state.current_data is not None:
        st.success(f"📊 当前数据: {st.session_state.current_file} "
                  f"({st.session_state.current_data.shape[0]}行 × {st.session_state.current_data.shape[1]}列)")
        
        # 数据预览
        with st.expander("📋 数据预览", expanded=False):
            st.dataframe(st.session_state.current_data)
        
        # 聊天界面
        display_chat_with_results()
        
        # 用户输入
        user_input = st.chat_input("请输入您的问题...")
        
        if user_input:
            # 添加用户消息
            st.session_state.chat_history.append({
                'role': 'user',
                'content': user_input
            })
            
            # 分析数据
            response, result = simple_analysis(st.session_state.current_data, user_input)
            
            # 添加助手回复
            st.session_state.chat_history.append({
                'role': 'assistant',
                'content': response,
                'analysis_result': result
            })
            
            st.rerun()
    
    else:
        st.info("👈 请在侧边栏上传数据文件或创建测试数据")
        
        st.markdown("""
        ## 🎯 测试说明
        
        ### 1. 侧边栏默认折叠 ✅
        - 页面加载时侧边栏应该是折叠状态
        - 提供更多主内容区域空间
        
        ### 2. 自动文件加载 ✅
        - 点击"创建测试数据"按钮
        - 刷新页面，应该自动加载最近的文件
        - 侧边栏会显示自动加载提示
        
        ### 3. 聊天输出集成 ✅
        - 上传数据后，在聊天框中输入查询
        - 分析结果直接显示在聊天对话中
        - 支持的查询：'总销售额'、'最高销量'、'产品销量'、'数据概览'
        """)

if __name__ == "__main__":
    main()
