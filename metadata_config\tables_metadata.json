{"customers": {"table_name": "customers", "description": "customers数据表，包含4个字段和3条记录，属于客户管理领域", "business_domain": "客户管理", "columns": {"客户ID": {"name": "客户ID", "display_name": "客户ID", "description": "客户ID字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["C001", "C002", "C003"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-04T09:15:08.267972", "updated_at": "2025-08-04T09:15:08.267972"}, "客户名称": {"name": "客户名称", "display_name": "客户名称", "description": "客户名称字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["张三公司", "李四企业", "王五集团"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-04T09:15:08.267972", "updated_at": "2025-08-04T09:15:08.267972"}, "联系电话": {"name": "联系电话", "display_name": "联系电话", "description": "联系电话字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["***********", "***********", "***********"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T09:15:08.267972", "updated_at": "2025-08-04T09:15:08.267972"}, "地区": {"name": "地区", "display_name": "地区", "description": "地区字段，表示地理位置或区域信息，每个值都是唯一的", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["地理", "区域", "空间"], "created_at": "2025-08-04T09:15:08.267972", "updated_at": "2025-08-04T09:15:08.267972"}}, "relationships": {}, "primary_keys": ["客户ID", "客户名称"], "created_at": "2025-08-04T09:15:08.267972", "updated_at": "2025-08-04T09:15:08.267972", "version": "1.0.0"}, "orders": {"table_name": "orders", "description": "orders数据表，包含5个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"订单ID": {"name": "订单ID", "display_name": "订单ID", "description": "订单ID字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["O001", "O002", "O003"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-04T09:15:08.289093", "updated_at": "2025-08-04T09:15:08.289093"}, "客户ID": {"name": "客户ID", "display_name": "客户ID", "description": "客户ID字段的数据信息，包含2种不同的值", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["C001", "C002"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-04T09:15:08.289093", "updated_at": "2025-08-04T09:15:08.289093"}, "产品ID": {"name": "产品ID", "display_name": "产品ID", "description": "产品ID字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["P001", "P002", "P003"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T09:15:08.289093", "updated_at": "2025-08-04T09:15:08.289093"}, "数量": {"name": "数量", "display_name": "数量", "description": "数量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["2", "1", "3"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-04T09:15:08.289093", "updated_at": "2025-08-04T09:15:08.289093"}, "订单日期": {"name": "订单日期", "display_name": "订单日期", "description": "订单日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-15", "2024-01-16", "2024-01-17"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-04T09:15:08.289093", "updated_at": "2025-08-04T09:15:08.289093"}}, "relationships": {}, "primary_keys": ["订单ID", "产品ID"], "created_at": "2025-08-04T09:15:08.289093", "updated_at": "2025-08-04T09:15:08.289093", "version": "1.0.0"}, "products": {"table_name": "products", "description": "products数据表，包含4个字段和3条记录，属于库存管理领域", "business_domain": "库存管理", "columns": {"产品ID": {"name": "产品ID", "display_name": "产品ID", "description": "产品ID字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["P001", "P002", "P003"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T09:15:08.310059", "updated_at": "2025-08-04T09:15:08.310059"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本电脑", "台式电脑", "平板电脑"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T09:15:08.310059", "updated_at": "2025-08-04T09:15:08.310059"}, "价格": {"name": "价格", "display_name": "价格", "description": "价格字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["8999", "5999", "3999"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T09:15:08.310059", "updated_at": "2025-08-04T09:15:08.310059"}, "库存": {"name": "库存", "display_name": "库存", "description": "库存字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["50", "30", "80"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-04T09:15:08.310059", "updated_at": "2025-08-04T09:15:08.310059"}}, "relationships": {}, "primary_keys": ["产品ID", "产品名称"], "created_at": "2025-08-04T09:15:08.310059", "updated_at": "2025-08-04T09:15:08.310059", "version": "1.0.0"}, "demo_data": {"table_name": "demo_data", "description": "演示数据表，包含20条记录和9个字段", "business_domain": "演示数据", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T09:15:08.350771", "updated_at": "2025-08-04T09:15:08.350771"}, "类别": {"name": "类别", "display_name": "类别", "description": "类别字段，用于数据分类和分组，包含5种不同的值", "data_type": "object", "business_meaning": "数据分组和分类分析的维度标识", "examples": ["手机", "平板", "笔记本"], "constraints": {}, "tags": ["分类", "维度", "分组"], "created_at": "2025-08-04T09:15:08.350771", "updated_at": "2025-08-04T09:15:08.350771"}, "价格": {"name": "价格", "display_name": "价格", "description": "价格字段，表示相关的金额数值", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["6999", "4599", "14999"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T09:15:08.350771", "updated_at": "2025-08-04T09:15:08.350771"}, "销量": {"name": "销量", "display_name": "销量", "description": "销量字段，表示相关的数量或计数", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["1200", "800", "400"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-04T09:15:08.350771", "updated_at": "2025-08-04T09:15:08.350771"}, "库存": {"name": "库存", "display_name": "库存", "description": "库存字段，表示相关的数量或计数", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["150", "200", "80"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-04T09:15:08.350771", "updated_at": "2025-08-04T09:15:08.350771"}, "评分": {"name": "评分", "display_name": "评分", "description": "评分字段，表示相关的金额数值，包含7种不同的值", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["4.8", "4.6", "4.9"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T09:15:08.350771", "updated_at": "2025-08-04T09:15:08.350771"}, "上市日期": {"name": "上市日期", "display_name": "上市日期", "description": "上市日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2023-09-15", "2023-10-20", "2023-11-10"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-04T09:15:08.350771", "updated_at": "2025-08-04T09:15:08.350771"}, "供应商": {"name": "供应商", "display_name": "供应商", "description": "供应商字段的数据信息", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["苹果公司", "三星电子", "戴尔公司"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-04T09:15:08.350771", "updated_at": "2025-08-04T09:15:08.350771"}, "成本": {"name": "成本", "display_name": "成本", "description": "成本字段，表示相关的金额数值", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["5500", "3600", "12000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T09:15:08.350771", "updated_at": "2025-08-04T09:15:08.350771"}}, "relationships": {}, "primary_keys": ["产品名称", "上市日期"], "created_at": "2025-08-04T09:15:08.350771", "updated_at": "2025-08-04T09:15:08.350771", "version": "1.0.0"}, "sales_data": {"table_name": "sales_data", "description": "sales_data数据表，包含6个字段和20条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"日期": {"name": "日期", "display_name": "日期", "description": "日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-04T10:45:55.095449", "updated_at": "2025-08-04T10:45:55.095449"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，包含9种不同的值", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本电脑", "台式电脑", "平板电脑"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T10:45:55.095449", "updated_at": "2025-08-04T10:45:55.095449"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["8500", "6200", "3200"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T10:45:55.095449", "updated_at": "2025-08-04T10:45:55.095449"}, "销量": {"name": "销量", "display_name": "销量", "description": "销量字段，表示相关的数量或计数", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["5", "3", "8"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-04T10:45:55.095449", "updated_at": "2025-08-04T10:45:55.095449"}, "地区": {"name": "地区", "display_name": "地区", "description": "地区字段，表示地理位置或区域信息，包含4种不同的值", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["地理", "区域", "空间"], "created_at": "2025-08-04T10:45:55.095449", "updated_at": "2025-08-04T10:45:55.095449"}, "销售员": {"name": "销售员", "display_name": "销售员", "description": "销售员字段，表示相关人员的标识信息，包含4种不同的值", "data_type": "object", "business_meaning": "人员管理和绩效分析的主体标识", "examples": ["张三", "李四", "王五"], "constraints": {}, "tags": ["人员", "标识", "管理"], "created_at": "2025-08-04T10:45:55.095449", "updated_at": "2025-08-04T10:45:55.095449"}}, "relationships": {}, "primary_keys": ["日期", "销售额"], "created_at": "2025-08-04T10:45:55.095449", "updated_at": "2025-08-04T10:45:55.095449", "version": "1.0.0"}, "sales_data.csv": {"table_name": "sales_data.csv", "description": "sales_data.csv数据表，包含6个字段和20条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"日期": {"name": "日期", "display_name": "日期", "description": "日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-04T10:51:02.677630", "updated_at": "2025-08-04T10:51:02.677630"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，包含9种不同的值", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本电脑", "台式电脑", "平板电脑"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T10:51:02.677630", "updated_at": "2025-08-04T10:51:02.677630"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["8500", "6200", "3200"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T10:51:02.677630", "updated_at": "2025-08-04T10:51:02.677630"}, "销量": {"name": "销量", "display_name": "销量", "description": "销量字段，表示相关的数量或计数", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["5", "3", "8"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-04T10:51:02.677630", "updated_at": "2025-08-04T10:51:02.677630"}, "地区": {"name": "地区", "display_name": "地区", "description": "地区字段，表示地理位置或区域信息，包含4种不同的值", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["地理", "区域", "空间"], "created_at": "2025-08-04T10:51:02.677630", "updated_at": "2025-08-04T10:51:02.677630"}, "销售员": {"name": "销售员", "display_name": "销售员", "description": "销售员字段，表示相关人员的标识信息，包含4种不同的值", "data_type": "object", "business_meaning": "人员管理和绩效分析的主体标识", "examples": ["张三", "李四", "王五"], "constraints": {}, "tags": ["人员", "标识", "管理"], "created_at": "2025-08-04T10:51:02.677630", "updated_at": "2025-08-04T10:51:02.677630"}}, "relationships": {}, "primary_keys": ["日期", "销售额"], "created_at": "2025-08-04T10:51:02.677630", "updated_at": "2025-08-04T10:51:02.677630", "version": "1.0.0"}, "test_data": {"table_name": "test_data", "description": "test_data数据表，包含4个字段和5条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T13:42:07.035775", "updated_at": "2025-08-04T13:42:07.035775"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["8000", "4500", "15000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T13:42:07.035775", "updated_at": "2025-08-04T13:42:07.035775"}, "销量": {"name": "销量", "display_name": "销量", "description": "销量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["120", "80", "40"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-04T13:42:07.035775", "updated_at": "2025-08-04T13:42:07.035775"}, "日期": {"name": "日期", "display_name": "日期", "description": "日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-04T13:42:07.035775", "updated_at": "2025-08-04T13:42:07.035775"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T13:42:07.035775", "updated_at": "2025-08-04T13:42:07.035775", "version": "1.0.0"}, "problematic_data": {"table_name": "problematic_data", "description": "problematic_data数据表，包含3个字段和5条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["A", "B", "C"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:06:50.715320", "updated_at": "2025-08-04T15:06:50.715320"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["1000.0", "inf", "2000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:06:50.715320", "updated_at": "2025-08-04T15:06:50.715320"}, "销量": {"name": "销量", "display_name": "销量", "description": "销量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["10", "20", "30"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-04T15:06:50.715320", "updated_at": "2025-08-04T15:06:50.715320"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:06:50.715320", "updated_at": "2025-08-04T15:06:50.715320", "version": "1.0.0"}, "test_data_1": {"table_name": "test_data_1", "description": "test_data_1数据表，包含4个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:08:56.825219", "updated_at": "2025-08-04T15:08:56.825219"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:08:56.825219", "updated_at": "2025-08-04T15:08:56.825219"}, "销量": {"name": "销量", "display_name": "销量", "description": "销量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["120", "80", "40"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-04T15:08:56.825219", "updated_at": "2025-08-04T15:08:56.825219"}, "日期": {"name": "日期", "display_name": "日期", "description": "日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-04T15:08:56.825219", "updated_at": "2025-08-04T15:08:56.825219"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:08:56.825219", "updated_at": "2025-08-04T15:08:56.825219", "version": "1.0.0"}, "test_data_2": {"table_name": "test_data_2", "description": "test_data_2数据表，包含4个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:09:00.531840", "updated_at": "2025-08-04T15:09:00.531840"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:09:00.531840", "updated_at": "2025-08-04T15:09:00.531840"}, "销量": {"name": "销量", "display_name": "销量", "description": "销量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["120", "80", "40"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-04T15:09:00.531840", "updated_at": "2025-08-04T15:09:00.531840"}, "日期": {"name": "日期", "display_name": "日期", "description": "日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-04T15:09:00.531840", "updated_at": "2025-08-04T15:09:00.531840"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:09:00.531840", "updated_at": "2025-08-04T15:09:00.531840", "version": "1.0.0"}, "test_data_3": {"table_name": "test_data_3", "description": "test_data_3数据表，包含4个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:09:02.808140", "updated_at": "2025-08-04T15:09:02.808140"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:09:02.808140", "updated_at": "2025-08-04T15:09:02.808140"}, "销量": {"name": "销量", "display_name": "销量", "description": "销量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["120", "80", "40"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-04T15:09:02.808140", "updated_at": "2025-08-04T15:09:02.808140"}, "日期": {"name": "日期", "display_name": "日期", "description": "日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-04T15:09:02.808140", "updated_at": "2025-08-04T15:09:02.808140"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:09:02.808140", "updated_at": "2025-08-04T15:09:02.808140", "version": "1.0.0"}, "problematic_test": {"table_name": "problematic_test", "description": "problematic_test数据表，包含4个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称@#$": {"name": "产品名称@#$", "display_name": "产品名称@#$", "description": "产品名称@#$字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["A", "B", "C"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:22:25.118329", "updated_at": "2025-08-04T15:22:25.118329"}, "销售额_start": {"name": "销售额_start", "display_name": "销售额_start", "description": "销售额_start字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["1000", "2000", "3000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:22:25.118329", "updated_at": "2025-08-04T15:22:25.118329"}, "销售额_end": {"name": "销售额_end", "display_name": "销售额_end", "description": "销售额_end字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["1500", "2500", "3500"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:22:25.118329", "updated_at": "2025-08-04T15:22:25.118329"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["inf", "-inf"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:22:25.118329", "updated_at": "2025-08-04T15:22:25.118329"}}, "relationships": {}, "primary_keys": ["产品名称@#$", "销售额_start"], "created_at": "2025-08-04T15:22:25.118329", "updated_at": "2025-08-04T15:22:25.118329", "version": "1.0.0"}, "forced_test": {"table_name": "forced_test", "description": "forced_test数据表，包含2个字段和4条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:24:33.078061", "updated_at": "2025-08-04T15:24:33.078061"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:24:33.078061", "updated_at": "2025-08-04T15:24:33.078061"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:24:33.078061", "updated_at": "2025-08-04T15:24:33.078061", "version": "1.0.0"}, "test_bar": {"table_name": "test_bar", "description": "test_bar数据表，包含3个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone", "iPad", "MacBook"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:24:38.659474", "updated_at": "2025-08-04T15:24:38.659474"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:24:38.659474", "updated_at": "2025-08-04T15:24:38.659474"}, "日期": {"name": "日期", "display_name": "日期", "description": "日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-04T15:24:38.659474", "updated_at": "2025-08-04T15:24:38.659474"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:24:38.659474", "updated_at": "2025-08-04T15:24:38.659474", "version": "1.0.0"}, "test_line": {"table_name": "test_line", "description": "test_line数据表，包含3个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone", "iPad", "MacBook"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:24:41.512185", "updated_at": "2025-08-04T15:24:41.512185"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:24:41.512185", "updated_at": "2025-08-04T15:24:41.512185"}, "日期": {"name": "日期", "display_name": "日期", "description": "日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-04T15:24:41.512185", "updated_at": "2025-08-04T15:24:41.512185"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:24:41.512185", "updated_at": "2025-08-04T15:24:41.512185", "version": "1.0.0"}, "final_test": {"table_name": "final_test", "description": "final_test数据表，包含2个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:28:20.189826", "updated_at": "2025-08-04T15:28:20.189826"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:28:20.190823", "updated_at": "2025-08-04T15:28:20.190823"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:28:20.190823", "updated_at": "2025-08-04T15:28:20.190823", "version": "1.0.0"}, "ultimate_test": {"table_name": "ultimate_test", "description": "ultimate_test数据表，包含2个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:30:00.847126", "updated_at": "2025-08-04T15:30:00.847126"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:30:00.847126", "updated_at": "2025-08-04T15:30:00.847126"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:30:00.847126", "updated_at": "2025-08-04T15:30:00.847126", "version": "1.0.0"}, "final_ultimate_test": {"table_name": "final_ultimate_test", "description": "final_ultimate_test数据表，包含2个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:34:50.693116", "updated_at": "2025-08-04T15:34:50.693116"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:34:50.693116", "updated_at": "2025-08-04T15:34:50.693116"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:34:50.693116", "updated_at": "2025-08-04T15:34:50.693116", "version": "1.0.0"}, "error_fix_test": {"table_name": "error_fix_test", "description": "error_fix_test数据表，包含2个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:45:54.611135", "updated_at": "2025-08-04T15:45:54.611135"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:45:54.611135", "updated_at": "2025-08-04T15:45:54.611135"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:45:54.611135", "updated_at": "2025-08-04T15:45:54.611135", "version": "1.0.0"}, "chart_diagnosis": {"table_name": "chart_diagnosis", "description": "chart_diagnosis数据表，包含2个字段和4条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:51:20.396995", "updated_at": "2025-08-04T15:51:20.396995"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:51:20.396995", "updated_at": "2025-08-04T15:51:20.396995"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:51:20.396995", "updated_at": "2025-08-04T15:51:20.396995", "version": "1.0.0"}, "test_0": {"table_name": "test_0", "description": "test_0数据表，包含2个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone", "iPad", "MacBook"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:51:31.128835", "updated_at": "2025-08-04T15:51:31.128835"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:51:31.128835", "updated_at": "2025-08-04T15:51:31.128835"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:51:31.128835", "updated_at": "2025-08-04T15:51:31.128835", "version": "1.0.0"}, "test_1": {"table_name": "test_1", "description": "test_1数据表，包含2个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone", "iPad", "MacBook"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:51:33.876126", "updated_at": "2025-08-04T15:51:33.876126"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:51:33.876126", "updated_at": "2025-08-04T15:51:33.876126"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:51:33.876126", "updated_at": "2025-08-04T15:51:33.876126", "version": "1.0.0"}, "test_2": {"table_name": "test_2", "description": "test_2数据表，包含2个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone", "iPad", "MacBook"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:51:40.346551", "updated_at": "2025-08-04T15:51:40.346551"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:51:40.346551", "updated_at": "2025-08-04T15:51:40.346551"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:51:40.346551", "updated_at": "2025-08-04T15:51:40.346551", "version": "1.0.0"}, "comprehensive_test": {"table_name": "comprehensive_test", "description": "comprehensive_test数据表，包含2个字段和4条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:54:00.714540", "updated_at": "2025-08-04T15:54:00.714540"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:54:00.714540", "updated_at": "2025-08-04T15:54:00.714540"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:54:00.714540", "updated_at": "2025-08-04T15:54:00.714540", "version": "1.0.0"}, "scenario_0": {"table_name": "scenario_0", "description": "scenario_0数据表，包含2个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone", "iPad", "MacBook"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:54:05.415189", "updated_at": "2025-08-04T15:54:05.415189"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:54:05.415189", "updated_at": "2025-08-04T15:54:05.415189"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:54:05.415189", "updated_at": "2025-08-04T15:54:05.415189", "version": "1.0.0"}, "scenario_1": {"table_name": "scenario_1", "description": "scenario_1数据表，包含2个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone", "iPad", "MacBook"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:54:09.674988", "updated_at": "2025-08-04T15:54:09.674988"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:54:09.674988", "updated_at": "2025-08-04T15:54:09.674988"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:54:09.674988", "updated_at": "2025-08-04T15:54:09.674988", "version": "1.0.0"}, "scenario_2": {"table_name": "scenario_2", "description": "scenario_2数据表，包含2个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone", "iPad", "MacBook"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:54:18.535816", "updated_at": "2025-08-04T15:54:18.535816"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:54:18.535816", "updated_at": "2025-08-04T15:54:18.535816"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:54:18.535816", "updated_at": "2025-08-04T15:54:18.535816", "version": "1.0.0"}, "final_solution_test": {"table_name": "final_solution_test", "description": "final_solution_test数据表，包含2个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "iPad Air", "MacBook Pro"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-04T15:56:22.862922", "updated_at": "2025-08-04T15:56:22.862922"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "float64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["25500.0", "20200.0", "15000.0"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-04T15:56:22.862922", "updated_at": "2025-08-04T15:56:22.862922"}}, "relationships": {}, "primary_keys": ["产品名称", "销售额"], "created_at": "2025-08-04T15:56:22.862922", "updated_at": "2025-08-04T15:56:22.862922", "version": "1.0.0"}}