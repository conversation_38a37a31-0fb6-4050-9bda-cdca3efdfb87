@echo off
chcp 65001 >nul
echo 🤖 智能数据分析助手 - 完整版启动脚本
echo ========================================

echo 📦 检查虚拟环境...
if not exist venv (
    echo ❌ 未找到虚拟环境，请先创建虚拟环境
    echo 运行: python -m venv venv
    pause
    exit /b 1
)

echo ✅ 虚拟环境存在

echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

echo 📋 检查核心依赖...
venv\Scripts\python.exe -c "import streamlit, pandasai, yaml; print('✅ 核心依赖检查通过')" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 核心依赖缺失，正在安装...
    echo 安装PandasAI和相关依赖...
    venv\Scripts\pip.exe install pandasai==2.2.15 PyYAML openpyxl xlrd
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

echo ✅ 依赖检查完成

echo 🔑 检查API密钥...
if not exist .env (
    echo ⚠️ 未找到.env文件，正在创建示例文件...
    echo # 通义千问API密钥 > .env.example
    echo DASHSCOPE_API_KEY=your-dashscope-api-key-here >> .env.example
    echo ❌ 请先配置.env文件中的API密钥
    echo 参考.env.example文件
    pause
    exit /b 1
)

echo ✅ 配置文件存在

echo 📁 创建目录...
if not exist uploaded_files mkdir uploaded_files
if not exist chat_history mkdir chat_history
if not exist charts mkdir charts
if not exist temp mkdir temp

echo ✅ 目录创建完成

echo 🧪 测试核心模块导入...
venv\Scripts\python.exe -c "from perfect_tongyi_integration import TongyiQianwenLLM, analyze_data; print('✅ 核心模块导入成功')" >nul 2>&1
if errorlevel 1 (
    echo ❌ 核心模块导入失败，请检查项目文件
    pause
    exit /b 1
)

echo ✅ 核心模块测试通过

echo 🚀 启动完整版Streamlit应用...
echo 🌐 应用将在浏览器中打开: http://localhost:8501
echo 🎯 功能: AI智能数据分析 + 通义千问集成
echo 按 Ctrl+C 停止应用
echo ========================================

venv\Scripts\streamlit.exe run streamlit_app.py --server.port 8501

pause
