#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整修复验证测试
验证所有图表消失问题的修复是否生效
"""

import pandas as pd
import numpy as np
import re
from io import StringIO

def create_problematic_data():
    """创建包含所有问题的测试数据"""
    print("🧪 创建包含所有问题的测试数据")
    print("=" * 50)
    
    data = {
        '产品名称@#$': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro'],
        '销售额_start': [6999000, np.inf, 14999000, 1899000],
        '销售额_end': [7999000, 5599000, -np.inf, 2899000],
        '销售额': [7499000, np.nan, 15499000, 2399000],
        '销量': [1200, 800, 400, 1500]
    }
    
    df = pd.DataFrame(data)
    
    print("原始数据:")
    print(df)
    print(f"无穷大值数量: {np.isinf(df.select_dtypes(include=[np.number])).sum().sum()}")
    print(f"NaN值数量: {df.isnull().sum().sum()}")
    print()
    
    return df

def test_result_formatter_fix():
    """测试result_formatter.py中的修复"""
    print("🔧 测试result_formatter.py修复")
    print("=" * 50)
    
    # 模拟包含无穷大值的输出
    mock_output = """产品名称___
iPhone 15     7499000.0
iPad Air      inf
MacBook Pro   15499000.0
AirPods Pro   -inf
数据类型: float64
数据范围: 0.0 - 15499000.0
使用列: 产品列=产品名称___, 销售额列=销售额"""
    
    print("模拟输出（包含无穷大值）:")
    print(mock_output)
    print()
    
    # 测试数据解析逻辑
    lines = mock_output.strip().split('\n')
    data_dict = {}
    
    # 应用修复后的解析逻辑
    debug_patterns = [
        '数据范围:', '数据类型:', '数据形状:', '是否包含', 
        'print(', 'dtype:', 'Name:', '图表数据:', '使用列:',
        '数据验证:', '清理后数据:', '原始数据:', '测试数据:',
        '列名映射:', '修复检查:', '执行成功', '生成的代码'
    ]
    
    for line in lines:
        line = line.strip()
        
        # 跳过调试输出行
        if any(pattern in line for pattern in debug_patterns):
            continue
            
        if line and not line.startswith('Name:') and not line.startswith('dtype:'):
            # 传统序列格式：产品名称 销售额
            parts = line.rsplit(None, 1)
            if len(parts) == 2:
                try:
                    key = parts[0]
                    # 再次检查是否是调试输出
                    if any(pattern in key for pattern in debug_patterns):
                        continue
                    # 安全转换数值，处理无穷大值（修复Vega-Lite警告）
                    raw_value = float(parts[1])
                    if np.isinf(raw_value) or np.isnan(raw_value):
                        value = 0.0  # 将异常值转换为0
                    else:
                        value = raw_value
                    data_dict[key] = value
                except ValueError:
                    continue
    
    print("解析后的数据字典:")
    print(data_dict)
    
    # 检查是否成功处理了无穷大值
    has_inf_values = any(np.isinf(v) for v in data_dict.values())
    has_nan_values = any(np.isnan(v) for v in data_dict.values())
    
    if not has_inf_values and not has_nan_values:
        print("✅ result_formatter.py修复成功！")
        print("- ✅ 无穷大值已转换为0")
        print("- ✅ NaN值已转换为0")
        print("- ✅ 调试输出已过滤")
        return True
    else:
        print("❌ result_formatter.py修复失败")
        print(f"- 仍有无穷大值: {has_inf_values}")
        print(f"- 仍有NaN值: {has_nan_values}")
        return False

def test_streamlit_app_fix():
    """测试streamlit_app.py中的修复"""
    print("\n🔧 测试streamlit_app.py修复")
    print("=" * 50)
    
    # 模拟包含异常值的数据
    region_data = pd.DataFrame({
        '产品名称': ['iPhone', 'iPad', 'MacBook'],
        '销售额': [1000000, np.inf, -np.inf]
    })
    
    print("原始region_data:")
    print(region_data)
    print(f"无穷大值: {np.isinf(region_data['销售额']).sum()}")
    
    # 应用修复后的逻辑
    chart_data = region_data.set_index('产品名称')['销售额']
    
    # 深度数据清理（解决Vega-Lite渲染问题）
    chart_data = chart_data.replace([np.inf, -np.inf], np.nan)
    chart_data = chart_data.fillna(0)
    chart_data = pd.to_numeric(chart_data, errors='coerce').fillna(0)
    
    # 处理重复索引（避免Vega-Lite字段冲突）
    if chart_data.index.duplicated().any():
        chart_data = chart_data.groupby(chart_data.index).sum()
    
    print("\n清理后的chart_data:")
    print(chart_data)
    print(f"无穷大值: {np.isinf(chart_data).sum()}")
    print(f"NaN值: {chart_data.isnull().sum()}")
    
    # 检查清理效果
    if np.isinf(chart_data).sum() == 0 and chart_data.isnull().sum() == 0:
        print("✅ streamlit_app.py修复成功！")
        print("- ✅ 备用图表路径已修复")
        print("- ✅ 数据清理逻辑已应用")
        return True
    else:
        print("❌ streamlit_app.py修复失败")
        return False

def test_enhanced_integration_fix():
    """测试enhanced_tongyi_integration.py修复"""
    print("\n🔧 测试enhanced_tongyi_integration.py修复")
    print("=" * 50)
    
    try:
        from enhanced_tongyi_integration import EnhancedTongyiQianwenLLM
        
        # 创建测试数据
        df = create_problematic_data()
        
        # 创建LLM实例
        llm = EnhancedTongyiQianwenLLM()
        llm.set_current_data("test_data", df)
        
        # 测试图表生成
        query = "分析各产品销售额，生成柱状图"
        
        print(f"测试查询: {query}")
        
        # 生成代码
        code = llm.call(query, df.to_string())
        
        # 检查修复特征
        fix_features = {
            '智能列检测': '产品列=' in code and '销售额列=' in code,
            '数据清理': 'replace([np.inf, -np.inf], np.nan)' in code,
            '容器包装': 'with st.container():' in code,
            '错误处理': 'try:' in code and 'except' in code,
            '列名清理': 'df.columns = [re.sub' in code,
            '重复索引处理': 'index.duplicated()' in code
        }
        
        print("\n修复特征检查:")
        all_features = True
        for feature, present in fix_features.items():
            status = "✅" if present else "❌"
            print(f"{status} {feature}")
            if not present:
                all_features = False
        
        if all_features:
            print("\n✅ enhanced_tongyi_integration.py修复完整！")
            return True
        else:
            print("\n⚠️ enhanced_tongyi_integration.py部分修复缺失")
            return False
            
    except Exception as e:
        print(f"❌ enhanced_tongyi_integration.py测试失败: {e}")
        return False

def test_data_flow_integrity():
    """测试数据流完整性"""
    print("\n🔍 测试数据流完整性")
    print("=" * 50)
    
    # 创建包含所有问题的数据
    original_data = {
        '产品名称@#$': ['A', 'B', 'C'],
        '销售额_start': [1000, np.inf, 2000],
        '销售额_end': [1500, 2500, -np.inf],
        '销售额': [1200, np.nan, 2200]
    }
    
    df = pd.DataFrame(original_data)
    
    print("1. 原始数据:")
    print(df)
    print(f"   无穷大值: {np.isinf(df.select_dtypes(include=[np.number])).sum().sum()}")
    print(f"   NaN值: {df.isnull().sum().sum()}")
    
    # 模拟enhanced_tongyi_integration.py的数据清理
    print("\n2. 应用enhanced_tongyi_integration.py清理:")
    
    # 1. 处理数值列中的无穷大值和NaN
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    for col in numeric_cols:
        df[col] = df[col].replace([np.inf, -np.inf], np.nan)
        df[col] = df[col].fillna(0)
        df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)
    
    # 2. 清理列名中的特殊字符
    df.columns = [re.sub(r'[^\w\u4e00-\u9fff]', '_', str(col)) for col in df.columns]
    
    print(df)
    print(f"   无穷大值: {np.isinf(df.select_dtypes(include=[np.number])).sum().sum()}")
    print(f"   NaN值: {df.isnull().sum().sum()}")
    print(f"   列名: {list(df.columns)}")
    
    # 模拟输出到result_formatter.py
    print("\n3. 模拟输出到result_formatter.py:")
    
    # 生成模拟输出
    output_lines = []
    for _, row in df.iterrows():
        product_name = row['产品名称___']
        sales_value = row['销售额']
        output_lines.append(f"{product_name}     {sales_value}")
    
    mock_output = '\n'.join(output_lines)
    print(mock_output)
    
    # 模拟result_formatter.py的解析
    print("\n4. 应用result_formatter.py解析:")
    
    data_dict = {}
    for line in mock_output.split('\n'):
        parts = line.rsplit(None, 1)
        if len(parts) == 2:
            key = parts[0]
            raw_value = float(parts[1])
            if np.isinf(raw_value) or np.isnan(raw_value):
                value = 0.0
            else:
                value = raw_value
            data_dict[key] = value
    
    print(data_dict)
    
    # 检查最终结果
    final_has_inf = any(np.isinf(v) for v in data_dict.values())
    final_has_nan = any(np.isnan(v) for v in data_dict.values())
    
    if not final_has_inf and not final_has_nan:
        print("\n✅ 数据流完整性测试通过！")
        print("- ✅ 端到端数据清理成功")
        print("- ✅ 无异常值泄漏到前端")
        return True
    else:
        print("\n❌ 数据流完整性测试失败")
        print(f"- 仍有无穷大值: {final_has_inf}")
        print(f"- 仍有NaN值: {final_has_nan}")
        return False

def main():
    """主测试函数"""
    print("🎯 完整修复验证测试")
    print("=" * 60)
    
    # 执行所有测试
    test_results = []
    
    # 测试1：result_formatter.py修复
    result1 = test_result_formatter_fix()
    test_results.append(("result_formatter.py修复", result1))
    
    # 测试2：streamlit_app.py修复
    result2 = test_streamlit_app_fix()
    test_results.append(("streamlit_app.py修复", result2))
    
    # 测试3：enhanced_tongyi_integration.py修复
    result3 = test_enhanced_integration_fix()
    test_results.append(("enhanced_tongyi_integration.py修复", result3))
    
    # 测试4：数据流完整性
    result4 = test_data_flow_integrity()
    test_results.append(("数据流完整性", result4))
    
    # 生成总结
    print(f"\n📊 完整修复验证总结")
    print("=" * 30)
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 所有修复验证通过！")
        print("\n💡 现在应该完全解决:")
        print("1. ✅ 图表消失问题（多路径修复）")
        print("2. ✅ 调试输出显示问题（过滤修复）")
        print("3. ✅ Vega-Lite警告问题（数据清理）")
        print("4. ✅ 无穷大值渲染问题（端到端处理）")
        print("5. ✅ 特殊字符列名问题（智能清理）")
        
        print(f"\n🚀 建议:")
        print("1. 重启Streamlit应用以加载所有修复")
        print("2. 测试包含异常值的真实数据")
        print("3. 验证控制台不再有Vega-Lite警告")
        print("4. 确认图表持久显示不消失")
    else:
        print(f"\n⚠️ 部分修复验证失败，需要进一步调试")

if __name__ == "__main__":
    main()
