#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试结果显示修复
验证analyze_data函数是否正确返回结果
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_analyze_data_return():
    """测试analyze_data函数的返回值"""
    print("🧪 测试analyze_data函数返回值...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro'],
        '价格': [6999, 4599, 14999],
        '销量': [1200, 800, 400]
    })
    
    # 测试查询
    query = "显示数据的基本信息"
    
    print(f"📊 测试数据: {test_data.shape}")
    print(f"🔍 测试查询: {query}")
    print("-" * 50)
    
    # 调用analyze_data函数
    result = analyze_data(test_data, query)
    
    print("-" * 50)
    print("📋 返回结果分析:")
    print(f"类型: {type(result)}")
    
    if isinstance(result, dict):
        print("✅ 返回了字典结果")
        for key, value in result.items():
            print(f"  {key}: {value}")
        
        if result.get('success'):
            print("✅ 执行成功标志为True")
        else:
            print("❌ 执行成功标志为False")
            
        if result.get('output'):
            print("✅ 包含输出内容")
        else:
            print("⚠️ 没有输出内容")
            
    else:
        print(f"❌ 返回类型不正确: {type(result)}")
    
    return result

def main():
    """主测试函数"""
    print("🔧 测试结果显示修复")
    print("=" * 50)
    
    try:
        result = test_analyze_data_return()
        
        print("\n" + "=" * 50)
        if isinstance(result, dict) and result.get('success'):
            print("🎉 测试通过！analyze_data函数正确返回结果")
            print("✅ Streamlit前端应该能正常显示结果了")
        else:
            print("❌ 测试失败，需要进一步调试")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
