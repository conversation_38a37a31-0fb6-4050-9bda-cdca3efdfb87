#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试混合输出的格式化
"""

import pandas as pd
from result_formatter import EnhancedResultFormatter

def test_mixed_output_formatting():
    """测试混合输出格式化"""
    print("🧪 测试混合输出格式化")
    print("=" * 50)
    
    # 模拟实际的混合输出（describe + info）
    mixed_output = """                 价格          销量  ...         评分            成本
count     20.000000    20.00000  ...  20.000000     20.000000    
mean    6184.000000   685.00000  ...   4.595000   4880.000000    
std     3496.656298   332.09859  ...   0.160509   2860.364349    
min     1899.000000   200.00000  ...   4.300000   1200.000000    
25%     3874.000000   437.50000  ...   4.500000   3100.000000    
50%     5499.000000   625.00000  ...   4.600000   4400.000000    
75%     7249.000000   825.00000  ...   4.700000   5800.000000    
max    14999.000000  1500.00000  ...   4.900000  12000.000000    

[8 rows x 5 columns]
<class 'pandas.core.frame.DataFrame'>
RangeIndex: 20 entries, 0 to 19
Data columns (total 9 columns):
 #   Column  Non-Null Count  Dtype  
---  ------  --------------  -----  
 0   产品名称    20 non-null     object 
 1   类别      20 non-null     object 
 2   价格      20 non-null     int64  
 3   销量      20 non-null     int64  
 4   库存      20 non-null     int64  
 5   评分      20 non-null     float64
 6   上市日期    20 non-null     object 
 7   供应商     20 non-null     object 
 8   成本      20 non-null     int64  
dtypes: float64(1), int64(4), object(4)
memory usage: 1.5+ KB
None"""

    # 测试检测
    output_type = EnhancedResultFormatter._detect_output_type(mixed_output)
    print(f"🎯 检测类型: {output_type}")
    
    if output_type == 'dataframe_info':
        print("✅ 检测正确！")
        
        # 测试解析逻辑
        lines = mixed_output.split('\n')
        
        # 查找describe部分
        describe_start = -1
        for i, line in enumerate(lines):
            if 'count' in line and 'mean' in line:
                describe_start = i - 1 if i > 0 else i
                break
        
        if describe_start != -1:
            print(f"✅ 找到describe输出，开始行: {describe_start}")
            print(f"   内容: {lines[describe_start][:50]}...")
        
        # 查找info部分
        info_start = -1
        for i, line in enumerate(lines):
            if 'DataFrame' in line and 'RangeIndex' in line:
                info_start = i
                break
        
        if info_start != -1:
            print(f"✅ 找到info输出，开始行: {info_start}")
            print(f"   内容: {lines[info_start][:50]}...")
            
        # 测试列信息解析
        column_count = 0
        in_column_section = False
        for line in lines:
            if 'Column' in line and 'Non-Null Count' in line and 'Dtype' in line:
                in_column_section = True
                continue
            elif in_column_section and line.strip() and not line.startswith('dtypes:'):
                parts = line.strip().split()
                if len(parts) >= 4:
                    column_count += 1
            elif in_column_section and (line.startswith('dtypes:') or not line.strip()):
                break
        
        print(f"✅ 解析到 {column_count} 列信息")
        
    else:
        print(f"❌ 检测错误，期望 'dataframe_info'，实际 '{output_type}'")

if __name__ == "__main__":
    test_mixed_output_formatting()
