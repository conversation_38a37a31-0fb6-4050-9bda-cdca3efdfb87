{"销售相关": {"销售额": {"description": "产品或服务的销售金额", "business_meaning": "反映业务收入情况的核心指标", "data_type": "float", "constraints": {"min": 0}, "tags": ["财务", "收入", "KPI"]}, "销量": {"description": "产品销售的数量", "business_meaning": "反映产品市场接受度和需求量", "data_type": "int", "constraints": {"min": 0}, "tags": ["销售", "数量", "市场"]}, "销售员": {"description": "负责销售的员工姓名", "business_meaning": "用于分析个人销售业绩和团队管理", "data_type": "string", "tags": ["人员", "业绩", "管理"]}}, "产品相关": {"产品名称": {"description": "产品的具体名称或型号", "business_meaning": "用于产品分析和库存管理的标识", "data_type": "string", "tags": ["产品", "标识", "分类"]}, "价格": {"description": "产品的单价或售价", "business_meaning": "定价策略和利润分析的基础数据", "data_type": "float", "constraints": {"min": 0}, "tags": ["定价", "财务", "策略"]}, "库存": {"description": "产品的库存数量", "business_meaning": "库存管理和供应链优化的关键指标", "data_type": "int", "constraints": {"min": 0}, "tags": ["库存", "供应链", "管理"]}}, "地理相关": {"地区": {"description": "销售或业务发生的地理区域", "business_meaning": "用于区域分析和市场策略制定", "data_type": "string", "tags": ["地理", "区域", "市场"]}, "城市": {"description": "具体的城市名称", "business_meaning": "城市级别的市场分析和布局", "data_type": "string", "tags": ["地理", "城市", "市场"]}}, "时间相关": {"日期": {"description": "事件发生的具体日期", "business_meaning": "时间序列分析和趋势预测的基础", "data_type": "datetime", "tags": ["时间", "趋势", "分析"]}, "月份": {"description": "事件发生的月份", "business_meaning": "月度业绩分析和季节性趋势识别", "data_type": "string", "tags": ["时间", "月度", "季节性"]}}}