#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析发送给LLM的数据格式和内容
"""

import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_data_transmission_format():
    """分析数据传输格式"""
    print("🔍 分析发送给LLM的数据格式")
    print("=" * 60)
    
    # 加载测试数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 原始数据形状: {df.shape}")
    print(f"📋 原始数据列: {list(df.columns)}")
    print()
    
    # 1. 分析df.to_string()的输出
    print("1️⃣ df.to_string() 输出分析")
    print("-" * 40)
    
    data_string = df.to_string()
    print(f"📏 字符串长度: {len(data_string)} 字符")
    print(f"📄 行数: {data_string.count(chr(10)) + 1}")
    
    # 显示前500字符作为示例
    print(f"\n📝 前500字符示例:")
    print(data_string[:500])
    print("...")
    
    # 2. 分析df.head().to_string()的输出
    print(f"\n2️⃣ df.head().to_string() 输出分析")
    print("-" * 40)
    
    head_string = df.head().to_string()
    print(f"📏 字符串长度: {len(head_string)} 字符")
    print(f"📄 行数: {head_string.count(chr(10)) + 1}")
    
    print(f"\n📝 完整内容:")
    print(head_string)
    
    # 3. 分析数据摘要格式
    print(f"\n3️⃣ 数据摘要格式分析")
    print("-" * 40)
    
    summary_info = f"""
数据形状: {df.shape}
列名: {list(df.columns)}
数据类型:
{df.dtypes.to_string()}

前5行数据:
{df.head().to_string()}

数据统计:
{df.describe().to_string()}
"""
    
    print(f"📏 摘要长度: {len(summary_info)} 字符")
    print(f"📝 摘要内容:")
    print(summary_info)
    
    return {
        'full_data_length': len(data_string),
        'head_data_length': len(head_string),
        'summary_length': len(summary_info),
        'full_data': data_string,
        'head_data': head_string,
        'summary': summary_info
    }

def analyze_prompt_construction():
    """分析提示词构建过程"""
    print("\n🔧 分析提示词构建过程")
    print("=" * 60)
    
    # 模拟LLM调用
    df = pd.read_csv('uploaded_files/sales_data.csv')
    query = "分析各地区的产品销售总额"
    
    # 1. 标准版本的提示词
    print("1️⃣ 标准版本提示词")
    print("-" * 40)
    
    value = df.to_string()
    standard_prompt = f"""你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
{value}

用户指令: {query}

要求:
1. 只返回可执行的Python代码，确保所有代码块都有正确的缩进
2. 使用变量名df表示DataFrame，df已经存在，直接使用即可
3. 绝对不要包含创建DataFrame的代码，如pd.read_csv()、pd.read_excel()等
4. 绝对不要包含任何文件读取操作，数据已经在df变量中
5. 不要包含任何中文注释或解释

代码:"""
    
    print(f"📏 标准提示词长度: {len(standard_prompt)} 字符")
    print(f"📝 提示词结构:")
    print("- 系统角色定义")
    print("- 完整数据内容")
    print("- 用户查询")
    print("- 代码生成要求")
    
    # 2. 增强版本的提示词（模拟）
    print(f"\n2️⃣ 增强版本提示词（含元数据）")
    print("-" * 40)
    
    metadata_context = """
列元数据信息:
- 销售额: 产品或服务的销售金额，反映业务收入情况的核心指标
- 产品名称: 产品的具体名称或型号，用于产品分析和库存管理的标识
- 地区: 销售或业务发生的地理区域，用于区域分析和市场策略制定
- 销量: 产品销售的数量，反映产品市场接受度和需求量
- 销售员: 负责销售的员工姓名，用于分析个人销售业绩和团队管理
- 日期: 事件发生的具体日期，时间序列分析和趋势预测的基础
"""
    
    enhanced_prompt = f"""你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
{value}

用户指令: {query}

数据元数据信息:
{metadata_context}

请根据以上元数据信息更准确地理解列名含义和业务逻辑。

要求:
1. 只返回可执行的Python代码
2. 使用变量名df表示DataFrame，df已经存在，直接使用即可
3. 根据元数据信息理解列的业务含义，生成更准确的分析代码

代码:"""
    
    print(f"📏 增强提示词长度: {len(enhanced_prompt)} 字符")
    print(f"📝 提示词结构:")
    print("- 系统角色定义")
    print("- 完整数据内容")
    print("- 用户查询")
    print("- 元数据上下文")
    print("- 代码生成要求")
    
    return {
        'standard_prompt_length': len(standard_prompt),
        'enhanced_prompt_length': len(enhanced_prompt),
        'data_portion_length': len(value),
        'metadata_length': len(metadata_context)
    }

def analyze_data_privacy_implications():
    """分析数据隐私影响"""
    print("\n🔒 数据隐私影响分析")
    print("=" * 60)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    print("📋 发送给LLM的数据内容包括:")
    print("✅ 完整的原始数据记录")
    print("✅ 所有列的实际值")
    print("✅ 所有行的完整信息")
    print("✅ 数据的真实业务内容")
    
    print(f"\n📊 具体数据示例:")
    print(df.head(3).to_string())
    
    print(f"\n🔍 数据敏感性分析:")
    
    # 检查敏感信息
    sensitive_columns = []
    for col in df.columns:
        if any(keyword in col.lower() for keyword in ['姓名', '员工', '销售员', '客户', '电话', '地址']):
            sensitive_columns.append(col)
    
    if sensitive_columns:
        print(f"⚠️ 可能包含敏感信息的列: {sensitive_columns}")
    else:
        print("✅ 未检测到明显的敏感信息列")
    
    # 检查数据量
    total_records = len(df)
    print(f"📈 发送的记录数量: {total_records} 条")
    
    if total_records > 100:
        print("⚠️ 数据量较大，建议考虑数据脱敏或采样")
    else:
        print("✅ 数据量适中")
    
    print(f"\n💡 隐私保护建议:")
    print("1. 考虑对敏感列进行脱敏处理")
    print("2. 对于大数据集，可以使用采样数据")
    print("3. 可以只发送数据结构和统计信息，而非原始数据")
    print("4. 考虑本地化部署LLM以避免数据外传")

def suggest_data_optimization():
    """建议数据优化方案"""
    print("\n🚀 数据传输优化建议")
    print("=" * 60)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    print("📋 当前方案: df.to_string() - 发送完整原始数据")
    print(f"📏 数据大小: {len(df.to_string())} 字符")
    
    print(f"\n💡 优化方案建议:")
    
    # 方案1: 数据采样
    sample_df = df.head(10)
    sample_size = len(sample_df.to_string())
    print(f"1️⃣ 数据采样方案 (前10行)")
    print(f"   📏 大小: {sample_size} 字符 (减少 {((len(df.to_string()) - sample_size) / len(df.to_string()) * 100):.1f}%)")
    
    # 方案2: 结构化摘要
    structure_info = f"""
数据结构:
- 行数: {len(df)}
- 列数: {len(df.columns)}
- 列名: {list(df.columns)}
- 数据类型: {dict(df.dtypes)}

数据样本 (前3行):
{df.head(3).to_string()}

统计摘要:
{df.describe().to_string()}
"""
    
    print(f"2️⃣ 结构化摘要方案")
    print(f"   📏 大小: {len(structure_info)} 字符 (减少 {((len(df.to_string()) - len(structure_info)) / len(df.to_string()) * 100):.1f}%)")
    
    # 方案3: 智能采样
    print(f"3️⃣ 智能采样方案")
    print("   - 保留数据结构信息")
    print("   - 包含代表性样本")
    print("   - 添加统计摘要")
    print("   - 保护敏感信息")

if __name__ == "__main__":
    data_analysis = analyze_data_transmission_format()
    prompt_analysis = analyze_prompt_construction()
    analyze_data_privacy_implications()
    suggest_data_optimization()
