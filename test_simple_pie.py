#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试饼图回退机制
"""

def test_plotly_import():
    """测试Plotly导入"""
    print("🔍 测试Plotly导入")
    try:
        import plotly.express as px
        print("✅ Plotly可用")
        return True
    except ImportError as e:
        print(f"❌ Plotly不可用: {e}")
        return False

def test_matplotlib_fallback():
    """测试Matplotlib回退"""
    print("\n🔍 测试Matplotlib回退")
    try:
        import matplotlib.pyplot as plt
        print("✅ Matplotlib可用")
        
        # 创建简单饼图
        data = [8500, 6200, 3200, 2800, 450]
        labels = ['笔记本电脑', '台式电脑', '平板电脑', '手机', '耳机']
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc']
        
        plt.figure(figsize=(10, 8))
        wedges, texts, autotexts = plt.pie(data, labels=labels, autopct='%1.1f%%', 
                                          startangle=90, colors=colors, shadow=True)
        plt.title('销售金额分布', fontsize=16, fontweight='bold')
        plt.legend(wedges, labels, title="产品类别", 
                  loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))
        plt.axis('equal')
        plt.tight_layout()
        
        plt.savefig('simple_pie_test.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✅ Matplotlib饼图创建成功")
        return True
        
    except Exception as e:
        print(f"❌ Matplotlib失败: {e}")
        return False

def generate_fallback_code():
    """生成回退代码示例"""
    print("\n📝 生成的回退代码示例:")
    
    code = '''try:
    import plotly.express as px
    data = df.groupby('产品名称')['销售金额'].sum().reset_index()
    print("数据分布:")
    print(data)
    fig = px.pie(data, values='销售金额', names='产品名称', title='销售金额分布')
    fig.update_traces(textposition='inside', textinfo='percent+label')
    fig.update_layout(showlegend=True, legend=dict(orientation="v", yanchor="middle", y=0.5, xanchor="left", x=1.01))
    st.plotly_chart(fig, use_container_width=True)
except ImportError:
    import matplotlib.pyplot as plt
    data = df.groupby('产品名称')['销售金额'].sum()
    print("数据分布:")
    print(data)
    colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc', '#c2c2f0', '#ffb3e6']
    wedges, texts, autotexts = plt.pie(data.values, labels=data.index, autopct='%1.1f%%', 
                                      startangle=90, colors=colors, shadow=True)
    plt.title('销售金额分布', fontsize=16, fontweight='bold')
    plt.legend(wedges, data.index, title="产品类别", loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))
    plt.axis('equal')
    plt.tight_layout()
    save_chart()'''
    
    print(code)
    return code

if __name__ == "__main__":
    plotly_available = test_plotly_import()
    matplotlib_available = test_matplotlib_fallback()
    
    print(f"\n🎯 测试结果:")
    print(f"  Plotly: {'✅ 可用' if plotly_available else '❌ 不可用'}")
    print(f"  Matplotlib: {'✅ 可用' if matplotlib_available else '❌ 不可用'}")
    
    if plotly_available:
        print(f"\n🏆 推荐方案: Plotly原生饼图")
    elif matplotlib_available:
        print(f"\n🏆 推荐方案: Matplotlib优化饼图")
    else:
        print(f"\n❌ 两种方案都不可用")
    
    generate_fallback_code()
