#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图表生成修复效果
"""

import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from perfect_tongyi_integration import TongyiQianwenLLM, analyze_data

def test_chart_generation_fixes():
    """测试图表生成修复"""
    print("🧪 测试图表生成修复效果")
    print("=" * 50)
    
    # 加载测试数据
    try:
        df = pd.read_csv('uploaded_files/sales_data.csv')
        print(f"✅ 数据加载成功: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        print()
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 测试用例1：简单分组分析（之前成功的）
    print("1️⃣ 测试简单分组分析（对照组）")
    print("-" * 40)
    query1 = "分析各地区的产品销售总额"
    
    try:
        result1 = analyze_data(df, query1, "sales_data", use_metadata=True)
        print(f"✅ 查询1成功执行")
        if result1.get('error'):
            print(f"⚠️ 有警告: {result1['error']}")
        print()
    except Exception as e:
        print(f"❌ 查询1失败: {e}")
        print()
    
    # 测试用例2：多维度分析（之前失败的）
    print("2️⃣ 测试多维度分析（修复目标）")
    print("-" * 40)
    query2 = "请按照各地区各中各产品销售总额情况分析"
    
    try:
        result2 = analyze_data(df, query2, "sales_data", use_metadata=True)
        print(f"✅ 查询2成功执行")
        if result2.get('error'):
            print(f"⚠️ 有警告: {result2['error']}")
        else:
            print(f"🎉 查询2无错误！修复成功！")
        print()
    except Exception as e:
        print(f"❌ 查询2失败: {e}")
        print()
    
    # 测试用例3：直接测试代码清理功能
    print("3️⃣ 测试代码清理功能")
    print("-" * 40)
    
    # 模拟有缩进问题的代码
    problematic_code = """
import plotly.express as px
sales_by_region_product = df.groupby(['地区', '产品名称'])['销售额'].sum().reset_index()
print(sales_by_region_product)
for region in sales_by_region_product['地区'].unique():
region_data = sales_by_region_product[sales_by_region_product['地区'] == region]
fig = px.pie(region_data, values='销售额', names='产品名称', title=f'{region}地区各产品销售总额占比')
fig.update_traces(textposition='inside', textinfo='percent+label')
st.plotly_chart(fig, use_container_width=True)
"""
    
    llm = TongyiQianwenLLM()
    cleaned_code = llm.clean_code(problematic_code)
    
    print("原始代码（有缩进问题）:")
    print(problematic_code)
    print("\n清理后的代码:")
    print(cleaned_code)
    print()
    
    # 验证清理后的代码语法
    try:
        compile(cleaned_code, '<string>', 'exec')
        print("✅ 清理后的代码语法正确")
    except SyntaxError as e:
        print(f"❌ 清理后的代码仍有语法错误: {e}")
    
    print("\n🎯 修复总结:")
    print("-" * 40)
    print("1. ✅ 添加了缩进检测和修复逻辑")
    print("2. ✅ 改进了代码清理函数")
    print("3. ✅ 增强了AI提示词，强调缩进重要性")
    print("4. ✅ 提供了多维度分析的标准模板")

def test_specific_query():
    """测试具体的失败查询"""
    print("\n🎯 测试具体失败查询")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 直接使用LLM生成代码
    llm = TongyiQianwenLLM()
    query = "请按照各地区各中各产品销售总额情况分析"
    
    print(f"🔍 查询: {query}")
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 表名: sales_data")
    print("🎯 使用增强LLM（支持元数据）")
    
    # 生成代码
    code = llm.call(query, df.to_string())
    print(f"\n📝 生成的代码:")
    print(code)
    
    # 验证语法
    try:
        compile(code, '<string>', 'exec')
        print("\n✅ 代码语法检查通过")
    except SyntaxError as e:
        print(f"\n❌ 代码语法错误: {e}")
        print(f"错误位置: 第{e.lineno}行")
        if e.text:
            print(f"错误行内容: {e.text.strip()}")

if __name__ == "__main__":
    test_chart_generation_fixes()
    test_specific_query()
