#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试错误修复
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_error_fix():
    """测试错误修复"""
    print("🔧 测试错误修复")
    print("=" * 50)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro'],
        '销售额': [25500.0, 20200.0, 15000.0]
    })
    
    print("测试数据:")
    print(df)
    print()
    
    # 测试查询
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 30)
    
    try:
        result = analyze_data(df, query, "error_fix_test", use_metadata=True)
        
        if result.get('success'):
            print("✅ 分析成功！错误已修复")
            
            # 检查生成的代码
            code = result.get('code', '')
            
            print("生成的代码:")
            print(code)
            print()
            
            # 检查是否包含数据清理
            if '数据清理' in code and 'Vega-Lite渲染问题' in code:
                print("✅ 数据清理代码已添加")
            else:
                print("ℹ️ 使用原始代码")
            
            return True
            
        else:
            error_msg = result.get('error', '未知错误')
            print(f"❌ 分析失败: {error_msg}")
            
            if "cannot access local variable 'st'" in error_msg:
                print("⚠️ 仍然存在st变量作用域问题")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        
        if "cannot access local variable 'st'" in str(e):
            print("⚠️ 仍然存在st变量作用域问题")
        
        return False

if __name__ == "__main__":
    print("🚀 错误修复测试工具")
    print("=" * 50)
    
    # 执行测试
    success = test_error_fix()
    
    # 总结
    print(f"\n🎯 测试结果")
    print("=" * 20)
    
    if success:
        print("✅ 错误已修复！")
        print("现在可以重启Streamlit服务测试图表功能。")
        
        print(f"\n📋 重启步骤:")
        print("1. 停止Streamlit服务: Ctrl+C")
        print("2. 清理缓存: find . -name '__pycache__' -exec rm -rf {} +")
        print("3. 重启服务: streamlit run streamlit_app.py")
        print("4. 清理浏览器缓存: Ctrl+Shift+R")
        print("5. 测试查询: '请分析各产品销售额，用柱状图展示'")
        
    else:
        print("❌ 错误仍然存在")
        print("需要进一步调试和修复。")
    
    print(f"\n💡 说明:")
    print("我们简化了深度修复逻辑，避免复杂的代码替换，")
    print("改为在原代码前添加简单的数据清理，")
    print("这样可以避免变量作用域问题。")
