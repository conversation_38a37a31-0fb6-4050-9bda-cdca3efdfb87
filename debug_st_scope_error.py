#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 st 变量作用域错误
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def debug_st_scope_error():
    """调试 st 变量作用域错误"""
    print("🔍 调试 st 变量作用域错误")
    print("=" * 50)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500, 20200, 15000, 9700, 8000],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    # 测试查询
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 30)
    
    try:
        # 调用分析函数
        result = analyze_data(df, query, "sales_data", use_metadata=True)
        
        print("✅ 分析成功")
        print(f"代码: {result.get('code', 'N/A')}")
        print(f"输出: {result.get('output', 'N/A')}")
        print(f"错误: {result.get('error', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 打印详细的错误信息
        import traceback
        print("详细错误信息:")
        traceback.print_exc()

def test_code_execution_directly():
    """直接测试代码执行"""
    print("\n🔍 直接测试代码执行")
    print("=" * 30)
    
    # 模拟生成的代码
    test_code = """import streamlit as st
data = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)     
st.bar_chart(data)
print(data)"""

    print("原始代码:")
    print(test_code)
    print()
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro'],
        '销售额': [25500, 20200, 15000]
    })
    
    # 测试不同的执行环境配置
    test_scenarios = [
        {
            'name': '场景1: 包含st变量',
            'globals': {
                'df': df,
                'pd': pd,
                'st': create_mock_streamlit()
            }
        },
        {
            'name': '场景2: 清理导入后',
            'code': test_code.replace('import streamlit as st', '# import streamlit as st # 已在执行环境中提供'),
            'globals': {
                'df': df,
                'pd': pd,
                'st': create_mock_streamlit()
            }
        },
        {
            'name': '场景3: 完全移除导入',
            'code': '\n'.join([line for line in test_code.split('\n') if not line.strip().startswith('import streamlit')]),
            'globals': {
                'df': df,
                'pd': pd,
                'st': create_mock_streamlit()
            }
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📋 {scenario['name']}")
        print("-" * 20)
        
        code_to_exec = scenario.get('code', test_code)
        exec_globals = scenario['globals']
        
        print("执行的代码:")
        print(code_to_exec)
        print()
        
        try:
            exec(code_to_exec, exec_globals)
            print("✅ 执行成功")
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            print(f"错误类型: {type(e).__name__}")

def create_mock_streamlit():
    """创建模拟的Streamlit对象"""
    class MockStreamlit:
        def __getattr__(self, name):
            def mock_method(*args, **kwargs):
                print(f"[模拟] st.{name}() 被调用")
                return None
            return mock_method
    return MockStreamlit()

def test_variable_scope():
    """测试变量作用域问题"""
    print("\n🔍 测试变量作用域问题")
    print("=" * 30)
    
    # 测试可能导致作用域问题的代码模式
    problematic_patterns = [
        {
            'name': '模式1: 导入后立即使用',
            'code': '''import streamlit as st
st.bar_chart([1, 2, 3])'''
        },
        {
            'name': '模式2: 条件导入',
            'code': '''if True:
    import streamlit as st
st.bar_chart([1, 2, 3])'''
        },
        {
            'name': '模式3: 函数内导入',
            'code': '''def test_func():
    import streamlit as st
    st.bar_chart([1, 2, 3])
test_func()'''
        },
        {
            'name': '模式4: 重复导入',
            'code': '''import streamlit as st
import streamlit as st
st.bar_chart([1, 2, 3])'''
        }
    ]
    
    for pattern in problematic_patterns:
        print(f"\n📋 {pattern['name']}")
        print("-" * 15)
        print("代码:")
        print(pattern['code'])
        
        # 测试在有st变量的环境中执行
        exec_globals = {'st': create_mock_streamlit()}
        
        try:
            exec(pattern['code'], exec_globals)
            print("✅ 执行成功")
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            if 'cannot access local variable' in str(e):
                print("🚨 检测到作用域冲突！")

if __name__ == "__main__":
    test_variable_scope()
    test_code_execution_directly()
    debug_st_scope_error()
    
    print("\n" + "=" * 60)
    print("🎯 作用域错误分析总结")
    print("=" * 60)
    print("问题：cannot access local variable 'st' where it is not associated with a value")
    print()
    print("可能原因：")
    print("1. 代码中包含 'import streamlit as st'")
    print("2. 执行环境中已经提供了 st 变量")
    print("3. Python认为st是局部变量，但在赋值前就被使用")
    print("4. 代码清理过程可能不完整")
    print()
    print("解决方向：")
    print("1. 完全移除导入语句，而不是注释掉")
    print("2. 确保执行环境中的st变量正确设置")
    print("3. 检查代码生成逻辑，避免生成导入语句")
    print("4. 添加更严格的代码验证机制")
