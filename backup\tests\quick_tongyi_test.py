#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问快速测试 - 验证集成是否正常工作
"""

import os
import pandas as pd
from pandasai import SmartDataframe
from pandasai.llm import OpenAI
from dotenv import load_dotenv

def quick_test():
    """快速测试通义千问集成"""
    print("🚀 通义千问快速测试")
    print("=" * 30)
    
    # 1. 加载环境变量
    print("1. 加载环境变量...")
    load_dotenv()
    
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY环境变量")
        print("请确保在.env文件中配置了DASHSCOPE_API_KEY=your-api-key")
        return False
    
    print(f"✅ API密钥已加载: {api_key[:8]}...{api_key[-4:]}")
    
    # 2. 配置LLM
    print("\n2. 配置通义千问LLM...")
    try:
        llm = OpenAI(
            api_token=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            model="qwen-plus",
            temperature=0.1,
            max_tokens=1500
        )
        print("✅ LLM配置成功")
    except Exception as e:
        print(f"❌ LLM配置失败: {e}")
        return False
    
    # 3. 创建测试数据
    print("\n3. 创建测试数据...")
    data = {
        '姓名': ['张三', '李四', '王五', '赵六'],
        '年龄': [25, 30, 35, 28],
        '工资': [8000, 12000, 15000, 9500],
        '部门': ['技术', '销售', '技术', '人事']
    }
    
    df = pd.DataFrame(data)
    print("✅ 测试数据创建成功")
    print(df)
    
    # 4. 创建SmartDataframe
    print("\n4. 创建SmartDataframe...")
    try:
        smart_df = SmartDataframe(df, config={
            "llm": llm,
            "verbose": False,
            "conversational": False
        })
        print("✅ SmartDataframe创建成功")
    except Exception as e:
        print(f"❌ SmartDataframe创建失败: {e}")
        return False
    
    # 5. 执行测试查询
    print("\n5. 执行测试查询...")
    
    test_queries = [
        "平均工资是多少？",
        "哪个部门的人最多？",
        "工资最高的是谁？"
    ]
    
    success_count = 0
    for i, query in enumerate(test_queries, 1):
        print(f"\n   测试 {i}: {query}")
        try:
            result = smart_df.chat(query)
            print(f"   ✅ 结果: {result}")
            success_count += 1
        except Exception as e:
            print(f"   ❌ 失败: {e}")
    
    # 6. 测试结果
    print(f"\n6. 测试结果: {success_count}/{len(test_queries)} 成功")
    
    if success_count == len(test_queries):
        print("\n🎉 通义千问集成测试完全成功!")
        print("✅ 所有功能正常工作")
        print("✅ 支持中文自然语言查询")
        print("✅ 数据分析功能正常")
        return True
    else:
        print(f"\n⚠️  部分测试失败 ({success_count}/{len(test_queries)})")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "=" * 50)
    print("📖 通义千问使用指南")
    print("=" * 50)
    
    print("""
🔧 基本配置:
```python
from pandasai import SmartDataframe
from pandasai.llm import OpenAI
from dotenv import load_dotenv
import os

# 加载环境变量
load_dotenv()

# 配置通义千问
llm = OpenAI(
    api_token=os.getenv('DASHSCOPE_API_KEY'),
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    model="qwen-plus",
    temperature=0.1
)

# 使用
smart_df = SmartDataframe(df, config={"llm": llm})
result = smart_df.chat("你的中文问题")
```

🎯 支持的模型:
- qwen-turbo: 快速响应，适合简单查询
- qwen-plus: 平衡性能，推荐日常使用  
- qwen-max: 最强性能，适合复杂分析

💡 使用技巧:
1. 使用中文查询获得更好的理解效果
2. 设置 temperature=0.1 获得更稳定的结果
3. 根据查询复杂度选择合适的模型
4. 可以要求生成图表和可视化

🔗 相关文件:
- tongyi_qianwen_integration.py: 完整演示
- test_tongyi_connection.py: 详细测试
- pandasai_v2_examples.py: 更多示例
""")

def main():
    """主函数"""
    print("通义千问 + PandasAI V2 快速验证")
    print("=" * 40)
    
    # 执行快速测试
    success = quick_test()
    
    # 显示使用指南
    show_usage_guide()
    
    if success:
        print("\n🎉 集成验证成功! 现在可以开始使用通义千问进行数据分析了!")
        print("\n下一步:")
        print("1. 运行 tongyi_qianwen_integration.py 查看完整演示")
        print("2. 运行 test_tongyi_connection.py 进行详细测试")
        print("3. 查看 pandasai_v2_examples.py 了解更多用法")
    else:
        print("\n❌ 集成验证失败，请检查配置或网络连接")
        print("\n故障排除:")
        print("1. 确认.env文件中的DASHSCOPE_API_KEY正确")
        print("2. 检查网络连接是否正常")
        print("3. 运行 test_tongyi_connection.py 进行详细诊断")

if __name__ == "__main__":
    main()
