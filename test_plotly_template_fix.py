#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Plotly模板修复
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_plotly_template_enforcement():
    """测试Plotly模板强制使用"""
    print("🔧 测试Plotly模板强制使用")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据: {df.shape}")
    
    # 测试各种饼图查询
    pie_queries = [
        "分析2024年各地区的销售收入",
        "生成销售金额分布的饼图",
        "用饼图显示各产品的销售占比",
        "请为我创建一个销售数据的饼图"
    ]
    
    results = []
    
    for i, query in enumerate(pie_queries, 1):
        print(f"\n{i}️⃣ 测试查询: {query}")
        print("-" * 40)
        
        try:
            result = analyze_data(df, query, table_name="sales_data")
            
            if result and result.get('success'):
                code = result.get('code', '')
                output = result.get('output', '')
                uses_streamlit_native = result.get('uses_streamlit_native', False)
                uses_plotly_native = result.get('uses_plotly_native', False)
                has_chart = result.get('has_chart', False)
                
                print(f"📝 生成的代码:")
                print(code[:300] + "..." if len(code) > 300 else code)
                
                # 分析代码特征
                is_plotly_code = 'plotly' in code.lower() and 'px.pie' in code
                is_matplotlib_code = 'matplotlib' in code.lower() and 'plt.pie' in code
                has_st_plotly_chart = 'st.plotly_chart' in code
                has_save_chart = 'save_chart()' in code
                
                print(f"\n🔍 代码分析:")
                print(f"  📊 使用Plotly: {'✅' if is_plotly_code else '❌'}")
                print(f"  📊 使用Matplotlib: {'❌ (不应该)' if is_matplotlib_code else '✅'}")
                print(f"  🎨 包含st.plotly_chart: {'✅' if has_st_plotly_chart else '❌'}")
                print(f"  💾 包含save_chart(): {'❌ (不应该)' if has_save_chart else '✅'}")
                
                print(f"\n🏷️ 系统标志:")
                print(f"  uses_streamlit_native: {uses_streamlit_native}")
                print(f"  uses_plotly_native: {uses_plotly_native}")
                print(f"  has_chart: {has_chart}")
                
                print(f"\n📊 执行输出:")
                print(output if output.strip() else "(无输出)")
                
                # 评估模板遵循情况
                template_compliance = evaluate_template_compliance(
                    is_plotly_code, has_st_plotly_chart, not has_save_chart, 
                    uses_plotly_native, not has_chart
                )
                
                print(f"\n🎯 模板遵循度: {template_compliance}/5")
                
                if template_compliance >= 4:
                    print("🎉 完美！使用了Plotly原生模板")
                elif template_compliance >= 3:
                    print("✅ 良好，基本使用了正确模板")
                else:
                    print("❌ 仍在使用旧的matplotlib模板")
                
                results.append({
                    'query': query,
                    'is_plotly': is_plotly_code,
                    'is_matplotlib': is_matplotlib_code,
                    'has_st_plotly_chart': has_st_plotly_chart,
                    'template_compliance': template_compliance,
                    'uses_plotly_native': uses_plotly_native,
                    'has_chart': has_chart
                })
                
            else:
                print("❌ 查询失败")
                if result:
                    print(f"错误: {result.get('error', '未知错误')}")
                
                results.append({
                    'query': query,
                    'is_plotly': False,
                    'is_matplotlib': False,
                    'has_st_plotly_chart': False,
                    'template_compliance': 0,
                    'uses_plotly_native': False,
                    'has_chart': False
                })
                
        except Exception as e:
            print(f"❌ 查询异常: {e}")
            results.append({
                'query': query,
                'is_plotly': False,
                'is_matplotlib': False,
                'has_st_plotly_chart': False,
                'template_compliance': 0,
                'uses_plotly_native': False,
                'has_chart': False
            })
    
    return results

def evaluate_template_compliance(is_plotly, has_st_plotly_chart, no_save_chart, uses_plotly_native, no_has_chart):
    """评估模板遵循情况"""
    score = 0
    
    if is_plotly:
        score += 1  # 使用了Plotly
    
    if has_st_plotly_chart:
        score += 1  # 使用了st.plotly_chart
    
    if no_save_chart:
        score += 1  # 没有多余的save_chart()
    
    if uses_plotly_native:
        score += 1  # 系统正确识别为Plotly原生
    
    if no_has_chart:
        score += 1  # 系统正确设置has_chart=False
    
    return score

def analyze_template_effectiveness(results):
    """分析模板有效性"""
    print(f"\n📊 模板有效性分析")
    print("=" * 50)
    
    total_queries = len(results)
    plotly_usage = sum(1 for r in results if r['is_plotly'])
    matplotlib_usage = sum(1 for r in results if r['is_matplotlib'])
    st_plotly_chart_usage = sum(1 for r in results if r['has_st_plotly_chart'])
    
    avg_compliance = sum(r['template_compliance'] for r in results) / total_queries if total_queries > 0 else 0
    
    print(f"📈 统计结果:")
    print(f"  总查询数: {total_queries}")
    print(f"  使用Plotly: {plotly_usage} ({plotly_usage/total_queries*100:.1f}%)")
    print(f"  使用Matplotlib: {matplotlib_usage} ({matplotlib_usage/total_queries*100:.1f}%)")
    print(f"  使用st.plotly_chart: {st_plotly_chart_usage} ({st_plotly_chart_usage/total_queries*100:.1f}%)")
    print(f"  平均模板遵循度: {avg_compliance:.1f}/5")
    
    # 判断修复效果
    print(f"\n🎯 修复效果评估:")
    
    if plotly_usage >= total_queries * 0.8:  # 80%以上使用Plotly
        print("🎉 优秀！大部分查询使用了Plotly模板")
    elif plotly_usage >= total_queries * 0.5:  # 50%以上使用Plotly
        print("✅ 良好！部分查询使用了Plotly模板")
    else:
        print("❌ 需要改进！仍然主要使用matplotlib模板")
    
    if avg_compliance >= 4:
        print("🎉 模板遵循度优秀！")
    elif avg_compliance >= 3:
        print("✅ 模板遵循度良好！")
    else:
        print("❌ 模板遵循度需要改进！")
    
    # 显示逻辑检查
    correct_display_logic = sum(1 for r in results if r['uses_plotly_native'] and not r['has_chart'])
    display_logic_rate = (correct_display_logic / total_queries) * 100 if total_queries > 0 else 0
    
    print(f"🎨 显示逻辑正确率: {display_logic_rate:.1f}%")
    
    return plotly_usage >= total_queries * 0.8 and avg_compliance >= 4

if __name__ == "__main__":
    results = test_plotly_template_enforcement()
    template_effective = analyze_template_effectiveness(results)
    
    print(f"\n🎯 最终评估:")
    if template_effective:
        print("🎉 Plotly模板修复成功！")
        print("✅ 饼图现在会正确使用Plotly原生显示")
        print("✅ 不会再跳转到'AI生成的数据可视化图表'")
    else:
        print("⚠️ Plotly模板仍需进一步调整")
        print("❌ 可能仍会出现matplotlib显示问题")
