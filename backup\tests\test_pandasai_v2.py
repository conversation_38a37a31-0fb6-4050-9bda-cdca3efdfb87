#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PandasAI V2 测试和配置文件
"""

import os
import pandas as pd
from pandasai import SmartDataframe
from pandasai.llm import OpenAI

def test_pandasai_v2_basic():
    """测试PandasAI V2基本功能"""
    print("=== PandasAI V2 基本功能测试 ===")
    
    # 创建测试数据
    data = {
        'country': ['United States', 'United Kingdom', 'France', 'Germany', 'Italy', 'Spain', 'Canada', 'Australia', 'Japan', 'China'],
        'gdp': [19294482071552, 2891615567872, 2411255037952, 3435817336832, 1745433788416, 1181205135360, 1607402389504, 1490967855104, 4380756541440, 14631844184064],
        'happiness_index': [6.94, 7.16, 6.66, 7.07, 6.38, 6.4, 7.23, 7.22, 5.87, 5.12]
    }
    
    df = pd.DataFrame(data)
    print("测试数据创建成功:")
    print(df.head())
    print()
    
    return df

def setup_openai_llm():
    """配置OpenAI LLM"""
    print("=== 配置OpenAI LLM ===")
    
    # 检查API密钥
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("警告: 未找到OPENAI_API_KEY环境变量")
        print("请设置您的OpenAI API密钥:")
        print("方法1: 设置环境变量 OPENAI_API_KEY")
        print("方法2: 在代码中直接指定 api_token 参数")
        print()
        
        # 提供手动输入选项
        manual_key = input("请输入您的OpenAI API密钥 (或按Enter跳过): ").strip()
        if manual_key:
            api_key = manual_key
        else:
            print("跳过OpenAI配置，将使用模拟模式")
            return None
    
    try:
        # 配置OpenAI LLM
        llm = OpenAI(
            api_token=api_key,
            model="gpt-3.5-turbo",
            temperature=0.1,
            max_tokens=1000
        )
        print("OpenAI LLM配置成功!")
        return llm
    except Exception as e:
        print(f"OpenAI LLM配置失败: {e}")
        return None

def setup_tongyi_qianwen_llm():
    """配置通义千问LLM (使用OpenAI兼容接口)"""
    print("=== 配置通义千问LLM ===")
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("警告: 未找到DASHSCOPE_API_KEY环境变量")
        manual_key = input("请输入您的DashScope API密钥 (或按Enter跳过): ").strip()
        if manual_key:
            api_key = manual_key
        else:
            print("跳过通义千问配置")
            return None
    
    try:
        # 使用OpenAI类配置通义千问
        llm = OpenAI(
            api_token=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            model="qwen-plus",
            temperature=0.1,
            max_tokens=1000
        )
        print("通义千问LLM配置成功!")
        return llm
    except Exception as e:
        print(f"通义千问LLM配置失败: {e}")
        return None

def test_with_llm(df, llm, llm_name):
    """使用指定LLM测试PandasAI功能"""
    print(f"=== 使用{llm_name}测试PandasAI ===")
    
    try:
        # 创建SmartDataframe
        smart_df = SmartDataframe(df, config={"llm": llm, "verbose": True})
        
        # 测试查询
        print("执行查询: '哪5个国家最幸福?'")
        result = smart_df.chat('Which are the 5 happiest countries?')
        print("查询结果:")
        print(result)
        print()
        
        # 测试数据分析
        print("执行查询: '计算平均幸福指数'")
        result2 = smart_df.chat('What is the average happiness index?')
        print("查询结果:")
        print(result2)
        print()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_without_llm(df):
    """不使用LLM的基本测试"""
    print("=== 基本功能测试 (无LLM) ===")

    try:
        # 设置一个虚拟的API密钥来避免错误
        os.environ['PANDASAI_API_KEY'] = 'dummy-key-for-testing'

        # 创建SmartDataframe (不指定LLM)
        smart_df = SmartDataframe(df)
        print("SmartDataframe创建成功!")

        # 显示基本信息
        print("数据形状:", smart_df.shape)
        print("列名:", list(smart_df.columns))
        print()

        return True

    except Exception as e:
        print(f"基本测试失败: {e}")
        print("注意: PandasAI V2需要API密钥才能正常工作")
        return False

def main():
    """主函数"""
    print("PandasAI V2 安装验证和配置测试")
    print("=" * 50)
    
    # 检查版本
    try:
        import pandasai
        from pandasai.__version__ import __version__
        print(f"PandasAI版本: {__version__}")
        print()
    except:
        try:
            import pandasai
            print(f"PandasAI版本: {pandasai.__version__}")
            print()
        except:
            print("无法获取PandasAI版本信息")
    
    # 创建测试数据
    df = test_pandasai_v2_basic()
    
    # 基本功能测试
    basic_success = test_without_llm(df)
    
    if basic_success:
        print("✅ PandasAI V2基本功能正常!")
    else:
        print("❌ PandasAI V2基本功能异常!")
        return
    
    # LLM配置和测试
    print("\n" + "=" * 50)
    print("LLM配置选项:")
    print("1. OpenAI (需要OpenAI API密钥)")
    print("2. 通义千问 (需要DashScope API密钥)")
    print("3. 跳过LLM测试")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == "1":
        llm = setup_openai_llm()
        if llm:
            test_with_llm(df, llm, "OpenAI")
    elif choice == "2":
        llm = setup_tongyi_qianwen_llm()
        if llm:
            test_with_llm(df, llm, "通义千问")
    else:
        print("跳过LLM测试")
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("\n使用说明:")
    print("1. PandasAI V2使用SmartDataframe类")
    print("2. 通过config参数配置LLM")
    print("3. 使用chat()方法进行自然语言查询")

if __name__ == "__main__":
    main()
