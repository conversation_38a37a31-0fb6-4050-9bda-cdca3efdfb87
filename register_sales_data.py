#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新注册sales_data表格
"""

import pandas as pd
from metadata_manager import metadata_manager

def register_sales_data():
    """重新注册sales_data表格"""
    print("📊 重新注册sales_data表格")
    print("=" * 50)
    
    try:
        # 加载数据
        df = pd.read_csv("uploaded_files/sales_data.csv")
        print(f"✅ 数据加载成功: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        
        # 注册表格
        table_metadata = metadata_manager.register_table(
            "sales_data",
            df,
            description="销售数据表，包含产品销售的详细记录",
            business_domain="销售管理"
        )
        
        print("✅ 表格注册成功")
        print(f"📊 表格名称: {table_metadata.table_name}")
        print(f"📋 列数量: {len(table_metadata.columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 注册失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_registration():
    """验证注册结果"""
    print("\n🔍 验证注册结果")
    print("=" * 50)
    
    # 检查表格是否存在
    tables = metadata_manager.get_all_tables()
    print(f"📊 当前表格数量: {len(tables)}")
    print(f"📊 表格列表: {tables}")
    
    if "sales_data" in tables:
        print("✅ sales_data表格已成功注册")
        
        # 获取表格元数据
        table_metadata = metadata_manager.get_table_metadata("sales_data")
        if table_metadata:
            print(f"📋 表格描述: {table_metadata.description}")
            print(f"🏢 业务领域: {table_metadata.business_domain}")
            print(f"📊 列数量: {len(table_metadata.columns)}")
            print("📋 列信息:")
            for col_name, col_metadata in table_metadata.columns.items():
                print(f"  - {col_name}: {col_metadata.data_type}")
        
        return True
    else:
        print("❌ sales_data表格注册失败")
        return False

def main():
    """主函数"""
    print("📊 重新注册sales_data表格")
    print("=" * 60)
    
    # 注册表格
    success = register_sales_data()
    
    if success:
        # 验证注册结果
        verify_registration()
        print("\n🎉 sales_data表格注册完成！")
    else:
        print("\n❌ sales_data表格注册失败")

if __name__ == "__main__":
    main()
