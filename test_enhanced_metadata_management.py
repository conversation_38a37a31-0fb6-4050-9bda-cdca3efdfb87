#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的元数据管理功能
验证新实现的元数据刷新、清理和同步功能
"""

import sys
import pandas as pd
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

try:
    from metadata_manager import metadata_manager
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def create_test_environment():
    """创建测试环境"""
    print("\n🏗️ 创建测试环境")
    print("=" * 50)
    
    # 创建测试目录
    test_dir = Path("test_data")
    test_dir.mkdir(exist_ok=True)
    
    # 创建测试数据文件
    test_files = {
        "products.csv": pd.DataFrame({
            "产品ID": ["P001", "P002", "P003"],
            "产品名称": ["笔记本电脑", "台式电脑", "平板电脑"],
            "价格": [8999, 5999, 3999],
            "库存": [50, 30, 80]
        }),
        "customers.xlsx": pd.DataFrame({
            "客户ID": ["C001", "C002", "C003"],
            "客户名称": ["张三公司", "李四企业", "王五集团"],
            "联系电话": ["13800138001", "13800138002", "13800138003"],
            "地区": ["北京", "上海", "广州"]
        }),
        "orders.json": pd.DataFrame({
            "订单ID": ["O001", "O002", "O003"],
            "客户ID": ["C001", "C002", "C001"],
            "产品ID": ["P001", "P002", "P003"],
            "数量": [2, 1, 3],
            "订单日期": ["2024-01-15", "2024-01-16", "2024-01-17"]
        })
    }
    
    # 保存测试文件
    for filename, df in test_files.items():
        file_path = test_dir / filename
        
        if filename.endswith('.csv'):
            df.to_csv(file_path, index=False, encoding='utf-8')
        elif filename.endswith('.xlsx'):
            df.to_excel(file_path, index=False)
        elif filename.endswith('.json'):
            df.to_json(file_path, orient='records', force_ascii=False, indent=2)
        
        print(f"✅ 创建测试文件: {filename}")
    
    return test_dir, test_files

def test_current_metadata_state():
    """测试当前元数据状态"""
    print("\n📊 当前元数据状态")
    print("=" * 50)
    
    all_tables = metadata_manager.get_all_tables()
    print(f"📋 当前表格数量: {len(all_tables)}")
    
    if all_tables:
        print("📋 当前表格列表:")
        for table in all_tables:
            metadata = metadata_manager.get_table_metadata(table)
            if metadata:
                print(f"  - {table} (业务领域: {metadata.business_domain}, 列数: {len(metadata.columns)})")
    else:
        print("📋 当前没有注册的表格")
    
    return all_tables

def test_duplicate_cleanup():
    """测试重复元数据清理"""
    print("\n🧹 测试重复元数据清理")
    print("=" * 50)
    
    # 执行重复清理
    result = metadata_manager.cleanup_duplicate_metadata()
    
    print(f"📊 清理结果:")
    print(f"  发现重复项: {result['duplicates_found']}")
    print(f"  删除重复项: {result['duplicates_removed']}")
    
    if result['duplicate_pairs']:
        print(f"  重复对:")
        for removed, kept in result['duplicate_pairs'].items():
            print(f"    删除: {removed} -> 保留: {kept}")
    
    return result

def test_filesystem_sync():
    """测试文件系统同步"""
    print("\n🔄 测试文件系统同步")
    print("=" * 50)
    
    # 创建测试环境
    test_dir, test_files = create_test_environment()
    
    try:
        # 执行文件系统同步
        result = metadata_manager.sync_with_filesystem(
            data_directory=test_dir,
            demo_file="demo_data.csv"
        )
        
        print(f"📊 同步结果:")
        print(f"  重复清理: {result['duplicate_cleanup']}")
        print(f"  目录刷新: {result['directory_refresh']}")
        print(f"  演示数据: {result['demo_sync']}")
        print(f"  同步后总表格数: {result['total_tables_after_sync']}")
        
        return result
        
    finally:
        # 清理测试文件
        import shutil
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"🧹 已清理测试目录: {test_dir}")

def test_metadata_refresh():
    """测试元数据刷新功能"""
    print("\n🔄 测试元数据刷新功能")
    print("=" * 50)
    
    # 创建测试环境
    test_dir, test_files = create_test_environment()
    
    try:
        # 执行元数据刷新
        result = metadata_manager.refresh_metadata(test_dir)
        
        print(f"📊 刷新结果:")
        print(f"  扫描文件数: {result['scanned_files']}")
        print(f"  清理孤立元数据: {result['orphaned_cleaned']}")
        print(f"  更新表格数: {result['tables_updated']}")
        print(f"  注册新文件数: {result['new_files_registered']}")
        print(f"  刷新后总表格数: {result['total_tables']}")
        
        if result['orphaned_tables']:
            print(f"  孤立表格: {result['orphaned_tables']}")
        
        if result['updated_tables']:
            print(f"  更新表格: {result['updated_tables']}")
        
        if result['new_tables']:
            print(f"  新注册表格: {result['new_tables']}")
        
        return result
        
    finally:
        # 清理测试文件
        import shutil
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"🧹 已清理测试目录: {test_dir}")

def test_validation_and_consistency():
    """测试验证和一致性检查"""
    print("\n🔍 测试验证和一致性检查")
    print("=" * 50)
    
    all_tables = metadata_manager.get_all_tables()
    
    for table_name in all_tables:
        print(f"\n验证表格: {table_name}")
        
        # 验证元数据
        validation_result = metadata_manager.validate_metadata(table_name)
        
        if validation_result['errors']:
            print(f"  ❌ 错误: {validation_result['errors']}")
        
        if validation_result['warnings']:
            print(f"  ⚠️ 警告: {validation_result['warnings']}")
        
        if validation_result['suggestions']:
            print(f"  💡 建议: {validation_result['suggestions']}")
        
        if not validation_result['errors'] and not validation_result['warnings']:
            print(f"  ✅ 验证通过")

def generate_test_report():
    """生成测试报告"""
    print("\n📋 生成测试报告")
    print("=" * 50)
    
    report = {
        'test_time': datetime.now().isoformat(),
        'total_tables': len(metadata_manager.get_all_tables()),
        'tables': {}
    }
    
    for table_name in metadata_manager.get_all_tables():
        metadata = metadata_manager.get_table_metadata(table_name)
        if metadata:
            report['tables'][table_name] = {
                'description': metadata.description,
                'business_domain': metadata.business_domain,
                'columns_count': len(metadata.columns),
                'created_at': metadata.created_at,
                'updated_at': metadata.updated_at,
                'version': metadata.version
            }
    
    # 保存报告
    report_file = Path("enhanced_metadata_test_report.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 测试报告已保存: {report_file}")
    return report

def main():
    """主测试函数"""
    print("🚀 开始测试增强的元数据管理功能")
    print("=" * 60)
    
    try:
        # 1. 测试当前状态
        current_state = test_current_metadata_state()
        
        # 2. 测试重复清理
        duplicate_result = test_duplicate_cleanup()
        
        # 3. 测试元数据刷新
        refresh_result = test_metadata_refresh()
        
        # 4. 测试文件系统同步
        sync_result = test_filesystem_sync()
        
        # 5. 测试验证和一致性
        test_validation_and_consistency()
        
        # 6. 生成测试报告
        report = generate_test_report()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试完成！")
        
        print(f"\n📊 测试总结:")
        print(f"  初始表格数: {len(current_state)}")
        print(f"  重复清理: {duplicate_result['duplicates_removed']} 个")
        print(f"  最终表格数: {report['total_tables']}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎯 测试成功完成！")
        print(f"增强的元数据管理功能已验证正常工作。")
    else:
        print(f"\n⚠️ 测试过程中出现问题，请检查错误信息。")
