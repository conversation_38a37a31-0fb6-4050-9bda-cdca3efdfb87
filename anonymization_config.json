{"anonymization_level": "medium", "preserve_data_patterns": true, "preserve_relationships": true, "company_name_strategy": "generic", "personal_data_strategy": "hash", "financial_data_strategy": "scale", "date_strategy": "shift", "geographic_strategy": "generic", "description": {"anonymization_level": "脱敏级别: low(低), medium(中), high(高)", "preserve_data_patterns": "是否保持数据模式", "preserve_relationships": "是否保持数据关系", "company_name_strategy": "公司名称策略: generic(通用), hash(哈希), fake(假名)", "personal_data_strategy": "个人数据策略: hash(哈希), fake(假名), remove(移除)", "financial_data_strategy": "财务数据策略: scale(缩放), range(范围), remove(移除)", "date_strategy": "日期策略: shift(偏移), remove(移除), keep(保持)", "geographic_strategy": "地理策略: generic(通用), remove(移除), keep(保持)"}}