#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动柱状图逻辑修复
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data
from result_formatter import EnhancedResultFormatter

def test_auto_chart_logic():
    """测试自动柱状图逻辑"""
    print("🔧 测试自动柱状图逻辑修复")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据: {df.shape}")
    
    # 测试Plotly饼图
    query = "请为我分析2024年各产品销售额，并用饼图展示"
    print(f"🔍 查询: {query}")
    
    try:
        result = analyze_data(df, query, table_name="sales_data")
        
        if result and result.get('success'):
            print("✅ 查询成功")
            
            code = result.get('code', '')
            output = result.get('output', '')
            has_chart = result.get('has_chart', False)
            uses_plotly_native = result.get('uses_plotly_native', False)
            
            print(f"\n📝 生成的代码:")
            print(code[:200] + "..." if len(code) > 200 else code)
            
            print(f"\n🔍 图表标志分析:")
            print(f"  has_chart: {has_chart}")
            print(f"  uses_plotly_native: {uses_plotly_native}")
            
            # 模拟结果格式化器的逻辑
            print(f"\n🎨 自动柱状图逻辑模拟:")
            
            # 检测输出类型
            output_type = EnhancedResultFormatter._detect_output_type(output)
            print(f"  输出类型: {output_type}")
            
            if output_type == 'series_data':
                print("  → 检测到序列数据，会进入自动图表逻辑")
                
                # 模拟自动柱状图条件检查
                data_length = 5  # 我们的数据有5个产品
                condition1 = data_length <= 15
                condition2 = not has_chart
                condition3 = not uses_plotly_native
                
                print(f"  → 数据长度 <= 15: {condition1}")
                print(f"  → not has_chart: {condition2}")
                print(f"  → not uses_plotly_native: {condition3}")
                
                will_show_auto_chart = condition1 and condition2 and condition3
                print(f"  → 会显示自动柱状图: {will_show_auto_chart}")
                
                if not will_show_auto_chart:
                    print("  🎉 修复成功！不会显示自动柱状图")
                else:
                    print("  ❌ 仍会显示自动柱状图，需要进一步修复")
            else:
                print("  → 非序列数据，不会触发自动图表逻辑")
            
            print(f"\n📊 最终用户看到的内容:")
            if uses_plotly_native and output_type == 'series_data':
                if not has_chart and uses_plotly_native:
                    print("  1. 📊 数据分析结果表格")
                    print("  2. 🥧 Plotly原生饼图（唯一图表）")
                    print("  3. ✅ 分析完成状态")
                    print("  🎉 完美！用户只看到一个饼图")
                else:
                    print("  ⚠️ 可能仍有重复显示问题")
            else:
                print("  📊 其他类型的分析结果")
                
        else:
            print("❌ 查询失败")
            if result:
                print(f"错误: {result.get('error', '未知错误')}")
                
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        import traceback
        traceback.print_exc()

def test_different_scenarios():
    """测试不同场景"""
    print(f"\n🧪 测试不同场景")
    print("=" * 50)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    scenarios = [
        ("Plotly饼图", "生成销售金额分布的饼图", "应该只显示Plotly饼图"),
        ("Matplotlib柱状图", "生成销售金额的柱状图", "应该显示matplotlib图表"),
        ("数据分析", "分析各产品的销售情况", "可能显示自动柱状图")
    ]
    
    for scenario_name, query, expected in scenarios:
        print(f"\n📊 {scenario_name}场景:")
        print(f"  查询: {query}")
        print(f"  期望: {expected}")
        
        try:
            result = analyze_data(df, query, table_name="sales_data")
            
            if result and result.get('success'):
                has_chart = result.get('has_chart', False)
                uses_plotly_native = result.get('uses_plotly_native', False)
                output = result.get('output', '')
                output_type = EnhancedResultFormatter._detect_output_type(output)
                
                print(f"  结果:")
                print(f"    has_chart: {has_chart}")
                print(f"    uses_plotly_native: {uses_plotly_native}")
                print(f"    output_type: {output_type}")
                
                # 预测自动柱状图行为
                will_auto_chart = (output_type == 'series_data' and 
                                 not has_chart and 
                                 not uses_plotly_native)
                
                print(f"    会显示自动柱状图: {will_auto_chart}")
                
                if scenario_name == "Plotly饼图":
                    if uses_plotly_native and not will_auto_chart:
                        print("    ✅ 完美！只显示Plotly饼图")
                    else:
                        print("    ❌ 可能有重复显示")
                elif scenario_name == "Matplotlib柱状图":
                    if has_chart and not uses_plotly_native:
                        print("    ✅ 正确显示matplotlib图表")
                    else:
                        print("    ⚠️ 显示逻辑可能有问题")
                else:
                    print("    📊 数据分析场景")
                    
            else:
                print("  ❌ 查询失败")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")

def summarize_final_fix():
    """总结最终修复"""
    print(f"\n🎯 最终修复总结")
    print("=" * 50)
    
    print("🔧 修复的问题:")
    print("  ❌ 问题1: Plotly饼图被'AI生成图表'覆盖 → ✅ 已修复")
    print("  ❌ 问题2: Plotly饼图被自动柱状图替换 → ✅ 已修复")
    
    print("\n🎨 最终显示逻辑:")
    print("  1. 检测图表类型:")
    print("     - uses_plotly_native = 'st.plotly_chart' in code")
    print("     - has_chart = 是否有matplotlib图表")
    print()
    print("  2. 自动柱状图条件:")
    print("     - 数据长度 <= 15")
    print("     - AND not has_chart")
    print("     - AND not uses_plotly_native  ← 新增条件")
    print()
    print("  3. 结果:")
    print("     - Plotly饼图: 只显示原生饼图")
    print("     - Matplotlib图表: 显示图表 + 可能的自动柱状图")
    print("     - 纯数据分析: 显示自动柱状图")
    
    print("\n🎉 用户体验:")
    print("  ✅ 请求饼图 → 只看到一个Plotly原生饼图")
    print("  ✅ 界面清洁，无重复显示")
    print("  ✅ 保持交互功能和图例")

if __name__ == "__main__":
    test_auto_chart_logic()
    test_different_scenarios()
    summarize_final_fix()
