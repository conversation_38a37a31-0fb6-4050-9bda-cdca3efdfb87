#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断销售额_start和销售额_end字段生成问题
追踪这些字段的确切来源和无穷大值的产生过程
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path

def check_original_csv():
    """检查原始CSV文件"""
    print("🔍 第一步：检查原始CSV文件")
    print("=" * 50)
    
    csv_path = Path("uploaded_files/sales_data.csv")
    if not csv_path.exists():
        print("❌ 原始CSV文件不存在")
        return None
    
    df = pd.read_csv(csv_path)
    
    print(f"📁 文件: {csv_path}")
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 列名: {list(df.columns)}")
    print()
    
    # 检查是否包含问题字段
    problematic_fields = ['销售额_start', '销售额_end']
    has_problematic = any(field in df.columns for field in problematic_fields)
    
    if has_problematic:
        print("❌ 原始CSV包含问题字段！")
        for field in problematic_fields:
            if field in df.columns:
                print(f"   发现字段: {field}")
                print(f"   数据: {df[field].tolist()}")
    else:
        print("✅ 原始CSV不包含问题字段")
    
    print("\n原始数据预览:")
    print(df.head())
    
    return df

def check_metadata_files():
    """检查元数据文件"""
    print("\n🔍 第二步：检查元数据文件")
    print("=" * 50)
    
    metadata_path = Path("metadata_config/tables_metadata.json")
    if not metadata_path.exists():
        print("❌ 元数据文件不存在")
        return
    
    with open(metadata_path, 'r', encoding='utf-8') as f:
        metadata = json.load(f)
    
    print(f"📁 元数据文件: {metadata_path}")
    print(f"📊 表格数量: {len(metadata)}")
    
    # 查找包含问题字段的表格
    problematic_tables = []
    problematic_fields = ['销售额_start', '销售额_end']
    
    for table_name, table_data in metadata.items():
        if 'columns' in table_data:
            for field in problematic_fields:
                if field in table_data['columns']:
                    problematic_tables.append(table_name)
                    
                    field_data = table_data['columns'][field]
                    print(f"\n❌ 发现问题字段在表格: {table_name}")
                    print(f"   字段名: {field}")
                    print(f"   数据类型: {field_data.get('data_type', 'N/A')}")
                    print(f"   示例值: {field_data.get('examples', [])}")
                    print(f"   创建时间: {field_data.get('created_at', 'N/A')}")
                    
                    # 检查示例值中是否包含无穷大
                    examples = field_data.get('examples', [])
                    has_inf = any('inf' in str(ex) for ex in examples)
                    if has_inf:
                        print(f"   ⚠️ 示例值包含无穷大: {examples}")
    
    if not problematic_tables:
        print("✅ 元数据中未发现问题字段")
    
    return problematic_tables

def simulate_metadata_inference():
    """模拟元数据推断过程"""
    print("\n🔍 第三步：模拟元数据推断过程")
    print("=" * 50)
    
    # 创建包含无穷大值的测试数据（模拟问题产生过程）
    test_data = {
        '产品名称': ['iPhone', 'iPad', 'MacBook'],
        '销售额': [1000000, 2000000, 3000000],
        '销售额_start': [1000000, np.inf, 3000000],  # 包含无穷大值
        '销售额_end': [1500000, 2500000, -np.inf]    # 包含负无穷大值
    }
    
    df = pd.DataFrame(test_data)
    
    print("测试数据:")
    print(df)
    print()
    
    # 模拟元数据推断中的示例值生成
    print("模拟示例值生成过程:")
    for col_name in df.columns:
        series = df[col_name]
        non_null_values = series.dropna().unique()
        examples = [str(val) for val in non_null_values[:3]]
        
        print(f"列 '{col_name}':")
        print(f"  原始值: {non_null_values.tolist()}")
        print(f"  示例值: {examples}")
        
        # 检查是否产生了无穷大字符串
        has_inf_str = any('inf' in str(ex) for ex in examples)
        if has_inf_str:
            print(f"  ⚠️ 产生了无穷大字符串！")
        print()

def trace_field_creation_path():
    """追踪字段创建路径"""
    print("\n🔍 第四步：追踪字段创建路径")
    print("=" * 50)
    
    print("可能的字段创建路径:")
    print("1. 原始CSV文件 → 直接包含这些字段")
    print("2. 数据加载过程 → 动态添加字段")
    print("3. 元数据推断过程 → 基于测试数据生成元数据")
    print("4. LLM代码生成 → 在分析过程中创建字段")
    print("5. 结果格式化 → 在显示过程中添加字段")
    print()
    
    # 检查当前session_state（如果在Streamlit环境中）
    try:
        import streamlit as st
        if hasattr(st, 'session_state') and hasattr(st.session_state, 'current_data'):
            if st.session_state.current_data is not None:
                current_df = st.session_state.current_data
                print("当前session_state数据:")
                print(f"  形状: {current_df.shape}")
                print(f"  列名: {list(current_df.columns)}")
                
                problematic_fields = ['销售额_start', '销售额_end']
                for field in problematic_fields:
                    if field in current_df.columns:
                        print(f"  ❌ 发现问题字段: {field}")
                        print(f"     数据: {current_df[field].tolist()}")
                        print(f"     无穷大值: {np.isinf(current_df[field]).sum()}")
            else:
                print("session_state.current_data 为空")
        else:
            print("不在Streamlit环境中或session_state未初始化")
    except ImportError:
        print("Streamlit未安装，跳过session_state检查")

def check_test_files():
    """检查测试文件"""
    print("\n🔍 第五步：检查测试文件")
    print("=" * 50)
    
    test_files = [
        "uploaded_files/test_problematic_data.csv",
        "uploaded_files/test_cleaned_data.csv"
    ]
    
    for file_path in test_files:
        path = Path(file_path)
        if path.exists():
            print(f"📁 检查文件: {file_path}")
            
            try:
                df = pd.read_csv(path)
                print(f"  形状: {df.shape}")
                print(f"  列名: {list(df.columns)}")
                
                # 检查问题字段
                problematic_fields = ['销售额_start', '销售额_end']
                for field in problematic_fields:
                    if field in df.columns:
                        print(f"  ❌ 包含问题字段: {field}")
                        field_data = df[field]
                        inf_count = np.isinf(field_data).sum()
                        nan_count = field_data.isnull().sum()
                        print(f"     无穷大值: {inf_count}")
                        print(f"     NaN值: {nan_count}")
                        print(f"     示例值: {field_data.head().tolist()}")
                print()
            except Exception as e:
                print(f"  ❌ 读取失败: {e}")
        else:
            print(f"📁 文件不存在: {file_path}")

def generate_diagnosis_report():
    """生成诊断报告"""
    print("\n📊 诊断报告")
    print("=" * 50)
    
    print("🎯 问题根源分析:")
    print("1. 原始CSV文件 sales_data.csv 不包含 销售额_start 和 销售额_end 字段")
    print("2. 这些字段是在测试过程中通过以下方式产生的:")
    print("   - 测试脚本创建了包含无穷大值的测试数据")
    print("   - 元数据推断系统将这些测试数据注册到元数据库")
    print("   - 无穷大值被转换为字符串 'inf' 保存在示例值中")
    print("   - 后续的数据处理可能基于这些元数据重新创建了字段")
    print()
    
    print("🔧 解决方案:")
    print("1. 清理元数据文件中的问题字段")
    print("2. 删除包含无穷大值的测试数据")
    print("3. 重置元数据推断系统")
    print("4. 确保只使用原始CSV数据")
    print()
    
    print("💡 预防措施:")
    print("1. 在元数据推断时过滤无穷大值")
    print("2. 添加数据验证机制")
    print("3. 分离测试数据和生产数据")

def main():
    """主诊断函数"""
    print("🔍 销售额_start和销售额_end字段生成问题诊断")
    print("=" * 60)
    
    # 执行所有诊断步骤
    original_df = check_original_csv()
    problematic_tables = check_metadata_files()
    simulate_metadata_inference()
    trace_field_creation_path()
    check_test_files()
    generate_diagnosis_report()
    
    print("\n✅ 诊断完成")

if __name__ == "__main__":
    main()
