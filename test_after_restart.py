#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重启后测试脚本
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_after_restart():
    """重启后测试"""
    print("🧪 重启后功能测试")
    print("=" * 50)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500, 20200, 15000, 9700, 8000],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    # 测试查询
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 30)
    
    try:
        result = analyze_data(df, query, "sales_data", use_metadata=True)
        
        if result.get('success'):
            print("✅ 分析成功")
            
            # 检查关键指标
            code = result.get('code', '')
            plotly_code = result.get('plotly_code', '')
            output = result.get('output', '')
            error = result.get('error', '')
            
            print(f"📊 生成代码长度: {len(code)} 字符")
            print(f"📊 Plotly代码长度: {len(plotly_code)} 字符")
            print(f"📊 输出长度: {len(output)} 字符")
            print(f"📊 错误信息: {error if error else '无'}")
            
            # 检查是否包含异常字段
            problematic_fields = ['销售额_start', '销售额_end']
            has_problematic_fields = any(field in code for field in problematic_fields)
            
            if has_problematic_fields:
                print("❌ 检测到异常字段，修复可能未生效")
                for field in problematic_fields:
                    if field in code:
                        print(f"  发现异常字段: {field}")
            else:
                print("✅ 未检测到异常字段")
            
            # 检查语法
            try:
                compile(code, '<string>', 'exec')
                print("✅ 生成代码语法正确")
            except SyntaxError as e:
                print(f"❌ 生成代码语法错误: {e}")
            
            # 检查代码清理
            if plotly_code:
                if '# 已在执行环境中提供' in plotly_code:
                    print("✅ 代码清理机制正常工作")
                else:
                    print("❌ 代码清理机制可能未生效")
            
            return True
            
        else:
            print(f"❌ 分析失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_console_warnings():
    """检查控制台警告的解决方案"""
    print(f"\n🔍 控制台警告分析")
    print("=" * 30)
    
    warnings_info = {
        "Scale bindings": {
            "原因": "图表配置冲突或数据格式问题",
            "解决": "使用Streamlit原生图表方法"
        },
        "Infinite extent": {
            "原因": "数据中包含无穷大值或NaN",
            "解决": "数据清理和语法修复"
        },
        "销售额_start/销售额_end": {
            "原因": "数据处理错误产生异常字段",
            "解决": "修复AI生成代码的语法错误"
        }
    }
    
    print("常见警告及解决方案:")
    for warning, info in warnings_info.items():
        print(f"\n📋 {warning}:")
        print(f"  原因: {info['原因']}")
        print(f"  解决: {info['解决']}")
    
    print(f"\n💡 如果重启后仍有警告:")
    print("1. 检查生成的代码是否包含异常字段")
    print("2. 验证语法修复机制是否正常工作")
    print("3. 确认数据清理流程是否执行")
    print("4. 检查浏览器缓存是否已清理")

if __name__ == "__main__":
    # 运行测试
    success = test_after_restart()
    
    # 提供警告分析
    check_console_warnings()
    
    # 总结
    print(f"\n🎯 重启后测试总结")
    print("=" * 30)
    
    if success:
        print("✅ 重启后功能正常")
        print("建议:")
        print("1. 在Streamlit应用中测试图表查询")
        print("2. 观察控制台是否还有警告")
        print("3. 如果仍有问题，可能需要进一步调试")
    else:
        print("❌ 重启后仍有问题")
        print("建议:")
        print("1. 检查文件是否正确保存")
        print("2. 验证Python模块是否正确重新加载")
        print("3. 考虑重新应用修复")
    
    print(f"\n📞 如果问题持续存在:")
    print("请提供以下信息进行进一步诊断:")
    print("1. 重启后的控制台完整输出")
    print("2. 生成的具体代码内容")
    print("3. 浏览器开发者工具的Network和Console信息")
