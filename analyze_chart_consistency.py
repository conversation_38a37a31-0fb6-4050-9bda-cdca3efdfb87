#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析图表类型一致性问题
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_all_chart_types():
    """测试所有图表类型的一致性"""
    print("🔍 全面分析图表类型一致性")
    print("=" * 60)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据: {df.shape}")
    
    # 定义各种图表类型测试
    chart_tests = [
        # 图表类型, 查询, 期望的最佳实现
        ("饼图", "生成销售金额分布的饼图", "Plotly原生"),
        ("柱状图", "生成销售金额的柱状图", "Streamlit原生st.bar_chart"),
        ("折线图", "生成销售趋势的折线图", "Streamlit原生st.line_chart"),
        ("散点图", "生成销售金额和数量的散点图", "Streamlit原生st.scatter_chart"),
        ("面积图", "生成销售金额的面积图", "Streamlit原生st.area_chart"),
        ("热力图", "生成产品销售热力图", "Plotly原生"),
        ("箱线图", "生成销售金额的箱线图", "Plotly原生"),
        ("直方图", "生成销售金额分布的直方图", "Plotly原生"),
    ]
    
    results = []
    
    for chart_type, query, expected_best in chart_tests:
        print(f"\n📊 测试{chart_type}")
        print(f"  查询: {query}")
        print(f"  期望最佳: {expected_best}")
        
        try:
            result = analyze_data(df, query, table_name="sales_data")
            
            if result and result.get('success'):
                code = result.get('code', '')
                uses_plotly_native = result.get('uses_plotly_native', False)
                has_chart = result.get('has_chart', False)
                
                # 分析实际使用的技术栈
                actual_tech = analyze_chart_technology(code)
                
                print(f"  实际使用: {actual_tech}")
                print(f"  uses_plotly_native: {uses_plotly_native}")
                print(f"  has_chart: {has_chart}")
                
                # 评估一致性
                consistency_score = evaluate_consistency(chart_type, actual_tech, expected_best)
                print(f"  一致性评分: {consistency_score}/5")
                
                results.append({
                    'chart_type': chart_type,
                    'expected': expected_best,
                    'actual': actual_tech,
                    'consistency': consistency_score,
                    'uses_plotly_native': uses_plotly_native,
                    'has_chart': has_chart
                })
                
            else:
                print(f"  ❌ 查询失败")
                results.append({
                    'chart_type': chart_type,
                    'expected': expected_best,
                    'actual': 'FAILED',
                    'consistency': 0,
                    'uses_plotly_native': False,
                    'has_chart': False
                })
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
            results.append({
                'chart_type': chart_type,
                'expected': expected_best,
                'actual': 'ERROR',
                'consistency': 0,
                'uses_plotly_native': False,
                'has_chart': False
            })
    
    return results

def analyze_chart_technology(code):
    """分析代码使用的图表技术"""
    if 'st.bar_chart' in code:
        return "Streamlit原生st.bar_chart"
    elif 'st.line_chart' in code:
        return "Streamlit原生st.line_chart"
    elif 'st.scatter_chart' in code:
        return "Streamlit原生st.scatter_chart"
    elif 'st.area_chart' in code:
        return "Streamlit原生st.area_chart"
    elif 'st.plotly_chart' in code:
        return "Plotly原生"
    elif 'px.' in code or 'plotly' in code.lower():
        return "Plotly"
    elif 'plt.' in code or 'matplotlib' in code.lower():
        return "Matplotlib"
    else:
        return "未知"

def evaluate_consistency(chart_type, actual_tech, expected_best):
    """评估一致性得分"""
    if actual_tech == expected_best:
        return 5  # 完美匹配
    elif actual_tech == "Plotly原生" and expected_best in ["Plotly原生", "Streamlit原生st.bar_chart"]:
        return 4  # 很好的替代方案
    elif actual_tech == "Plotly" and expected_best == "Plotly原生":
        return 3  # 需要改进为原生显示
    elif actual_tech == "Matplotlib":
        return 2  # 基础实现，需要改进
    elif actual_tech in ["FAILED", "ERROR"]:
        return 0  # 完全失败
    else:
        return 1  # 其他情况

def analyze_results(results):
    """分析测试结果"""
    print(f"\n📊 一致性分析结果")
    print("=" * 60)
    
    # 统计各技术栈使用情况
    tech_count = {}
    consistency_scores = []
    
    for result in results:
        actual = result['actual']
        tech_count[actual] = tech_count.get(actual, 0) + 1
        consistency_scores.append(result['consistency'])
    
    print(f"🔧 技术栈使用统计:")
    for tech, count in tech_count.items():
        print(f"  {tech}: {count}次")
    
    avg_consistency = sum(consistency_scores) / len(consistency_scores) if consistency_scores else 0
    print(f"\n📈 平均一致性得分: {avg_consistency:.1f}/5")
    
    # 识别问题
    print(f"\n❌ 发现的问题:")
    problems = []
    
    for result in results:
        if result['consistency'] < 4:
            problems.append(f"  {result['chart_type']}: 期望{result['expected']}，实际{result['actual']}")
    
    if problems:
        for problem in problems:
            print(problem)
    else:
        print("  无问题发现")
    
    return results

def generate_improvement_plan(results):
    """生成改进计划"""
    print(f"\n🛠️ 改进计划")
    print("=" * 60)
    
    print("1️⃣ 需要实现的Streamlit原生图表:")
    streamlit_native_needed = []
    
    for result in results:
        if "Streamlit原生" in result['expected'] and result['actual'] != result['expected']:
            streamlit_native_needed.append(result['chart_type'])
    
    if streamlit_native_needed:
        for chart in streamlit_native_needed:
            print(f"  📊 {chart}")
    else:
        print("  ✅ 无需添加")
    
    print("\n2️⃣ 需要添加Plotly回退的图表:")
    plotly_fallback_needed = []
    
    for result in results:
        if result['actual'] == "Matplotlib" and result['expected'] in ["Plotly原生", "Streamlit原生st.bar_chart"]:
            plotly_fallback_needed.append(result['chart_type'])
    
    if plotly_fallback_needed:
        for chart in plotly_fallback_needed:
            print(f"  📊 {chart}")
    else:
        print("  ✅ 无需添加")
    
    print("\n3️⃣ 需要修复显示逻辑的图表:")
    display_logic_needed = []
    
    for result in results:
        if result['actual'] in ["Plotly", "Matplotlib"] and not result['uses_plotly_native']:
            display_logic_needed.append(result['chart_type'])
    
    if display_logic_needed:
        for chart in display_logic_needed:
            print(f"  📊 {chart}")
    else:
        print("  ✅ 无需修复")

if __name__ == "__main__":
    results = test_all_chart_types()
    analyze_results(results)
    generate_improvement_plan(results)
