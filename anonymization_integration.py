#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据脱敏集成方案
将脱敏功能集成到现有的数据分析系统中
"""

import pandas as pd
import streamlit as st
from data_anonymization_solution import DataAnonymizer

def create_anonymization_ui():
    """创建脱敏控制界面"""
    st.sidebar.markdown("---")
    st.sidebar.subheader("🔒 数据脱敏设置")
    
    # 脱敏开关
    enable_anonymization = st.sidebar.checkbox(
        "启用数据脱敏", 
        value=True,
        help="向LLM发送数据前进行脱敏处理"
    )
    
    if enable_anonymization:
        # 脱敏级别选择
        anonymization_level = st.sidebar.selectbox(
            "脱敏级别",
            ["低", "中", "高"],
            index=1,
            help="低：仅脱敏个人信息\n中：脱敏个人信息+产品名称\n高：全面脱敏"
        )
        
        # 高级设置
        with st.sidebar.expander("🔧 高级脱敏设置"):
            company_strategy = st.selectbox(
                "公司/产品名称策略",
                ["通用类别", "哈希编码", "保持原样"],
                index=0
            )
            
            financial_strategy = st.selectbox(
                "财务数据策略", 
                ["比例缩放", "范围映射", "完全隐藏"],
                index=0
            )
            
            personal_strategy = st.selectbox(
                "个人信息策略",
                ["哈希编码", "角色代号", "完全隐藏"],
                index=0
            )
        
        # 构建配置
        config = build_anonymization_config(
            anonymization_level, 
            company_strategy, 
            financial_strategy, 
            personal_strategy
        )
        
        return enable_anonymization, config
    
    return enable_anonymization, None

def build_anonymization_config(level, company_strategy, financial_strategy, personal_strategy):
    """构建脱敏配置"""
    strategy_mapping = {
        "通用类别": "generic",
        "哈希编码": "hash", 
        "保持原样": "keep",
        "比例缩放": "scale",
        "范围映射": "range",
        "完全隐藏": "remove",
        "角色代号": "fake"
    }
    
    config = {
        "anonymization_level": level.lower(),
        "preserve_data_patterns": True,
        "preserve_relationships": True,
        "company_name_strategy": strategy_mapping.get(company_strategy, "generic"),
        "personal_data_strategy": strategy_mapping.get(personal_strategy, "hash"),
        "financial_data_strategy": strategy_mapping.get(financial_strategy, "scale"),
        "date_strategy": "shift",
        "geographic_strategy": "generic"
    }
    
    return config

def analyze_data_with_anonymization(df, query, table_name="data_table", use_metadata=True, anonymization_config=None):
    """
    带脱敏功能的数据分析函数
    
    Args:
        df: 原始DataFrame
        query: 用户查询
        table_name: 表名
        use_metadata: 是否使用元数据
        anonymization_config: 脱敏配置，None表示不脱敏
    
    Returns:
        dict: 分析结果
    """
    from perfect_tongyi_integration import analyze_data
    
    if anonymization_config:
        # 创建脱敏器
        anonymizer = DataAnonymizer()
        anonymizer.config.update(anonymization_config)
        
        # 脱敏数据
        df_safe, anonymization_report = anonymizer.anonymize_dataframe(
            df, 
            preserve_analysis_capability=True
        )
        
        # 显示脱敏信息
        st.info(f"🔒 数据已脱敏，处理列: {', '.join(anonymization_report['anonymized_columns'])}")
        
        # 显示脱敏详情
        with st.expander("📋 查看脱敏详情"):
            st.json(anonymization_report['anonymization_methods'])
            
            col1, col2 = st.columns(2)
            with col1:
                st.subheader("原始数据样本")
                st.dataframe(df.head(3), use_container_width=True)
            
            with col2:
                st.subheader("脱敏后数据样本")
                st.dataframe(df_safe.head(3), use_container_width=True)
        
        # 使用脱敏后的数据进行分析
        result = analyze_data(df_safe, query, table_name, use_metadata)
        
        # 在结果中标记使用了脱敏
        if result:
            result['anonymization_used'] = True
            result['anonymization_report'] = anonymization_report
        
        return result
    else:
        # 不使用脱敏，直接分析
        result = analyze_data(df, query, table_name, use_metadata)
        if result:
            result['anonymization_used'] = False
        return result

def display_anonymization_status(result):
    """显示脱敏状态"""
    if result and result.get('anonymization_used'):
        st.success("🔒 本次分析使用了数据脱敏保护")
        
        # 显示脱敏统计
        report = result.get('anonymization_report', {})
        anonymized_cols = len(report.get('anonymized_columns', []))
        total_cols = anonymized_cols + len(report.get('preserved_columns', []))
        
        st.metric(
            "脱敏覆盖率", 
            f"{anonymized_cols}/{total_cols}",
            f"{(anonymized_cols/total_cols*100):.1f}%" if total_cols > 0 else "0%"
        )
    else:
        st.warning("⚠️ 本次分析使用了原始数据")

def create_anonymization_test():
    """创建脱敏测试功能"""
    st.subheader("🧪 脱敏效果测试")
    
    if st.session_state.current_data is not None:
        df = st.session_state.current_data
        
        # 创建测试配置
        test_configs = {
            "低级别脱敏": {
                "company_name_strategy": "keep",
                "personal_data_strategy": "hash",
                "financial_data_strategy": "keep",
                "geographic_strategy": "keep"
            },
            "中级别脱敏": {
                "company_name_strategy": "generic",
                "personal_data_strategy": "hash", 
                "financial_data_strategy": "scale",
                "geographic_strategy": "generic"
            },
            "高级别脱敏": {
                "company_name_strategy": "hash",
                "personal_data_strategy": "hash",
                "financial_data_strategy": "range", 
                "geographic_strategy": "generic"
            }
        }
        
        selected_config = st.selectbox("选择测试配置", list(test_configs.keys()))
        
        if st.button("🔍 测试脱敏效果"):
            anonymizer = DataAnonymizer()
            anonymizer.config.update(test_configs[selected_config])
            
            df_anonymized, report = anonymizer.anonymize_dataframe(df)
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("原始数据")
                st.dataframe(df.head(), use_container_width=True)
            
            with col2:
                st.subheader(f"脱敏后数据 ({selected_config})")
                st.dataframe(df_anonymized.head(), use_container_width=True)
            
            st.subheader("脱敏报告")
            st.json(report)
    else:
        st.info("请先上传数据文件以测试脱敏效果")

def export_anonymized_data():
    """导出脱敏后的数据"""
    if st.session_state.current_data is not None:
        df = st.session_state.current_data
        
        st.subheader("📤 导出脱敏数据")
        
        # 脱敏配置
        config = {
            "company_name_strategy": "generic",
            "personal_data_strategy": "hash",
            "financial_data_strategy": "scale",
            "geographic_strategy": "generic"
        }
        
        if st.button("生成脱敏数据"):
            anonymizer = DataAnonymizer()
            anonymizer.config.update(config)
            
            df_anonymized, report = anonymizer.anonymize_dataframe(df)
            
            # 显示预览
            st.subheader("脱敏数据预览")
            st.dataframe(df_anonymized, use_container_width=True)
            
            # 提供下载
            csv_data = df_anonymized.to_csv(index=False)
            st.download_button(
                label="📥 下载脱敏数据 (CSV)",
                data=csv_data,
                file_name=f"anonymized_{st.session_state.current_file}",
                mime="text/csv"
            )
            
            st.success("✅ 脱敏数据已生成，可以安全用于外部分析")

# 使用示例
def demo_integration():
    """演示集成效果"""
    print("🔧 脱敏集成演示")
    print("=" * 50)
    
    # 模拟Streamlit会话状态
    class MockSession:
        def __init__(self):
            self.current_data = pd.read_csv('uploaded_files/sales_data.csv')
            self.current_file = 'sales_data.csv'
    
    # 模拟配置
    config = {
        "company_name_strategy": "generic",
        "personal_data_strategy": "hash",
        "financial_data_strategy": "scale"
    }
    
    # 测试脱敏分析
    session = MockSession()
    query = "分析各地区的产品销售总额"
    
    print(f"查询: {query}")
    print("使用脱敏配置:", config)
    
    # 这里会调用实际的分析函数
    # result = analyze_data_with_anonymization(
    #     session.current_data, 
    #     query, 
    #     anonymization_config=config
    # )
    
    print("✅ 集成测试完成")

if __name__ == "__main__":
    demo_integration()
