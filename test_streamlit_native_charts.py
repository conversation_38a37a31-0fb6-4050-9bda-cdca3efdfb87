#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Streamlit原生图表组件
"""

import pandas as pd
import streamlit as st
import numpy as np

def test_streamlit_chart_capabilities():
    """测试Streamlit原生图表能力"""
    print("🔍 测试Streamlit原生图表能力")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据: {df.shape}")
    
    # 准备饼图数据
    product_sales = df.groupby('产品名称')['销售金额'].sum()
    print(f"\n📊 产品销售数据:")
    print(product_sales)
    
    # 检查Streamlit原生图表组件
    print(f"\n🎨 Streamlit原生图表组件:")
    
    # 1. 条形图 (st.bar_chart)
    print("1. st.bar_chart - ✅ 支持")
    
    # 2. 线图 (st.line_chart)  
    print("2. st.line_chart - ✅ 支持")
    
    # 3. 面积图 (st.area_chart)
    print("3. st.area_chart - ✅ 支持")
    
    # 4. 散点图 (st.scatter_chart)
    print("4. st.scatter_chart - ✅ 支持")
    
    # 5. 饼图 (st.pie_chart) - 检查是否存在
    try:
        # 尝试访问st.pie_chart
        pie_chart_func = getattr(st, 'pie_chart', None)
        if pie_chart_func:
            print("5. st.pie_chart - ✅ 支持")
        else:
            print("5. st.pie_chart - ❌ 不支持")
    except:
        print("5. st.pie_chart - ❌ 不支持")
    
    # 6. Plotly图表 (st.plotly_chart)
    print("6. st.plotly_chart - ✅ 支持 (需要plotly)")
    
    # 7. Altair图表 (st.altair_chart)
    print("7. st.altair_chart - ✅ 支持 (需要altair)")
    
    return product_sales

def test_plotly_pie_chart():
    """测试Plotly饼图（Streamlit原生支持）"""
    print(f"\n🥧 测试Plotly饼图")
    print("-" * 30)
    
    try:
        import plotly.express as px
        import plotly.graph_objects as go
        
        # 加载数据
        df = pd.read_csv('uploaded_files/sales_data.csv')
        product_sales = df.groupby('产品名称')['销售金额'].sum().reset_index()
        
        print("✅ Plotly可用")
        print(f"📊 数据准备完成: {product_sales.shape}")
        
        # 生成Plotly饼图代码
        plotly_pie_code = """
import plotly.express as px
product_sales = df.groupby('产品名称')['销售金额'].sum().reset_index()
print("销售金额分布:")
print(product_sales)
fig = px.pie(product_sales, values='销售金额', names='产品名称', 
             title='销售金额分布饼图')
fig.update_traces(textposition='inside', textinfo='percent+label')
st.plotly_chart(fig, use_container_width=True)
"""
        
        print(f"📝 Plotly饼图代码模板:")
        print(plotly_pie_code)
        
        return True
        
    except ImportError:
        print("❌ Plotly不可用")
        return False

def test_altair_pie_chart():
    """测试Altair饼图（Streamlit原生支持）"""
    print(f"\n📊 测试Altair饼图")
    print("-" * 30)
    
    try:
        import altair as alt
        
        # 加载数据
        df = pd.read_csv('uploaded_files/sales_data.csv')
        product_sales = df.groupby('产品名称')['销售金额'].sum().reset_index()
        
        print("✅ Altair可用")
        print(f"📊 数据准备完成: {product_sales.shape}")
        
        # 生成Altair饼图代码（注意：Altair的饼图比较复杂）
        altair_pie_code = """
import altair as alt
import numpy as np
product_sales = df.groupby('产品名称')['销售金额'].sum().reset_index()
print("销售金额分布:")
print(product_sales)

# 计算角度
product_sales['angle'] = product_sales['销售金额'] / product_sales['销售金额'].sum() * 2 * np.pi
product_sales['cumulative'] = product_sales['angle'].cumsum()
product_sales['start_angle'] = product_sales['cumulative'] - product_sales['angle']

# 创建饼图
chart = alt.Chart(product_sales).add_selection(
    alt.selection_single()
).mark_arc(innerRadius=50, outerRadius=120).encode(
    theta=alt.Theta('销售金额:Q'),
    color=alt.Color('产品名称:N', legend=alt.Legend(title="产品类别")),
    tooltip=['产品名称:N', '销售金额:Q']
).resolve_scale(
    color='independent'
).properties(
    title='销售金额分布饼图',
    width=400,
    height=400
)

st.altair_chart(chart, use_container_width=True)
"""
        
        print(f"📝 Altair饼图代码模板:")
        print(altair_pie_code)
        
        return True
        
    except ImportError:
        print("❌ Altair不可用")
        return False

def recommend_best_solution():
    """推荐最佳解决方案"""
    print(f"\n🎯 推荐最佳解决方案")
    print("=" * 50)
    
    # 测试各种方案
    plotly_available = test_plotly_pie_chart()
    altair_available = test_altair_pie_chart()
    
    print(f"\n📋 方案对比:")
    print(f"1. Plotly饼图: {'✅ 可用' if plotly_available else '❌ 不可用'}")
    print(f"   - 优点: 简单易用，交互性强，完全原生Streamlit风格")
    print(f"   - 缺点: 需要plotly依赖")
    
    print(f"\n2. Altair饼图: {'✅ 可用' if altair_available else '❌ 不可用'}")
    print(f"   - 优点: 声明式语法，完全原生Streamlit风格")
    print(f"   - 缺点: 代码复杂，需要altair依赖")
    
    print(f"\n3. Matplotlib饼图: ✅ 可用")
    print(f"   - 优点: 无额外依赖，功能强大")
    print(f"   - 缺点: 非原生风格，需要样式调整")
    
    # 推荐方案
    if plotly_available:
        print(f"\n🏆 推荐方案: Plotly饼图")
        print(f"   理由: 最接近Streamlit原生风格，代码简洁，交互性好")
    elif altair_available:
        print(f"\n🏆 推荐方案: Altair饼图")
        print(f"   理由: 完全原生Streamlit风格")
    else:
        print(f"\n🏆 推荐方案: 优化的Matplotlib饼图")
        print(f"   理由: 无额外依赖，通过样式调整接近原生风格")

if __name__ == "__main__":
    product_sales = test_streamlit_chart_capabilities()
    recommend_best_solution()
