#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面分析PandasAI结果格式
识别所有可能的输出类型并优化显示
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data
import json
from pathlib import Path

def create_comprehensive_test_data():
    """创建全面的测试数据集"""
    return pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch', 'Mac Mini', 'iMac', 'Studio Display'],
        '类别': ['手机', '平板', '笔记本', '配件', '配件', '台式机', '台式机', '显示器'],
        '价格': [6999, 4599, 14999, 1899, 3199, 4999, 12999, 11999],
        '销量': [1200, 800, 400, 1500, 1000, 300, 250, 150],
        '库存': [150, 200, 80, 300, 250, 100, 75, 50],
        '评分': [4.8, 4.6, 4.9, 4.7, 4.5, 4.4, 4.3, 4.2],
        '上市日期': ['2023-09-15', '2023-10-20', '2023-11-10', '2023-09-22', '2023-10-15', '2023-11-05', '2023-12-01', '2023-12-15'],
        '地区': ['全国', '全国', '全国', '全国', '全国', '全国', '全国', '全国'],
        '供应商': ['苹果', '苹果', '苹果', '苹果', '苹果', '苹果', '苹果', '苹果']
    })

def analyze_all_result_formats():
    """分析所有可能的结果格式"""
    
    test_data = create_comprehensive_test_data()
    
    # 定义各种类型的测试查询
    test_queries = {
        'dataframe_info': [
            "显示数据的基本信息",
            "查看数据结构",
            "显示DataFrame信息"
        ],
        'statistical_summary': [
            "计算所有数值列的统计摘要",
            "显示describe统计信息",
            "计算基本统计量"
        ],
        'numerical_calculation': [
            "计算总销售额",
            "计算平均价格",
            "求销量的总和",
            "计算最高评分"
        ],
        'tabular_data': [
            "显示前5行数据",
            "按价格排序显示所有数据",
            "显示价格大于5000的产品",
            "按类别分组显示数据"
        ],
        'series_data': [
            "计算各类别的平均价格",
            "统计各类别的产品数量",
            "计算各产品的销售额",
            "显示各类别的总销量"
        ],
        'correlation_analysis': [
            "计算价格和销量的相关性",
            "显示数值列之间的相关性矩阵"
        ],
        'aggregation_results': [
            "按类别汇总销售数据",
            "计算每个类别的平均评分",
            "统计各价格区间的产品数量"
        ],
        'chart_generation': [
            "生成价格分布的直方图",
            "创建各类别销量的饼图",
            "画出价格和销量的散点图",
            "制作各类别平均价格的柱状图"
        ]
    }
    
    results_analysis = {}
    
    print("🔍 全面分析PandasAI结果格式")
    print("=" * 60)
    
    for category, queries in test_queries.items():
        print(f"\n📊 分析类别: {category}")
        print("-" * 40)
        
        category_results = []
        
        for query in queries:
            print(f"\n🔍 查询: {query}")
            
            try:
                result = analyze_data(test_data, query)
                
                if result and result.get('success'):
                    analysis = {
                        'query': query,
                        'output_length': len(result.get('output', '')),
                        'has_chart': result.get('has_chart', False),
                        'output_preview': result.get('output', '')[:200] + '...' if len(result.get('output', '')) > 200 else result.get('output', ''),
                        'code_generated': result.get('code', ''),
                        'output_characteristics': analyze_output_characteristics(result.get('output', ''))
                    }
                    
                    category_results.append(analysis)
                    
                    print(f"✅ 成功 - 输出长度: {analysis['output_length']}")
                    print(f"📊 有图表: {analysis['has_chart']}")
                    print(f"🔍 输出特征: {analysis['output_characteristics']}")
                    
                else:
                    print("❌ 分析失败")
                    if result and result.get('error'):
                        print(f"   错误: {result['error']}")
                        
            except Exception as e:
                print(f"❌ 异常: {e}")
        
        results_analysis[category] = category_results
    
    return results_analysis

def analyze_output_characteristics(output):
    """分析输出特征"""
    if not output:
        return "empty"
    
    characteristics = []
    
    # 检查是否包含DataFrame信息
    if 'DataFrame' in output and 'RangeIndex' in output:
        characteristics.append('dataframe_info')
    
    # 检查是否包含统计信息
    if any(word in output for word in ['count', 'mean', 'std', 'min', 'max']):
        characteristics.append('statistics')
    
    # 检查是否包含表格数据
    lines = output.strip().split('\n')
    if len(lines) > 2:
        # 检查是否有表格结构
        first_line_parts = len(lines[0].split())
        if first_line_parts > 1:
            similar_structure = sum(1 for line in lines[1:3] if abs(len(line.split()) - first_line_parts) <= 1)
            if similar_structure >= 1:
                characteristics.append('tabular')
    
    # 检查是否为单一数值
    if len(lines) == 1:
        try:
            float(lines[0].strip())
            characteristics.append('single_number')
        except:
            pass
    
    # 检查是否包含索引-值对
    index_value_pairs = 0
    for line in lines[:5]:
        if re.match(r'^.+\s+[\d.]+$', line.strip()):
            index_value_pairs += 1
    
    if index_value_pairs >= 2:
        characteristics.append('series_like')
    
    # 检查行数
    if len(lines) > 10:
        characteristics.append('long_output')
    elif len(lines) > 3:
        characteristics.append('medium_output')
    else:
        characteristics.append('short_output')
    
    return characteristics if characteristics else ['text']

def save_analysis_results(results):
    """保存分析结果"""
    output_file = Path("pandasai_format_analysis.json")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 分析结果已保存到: {output_file}")

def generate_optimization_recommendations(results):
    """生成优化建议"""
    print("\n🎯 优化建议")
    print("=" * 60)
    
    all_characteristics = set()
    chart_queries = []
    
    for category, category_results in results.items():
        for result in category_results:
            all_characteristics.update(result['output_characteristics'])
            if result['has_chart']:
                chart_queries.append(result['query'])
    
    print(f"📊 发现的输出特征类型: {sorted(all_characteristics)}")
    print(f"📈 生成图表的查询数量: {len(chart_queries)}")
    
    # 生成具体建议
    recommendations = []
    
    if 'dataframe_info' in all_characteristics:
        recommendations.append("需要优化DataFrame信息显示 - 使用st.metric()显示关键指标")
    
    if 'statistics' in all_characteristics:
        recommendations.append("需要优化统计数据显示 - 使用st.dataframe()显示统计表格")
    
    if 'tabular' in all_characteristics:
        recommendations.append("需要优化表格数据显示 - 使用st.dataframe()或st.table()")
    
    if 'single_number' in all_characteristics:
        recommendations.append("需要优化单一数值显示 - 使用st.metric()或st.number_input()")
    
    if 'series_like' in all_characteristics:
        recommendations.append("需要优化序列数据显示 - 使用st.dataframe()和st.bar_chart()")
    
    if chart_queries:
        recommendations.append("需要确保图表正确显示 - 使用st.image()显示保存的图表")
    
    print("\n💡 具体优化建议:")
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")
    
    return recommendations

def main():
    """主分析函数"""
    print("🔍 PandasAI结果格式全面分析")
    print("=" * 60)
    
    # 执行分析
    results = analyze_all_result_formats()
    
    # 保存结果
    save_analysis_results(results)
    
    # 生成优化建议
    recommendations = generate_optimization_recommendations(results)
    
    print(f"\n🎉 分析完成！")
    print(f"📊 共分析了 {len(results)} 个类别")
    print(f"💡 生成了 {len(recommendations)} 条优化建议")

if __name__ == "__main__":
    import re
    main()
