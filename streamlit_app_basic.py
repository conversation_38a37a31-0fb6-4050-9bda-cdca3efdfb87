#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能数据分析助手 - 基础版本
不依赖PandasAI，可以直接运行
"""

import streamlit as st
import pandas as pd
import os
import json
import uuid
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 页面配置
st.set_page_config(
    page_title="智能数据分析助手",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 全局配置
UPLOAD_DIR = Path("uploaded_files")
CHAT_HISTORY_DIR = Path("chat_history")
SUPPORTED_FORMATS = ['.csv', '.xlsx', '.xls', '.txt', '.json']

# 创建必要目录
UPLOAD_DIR.mkdir(exist_ok=True)
CHAT_HISTORY_DIR.mkdir(exist_ok=True)

class ChatManager:
    """聊天会话管理器"""
    
    def __init__(self):
        self.session_id = self._get_or_create_session_id()
        self.history_file = CHAT_HISTORY_DIR / f"chat_{self.session_id}.json"
    
    def _get_or_create_session_id(self):
        """获取或创建会话ID"""
        if 'session_id' not in st.session_state:
            st.session_state.session_id = str(uuid.uuid4())
        return st.session_state.session_id
    
    def load_chat_history(self):
        """加载聊天历史"""
        if self.history_file.exists():
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                st.error(f"加载聊天历史失败: {e}")
        return []
    
    def save_chat_history(self, history):
        """保存聊天历史"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
        except Exception as e:
            st.error(f"保存聊天历史失败: {e}")
    
    def add_message(self, role, content, data_info=None):
        """添加消息到历史"""
        if 'chat_history' not in st.session_state:
            st.session_state.chat_history = self.load_chat_history()
        
        message = {
            'role': role,
            'content': content,
            'timestamp': datetime.now().isoformat(),
            'data_info': data_info
        }
        
        st.session_state.chat_history.append(message)
        self.save_chat_history(st.session_state.chat_history)

class FileManager:
    """文件管理器"""
    
    @staticmethod
    def save_uploaded_file(uploaded_file):
        """保存上传的文件"""
        try:
            file_path = UPLOAD_DIR / uploaded_file.name
            with open(file_path, "wb") as f:
                f.write(uploaded_file.getbuffer())
            return file_path
        except Exception as e:
            st.error(f"文件保存失败: {e}")
            return None
    
    @staticmethod
    def load_data_file(file_path):
        """加载数据文件"""
        try:
            file_ext = file_path.suffix.lower()
            
            if file_ext == '.csv':
                return pd.read_csv(file_path, encoding='utf-8')
            elif file_ext in ['.xlsx', '.xls']:
                return pd.read_excel(file_path)
            elif file_ext == '.json':
                return pd.read_json(file_path)
            elif file_ext == '.txt':
                # 尝试作为CSV读取
                return pd.read_csv(file_path, sep='\t', encoding='utf-8')
            else:
                st.error(f"不支持的文件格式: {file_ext}")
                return None
                
        except Exception as e:
            st.error(f"文件加载失败: {e}")
            return None
    
    @staticmethod
    def get_uploaded_files():
        """获取已上传的文件列表"""
        if not UPLOAD_DIR.exists():
            return []
        
        files = []
        for file_path in UPLOAD_DIR.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in SUPPORTED_FORMATS:
                files.append({
                    'name': file_path.name,
                    'path': file_path,
                    'size': file_path.stat().st_size,
                    'modified': datetime.fromtimestamp(file_path.stat().st_mtime)
                })
        
        return sorted(files, key=lambda x: x['modified'], reverse=True)

def init_session_state():
    """初始化会话状态"""
    if 'chat_manager' not in st.session_state:
        st.session_state.chat_manager = ChatManager()
    
    if 'chat_history' not in st.session_state:
        st.session_state.chat_history = st.session_state.chat_manager.load_chat_history()
    
    if 'current_data' not in st.session_state:
        st.session_state.current_data = None

def check_api_key():
    """检查API密钥"""
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        return False
    return True

def create_sidebar():
    """创建侧边栏"""
    with st.sidebar:
        st.title("🤖 智能数据分析助手")
        st.markdown("---")
        
        # API状态检查
        if check_api_key():
            st.success("✅ API密钥已配置")
        else:
            st.warning("⚠️ API密钥未配置")
            st.info("请在.env文件中配置DASHSCOPE_API_KEY")
        
        # 文件上传区域
        st.subheader("📁 数据文件上传")
        uploaded_file = st.file_uploader(
            "选择数据文件",
            type=['csv', 'xlsx', 'xls', 'txt', 'json'],
            help="支持CSV、Excel、TXT、JSON格式"
        )
        
        if uploaded_file is not None:
            if st.button("上传文件", type="primary"):
                with st.spinner("正在上传文件..."):
                    file_path = FileManager.save_uploaded_file(uploaded_file)
                    if file_path:
                        st.success(f"✅ 文件上传成功: {uploaded_file.name}")
                        st.rerun()
        
        # 已上传文件列表
        st.subheader("📋 已上传文件")
        uploaded_files = FileManager.get_uploaded_files()
        
        if uploaded_files:
            for file_info in uploaded_files:
                col1, col2 = st.columns([3, 1])
                with col1:
                    if st.button(f"📄 {file_info['name']}", key=f"load_{file_info['name']}"):
                        with st.spinner("正在加载数据..."):
                            df = FileManager.load_data_file(file_info['path'])
                            if df is not None:
                                st.session_state.current_data = df
                                st.session_state.current_file = file_info['name']
                                st.success(f"✅ 数据加载成功")
                                st.rerun()
                
                with col2:
                    file_size = f"{file_info['size'] / 1024:.1f}KB"
                    st.caption(file_size)
        else:
            st.info("暂无上传文件")
        
        # 会话管理
        st.markdown("---")
        st.subheader("💬 会话管理")
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("🗑️ 清空历史"):
                st.session_state.chat_history = []
                st.session_state.chat_manager.save_chat_history([])
                st.success("聊天历史已清空")
                st.rerun()
        
        with col2:
            if st.button("🔄 新会话"):
                st.session_state.session_id = str(uuid.uuid4())
                st.session_state.chat_manager = ChatManager()
                st.session_state.chat_history = []
                st.success("新会话已创建")
                st.rerun()

def display_chat_history():
    """显示聊天历史"""
    if st.session_state.chat_history:
        for message in st.session_state.chat_history:
            if message['role'] == 'user':
                with st.chat_message("user"):
                    st.write(message['content'])
                    if message.get('data_info'):
                        st.caption(f"📊 数据: {message['data_info']}")
            else:
                with st.chat_message("assistant"):
                    st.write(message['content'])

def perform_basic_analysis(df, query):
    """执行基础数据分析"""
    try:
        query_lower = query.lower()
        
        # 基础统计
        if any(word in query_lower for word in ['概览', '统计', '基本信息', '描述']):
            st.write("📊 **数据基本统计信息:**")
            st.write(df.describe())
            return "已显示数据基本统计信息"
        
        # 数据形状
        elif any(word in query_lower for word in ['形状', '大小', '维度']):
            st.write(f"📏 **数据形状:** {df.shape[0]} 行 × {df.shape[1]} 列")
            return f"数据包含 {df.shape[0]} 行和 {df.shape[1]} 列"
        
        # 列信息
        elif any(word in query_lower for word in ['列', '字段', '属性']):
            st.write("📋 **列信息:**")
            st.write(df.dtypes)
            return "已显示列信息"
        
        # 缺失值
        elif any(word in query_lower for word in ['缺失', '空值', 'null', 'nan']):
            missing = df.isnull().sum()
            st.write("🔍 **缺失值统计:**")
            st.write(missing[missing > 0])
            return "已显示缺失值统计"
        
        # 前几行
        elif any(word in query_lower for word in ['前', 'head', '开头']):
            st.write("👀 **数据前5行:**")
            st.dataframe(df.head())
            return "已显示数据前5行"
        
        else:
            return "抱歉，基础版本只支持简单的数据查看功能。要使用AI分析功能，请安装完整版本。"
            
    except Exception as e:
        return f"分析过程中出现错误: {str(e)}"

def main():
    """主函数"""
    # 初始化
    init_session_state()
    
    # 创建侧边栏
    create_sidebar()
    
    # 主界面
    st.title("🤖 智能数据分析助手 (基础版)")
    st.markdown("基础数据分析功能 - 无需复杂依赖")
    
    # 显示版本信息
    st.info("💡 这是基础版本，支持基本数据查看功能。要使用AI分析功能，请参考完整版本安装说明。")
    
    # 当前数据状态
    if st.session_state.current_data is not None:
        st.success(f"✅ 当前数据: {getattr(st.session_state, 'current_file', '未知文件')} "
                  f"({st.session_state.current_data.shape[0]}行 × {st.session_state.current_data.shape[1]}列)")
        
        # 数据预览
        with st.expander("📊 数据预览", expanded=False):
            st.dataframe(st.session_state.current_data.head(10))
            
            # 数据基本信息
            col1, col2 = st.columns(2)
            with col1:
                st.write("**列名:**")
                st.write(list(st.session_state.current_data.columns))
            with col2:
                st.write("**数据类型:**")
                st.write(st.session_state.current_data.dtypes.to_dict())
        
    else:
        st.info("👈 请先在侧边栏上传数据文件")
        
        # 显示使用说明
        with st.expander("📖 使用说明", expanded=True):
            st.markdown("""
            ### 🚀 如何使用智能数据分析助手 (基础版)
            
            1. **上传数据文件**
               - 在左侧边栏点击"选择数据文件"
               - 支持CSV、Excel、TXT、JSON格式
               - 上传后文件会自动保存，下次可直接使用
            
            2. **基础数据查看**
               - 上传数据后，可以进行基础查询
               - 支持的查询类型：
                 - "数据概览" - 显示基本统计信息
                 - "数据形状" - 显示行列数
                 - "列信息" - 显示列类型
                 - "缺失值" - 检查缺失数据
                 - "前几行" - 查看数据样本
            
            ### 🔧 升级到完整版
            
            要使用AI智能分析功能，请：
            1. 确保虚拟环境已激活
            2. 安装PandasAI: `pip install pandasai==2.2.15`
            3. 配置API密钥在.env文件中
            4. 使用 `streamlit_app.py` 而不是此基础版本
            """)
    
    # 聊天界面
    st.subheader("💬 基础查询")
    
    # 显示聊天历史
    display_chat_history()
    
    # 用户输入
    user_input = st.chat_input("请输入您的问题...")
    
    if user_input:
        # 检查是否有数据
        if st.session_state.current_data is None:
            st.error("请先上传数据文件")
            return
        
        # 添加用户消息
        st.session_state.chat_manager.add_message(
            "user", 
            user_input,
            f"{getattr(st.session_state, 'current_file', '未知文件')}"
        )
        
        # 显示用户消息
        with st.chat_message("user"):
            st.write(user_input)
            st.caption(f"📊 数据: {getattr(st.session_state, 'current_file', '未知文件')}")
        
        # 处理用户请求
        with st.chat_message("assistant"):
            with st.spinner("正在分析数据..."):
                result = perform_basic_analysis(st.session_state.current_data, user_input)
                st.write(result)
                st.session_state.chat_manager.add_message("assistant", result)
        
        st.rerun()

if __name__ == "__main__":
    main()
