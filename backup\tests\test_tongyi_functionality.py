#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问功能测试
测试通义千问与PandasAI的集成功能
"""

import os
import pandas as pd
from perfect_tongyi_integration import TongyiQianwenLLM

def test_tongyi_basic_functionality():
    """测试通义千问基本功能"""
    print("🧪 测试通义千问基本功能")
    print("="*50)
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY环境变量")
        return False
    
    print(f"✅ API密钥已配置: {api_key[:10]}...")
    
    # 创建测试数据
    data = {
        '产品': ['iPhone', 'iPad', 'MacBook', 'AirPods'],
        '销量': [1000, 800, 500, 1200],
        '价格': [6999, 4599, 14999, 1899],
        '类别': ['手机', '平板', '笔记本', '配件']
    }
    
    df = pd.DataFrame(data)
    print(f"✅ 测试数据创建成功: {df.shape}")
    print(df)
    print()
    
    # 创建LLM实例
    try:
        llm = TongyiQianwenLLM()
        print("✅ 通义千问LLM实例创建成功")
    except Exception as e:
        print(f"❌ LLM实例创建失败: {e}")
        return False
    
    # 测试基本查询
    test_queries = [
        "计算总销量",
        "找出价格最高的产品",
        "计算平均价格"
    ]
    
    success_count = 0
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 测试查询 {i}: {query}")
        try:
            # 调用LLM生成代码
            code = llm.call(query, df.to_string())
            print(f"📝 生成的代码:")
            print(code)
            
            # 执行代码
            print(f"🚀 执行结果:")
            exec(code)
            print("✅ 查询执行成功")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 查询执行失败: {e}")
        
        print("-" * 30)
    
    success_rate = (success_count / len(test_queries)) * 100
    print(f"\n📊 测试结果: {success_count}/{len(test_queries)} 成功 ({success_rate:.1f}%)")
    
    return success_count > 0

def test_smartdataframe_integration():
    """测试SmartDataframe集成"""
    print("\n🧪 测试SmartDataframe集成")
    print("="*50)
    
    try:
        from pandasai import SmartDataframe
        
        # 创建测试数据
        data = {
            '月份': ['1月', '2月', '3月', '4月'],
            '销售额': [10000, 12000, 15000, 13000],
            '成本': [6000, 7000, 8000, 7500]
        }
        
        df = pd.DataFrame(data)
        print(f"✅ 测试数据创建: {df.shape}")
        
        # 创建LLM
        llm = TongyiQianwenLLM()
        
        # 创建SmartDataframe
        smart_df = SmartDataframe(df, config={
            "llm": llm,
            "verbose": False,
            "conversational": False
        })
        
        print("✅ SmartDataframe创建成功")
        
        # 测试查询
        test_queries = [
            "计算总销售额",
            "哪个月份的利润最高？"
        ]
        
        success_count = 0
        
        for query in test_queries:
            print(f"\n🔍 SmartDataframe查询: {query}")
            try:
                result = smart_df.chat(query)
                print(f"✅ 查询结果: {result}")
                success_count += 1
            except Exception as e:
                print(f"❌ 查询失败: {e}")
        
        success_rate = (success_count / len(test_queries)) * 100
        print(f"\n📊 SmartDataframe测试结果: {success_count}/{len(test_queries)} 成功 ({success_rate:.1f}%)")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ SmartDataframe集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 通义千问功能测试开始")
    print("="*60)
    
    # 测试基本功能
    basic_success = test_tongyi_basic_functionality()
    
    # 测试SmartDataframe集成
    integration_success = test_smartdataframe_integration()
    
    # 总结
    print("\n" + "="*60)
    print("📋 测试总结")
    print("="*60)
    
    print(f"基本功能测试: {'✅ 通过' if basic_success else '❌ 失败'}")
    print(f"SmartDataframe集成: {'✅ 通过' if integration_success else '❌ 失败'}")
    
    if basic_success and integration_success:
        print("\n🎉 通义千问集成功能完全正常！")
        print("✅ 可以正常使用通义千问进行数据分析")
        print("✅ SmartDataframe集成工作正常")
        print("✅ 中文查询支持良好")
    elif basic_success:
        print("\n⚠️ 基本功能正常，但SmartDataframe集成有问题")
        print("建议检查SmartDataframe配置")
    else:
        print("\n❌ 通义千问集成存在问题")
        print("建议检查API密钥和网络连接")
    
    return basic_success and integration_success

if __name__ == "__main__":
    main()
