#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证脚本
确认所有图表闪退问题都已解决
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path
from metadata_manager import metadata_manager

def verify_metadata_state():
    """验证元数据状态"""
    print("🔍 验证元数据状态")
    print("=" * 50)
    
    # 检查表格列表
    tables = metadata_manager.get_all_tables()
    print(f"📊 当前表格数量: {len(tables)}")
    print(f"📊 表格列表: {tables}")
    
    # 检查是否还有问题字段
    problematic_fields = ['销售额_start', '销售额_end']
    found_issues = []
    
    for table_name in tables:
        table_metadata = metadata_manager.get_table_metadata(table_name)
        if table_metadata:
            for field in problematic_fields:
                if field in table_metadata.columns:
                    found_issues.append(f"{table_name}.{field}")
    
    if found_issues:
        print(f"❌ 仍有问题字段: {found_issues}")
        return False
    else:
        print("✅ 未发现问题字段")
        return True

def verify_sales_data():
    """验证sales_data表格"""
    print("\n🔍 验证sales_data表格")
    print("=" * 50)
    
    # 检查原始文件
    csv_path = Path("uploaded_files/sales_data.csv")
    if not csv_path.exists():
        print("❌ sales_data.csv文件不存在")
        return False
    
    df = pd.read_csv(csv_path)
    print(f"📁 原始文件: {csv_path}")
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 列名: {list(df.columns)}")
    
    # 检查是否包含问题字段
    problematic_fields = ['销售额_start', '销售额_end']
    has_problematic = any(field in df.columns for field in problematic_fields)
    
    if has_problematic:
        print("❌ 原始文件包含问题字段")
        return False
    else:
        print("✅ 原始文件正常")
    
    # 检查数据中是否有无穷大值
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    inf_count = np.isinf(df[numeric_cols]).sum().sum()
    nan_count = df[numeric_cols].isnull().sum().sum()
    
    print(f"📊 数值列: {list(numeric_cols)}")
    print(f"📊 无穷大值: {inf_count}")
    print(f"📊 NaN值: {nan_count}")
    
    if inf_count > 0:
        print("❌ 数据包含无穷大值")
        return False
    else:
        print("✅ 数据无异常值")
        return True

def test_chart_generation():
    """测试图表生成"""
    print("\n🔍 测试图表生成")
    print("=" * 50)
    
    try:
        # 加载数据
        df = pd.read_csv("uploaded_files/sales_data.csv")
        print(f"✅ 数据加载成功: {df.shape}")
        
        # 模拟图表数据处理
        chart_data = df.groupby('产品名称')['销售额'].sum()
        print(f"✅ 图表数据生成成功: {chart_data.shape}")
        
        # 检查图表数据是否包含异常值
        inf_count = np.isinf(chart_data).sum()
        nan_count = chart_data.isnull().sum()
        
        print(f"📊 图表数据无穷大值: {inf_count}")
        print(f"📊 图表数据NaN值: {nan_count}")
        
        if inf_count > 0 or nan_count > 0:
            print("❌ 图表数据包含异常值")
            return False
        else:
            print("✅ 图表数据正常")
            return True
            
    except Exception as e:
        print(f"❌ 图表生成测试失败: {e}")
        return False

def generate_final_report():
    """生成最终报告"""
    print("\n📊 最终验证报告")
    print("=" * 50)
    
    print("🎯 问题根源回顾:")
    print("1. ❌ 测试脚本创建了包含无穷大值的数据")
    print("2. ❌ 元数据推断系统将无穷大值保存为示例值")
    print("3. ❌ 污染的元数据导致图表渲染失败")
    print()
    
    print("🔧 解决方案实施:")
    print("1. ✅ 清理了所有包含问题字段的测试表格")
    print("2. ✅ 修复了元数据推断系统的示例值生成逻辑")
    print("3. ✅ 强制刷新了元数据缓存")
    print("4. ✅ 重新注册了正常的sales_data表格")
    print()
    
    print("🎉 预期结果:")
    print("- ✅ 控制台不再有Vega-Lite警告")
    print("- ✅ 图表稳定显示，不会闪退")
    print("- ✅ 元数据管理页面只显示正常表格")
    print("- ✅ 数据分析功能正常工作")

def main():
    """主验证函数"""
    print("🔍 最终验证 - 图表闪退问题解决确认")
    print("=" * 60)
    
    # 执行所有验证
    results = []
    
    # 1. 验证元数据状态
    result1 = verify_metadata_state()
    results.append(("元数据状态", result1))
    
    # 2. 验证sales_data表格
    result2 = verify_sales_data()
    results.append(("sales_data表格", result2))
    
    # 3. 测试图表生成
    result3 = test_chart_generation()
    results.append(("图表生成测试", result3))
    
    # 生成总结
    print(f"\n📊 验证总结")
    print("=" * 30)
    
    passed_tests = sum(1 for _, result in results if result)
    total_tests = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 验证通过")
    
    if passed_tests == total_tests:
        print("\n🎉 所有验证通过！图表闪退问题已彻底解决！")
        generate_final_report()
        
        print(f"\n🚀 现在您可以:")
        print("1. 重启Streamlit应用")
        print("2. 使用正常的sales_data.csv文件")
        print("3. 测试图表生成功能")
        print("4. 享受无闪退的数据分析体验！")
    else:
        print(f"\n⚠️ 部分验证失败，可能需要进一步调试")

if __name__ == "__main__":
    main()
