#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试饼图修复
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_pie_chart_generation():
    """测试饼图生成修复"""
    print("🧪 测试饼图生成修复")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据加载成功: {df.shape}")
    
    # 测试饼图查询
    queries = [
        "请为我生成销售金额分布的饼图",
        "生成各产品销售金额的饼图",
        "请创建一个显示产品销售金额比例的饼图",
        "为销售数据制作饼图"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n{i}️⃣ 测试查询: {query}")
        print("-" * 40)
        
        try:
            result = analyze_data(df, query, table_name="sales_data")
            
            if result and result.get('success'):
                print("✅ 查询成功")
                
                # 检查生成的代码
                code = result.get('code', '')
                print(f"📝 生成的代码:")
                print(code)
                
                # 检查关键要素
                has_save_chart = 'save_chart()' in code
                has_print = 'print(' in code
                has_pie = 'pie(' in code.lower()
                
                print(f"\n🔍 代码检查:")
                print(f"  📊 包含饼图: {'✅' if has_pie else '❌'}")
                print(f"  💾 包含save_chart(): {'✅' if has_save_chart else '❌'}")
                print(f"  📄 包含print输出: {'✅' if has_print else '❌'}")
                
                # 检查输出
                output = result.get('output', '')
                print(f"\n📊 执行输出:")
                if output.strip():
                    print(output)
                else:
                    print("  (无输出)")
                
                # 检查图表
                has_chart = result.get('has_chart', False)
                print(f"\n📈 图表生成: {'✅' if has_chart else '❌'}")
                
                if has_save_chart and has_chart and output.strip():
                    print("🎉 完整功能测试通过")
                else:
                    print("⚠️ 部分功能缺失")
                    
            else:
                print("❌ 查询失败")
                if result:
                    print(f"错误: {result.get('error', '未知错误')}")
                    
        except Exception as e:
            print(f"❌ 查询异常: {e}")
            import traceback
            traceback.print_exc()

def test_consecutive_pie_chart_queries():
    """测试连续饼图查询"""
    print("\n🔄 测试连续饼图查询")
    print("=" * 50)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 第一次查询：分析数据
    print("1️⃣ 第一次查询: 分析2024年各产品销售量")
    result1 = analyze_data(df, "分析2024年各产品销售量", table_name="sales_data")
    
    if result1 and result1.get('success'):
        print("✅ 第一次查询成功")
    else:
        print("❌ 第一次查询失败")
    
    print("\n" + "-" * 30)
    
    # 第二次查询：生成饼图
    print("2️⃣ 第二次查询: 请为我生成饼图")
    result2 = analyze_data(df, "请为我生成销售金额分布的饼图", table_name="sales_data")
    
    if result2 and result2.get('success'):
        print("✅ 第二次查询成功")
        
        code = result2.get('code', '')
        output = result2.get('output', '')
        has_chart = result2.get('has_chart', False)
        
        print(f"📝 生成的代码:")
        print(code)
        print(f"\n📊 执行输出:")
        print(output if output.strip() else "(无输出)")
        print(f"\n📈 图表生成: {'✅' if has_chart else '❌'}")
        
        # 检查是否满足用户期望
        has_save_chart = 'save_chart()' in code
        has_data_output = output.strip() != ''
        
        print(f"\n🎯 用户期望检查:")
        print(f"  1. 显示生成的代码: ✅")
        print(f"  2. 执行代码: ✅")
        print(f"  3. 显示数据分析结果: {'✅' if has_data_output else '❌'}")
        print(f"  4. 生成饼图可视化: {'✅' if has_chart else '❌'}")
        print(f"  5. 显示成功状态: ✅")
        
        if has_chart and has_data_output:
            print("\n🎉 所有用户期望都已满足！")
        else:
            print("\n⚠️ 部分用户期望未满足")
            
    else:
        print("❌ 第二次查询失败")
        if result2:
            print(f"错误: {result2.get('error', '未知错误')}")

if __name__ == "__main__":
    test_pie_chart_generation()
    test_consecutive_pie_chart_queries()
