#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据分析和图表生成
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from perfect_tongyi_integration import analyze_data

def setup_chinese_font():
    """设置中文字体"""
    try:
        # 获取系统可用字体
        font_list = [f.name for f in fm.fontManager.ttflist]
        
        # 按优先级尝试中文字体
        chinese_fonts = [
            'Microsoft YaHei',  # 微软雅黑
            'SimHei',           # 黑体
            'SimSun',           # 宋体
        ]
        
        # 找到第一个可用的中文字体
        available_font = None
        for font in chinese_fonts:
            if font in font_list:
                available_font = font
                break
        
        if available_font:
            plt.rcParams['font.sans-serif'] = [available_font]
            plt.rcParams['axes.unicode_minus'] = False
            print(f"✅ 中文字体设置成功: {available_font}")
        else:
            print("⚠️ 未找到中文字体")
            
    except Exception as e:
        print(f"⚠️ 字体设置失败: {e}")

def test_data_loading():
    """测试数据加载"""
    print("🧪 测试数据加载")
    print("=" * 50)
    
    try:
        # 尝试加载数据
        df = pd.read_csv('uploaded_files/sales_data.csv')
        print(f"✅ 数据加载成功: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        print(f"📊 数据预览:")
        print(df.head())
        print()
        
        return df
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def test_manual_analysis(df):
    """手动测试数据分析"""
    print("🧪 手动测试数据分析")
    print("=" * 50)
    
    if df is None:
        print("❌ 没有数据可分析")
        return
    
    # 1. 测试各产品总销售额
    print("1️⃣ 各产品总销售额:")
    product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
    print(product_sales)
    print()
    
    # 2. 测试数据类型
    print("2️⃣ 数据类型检查:")
    print(df.dtypes)
    print()
    
    # 3. 测试图表生成
    print("3️⃣ 测试图表生成:")
    try:
        setup_chinese_font()
        
        plt.figure(figsize=(10, 6))
        plt.bar(product_sales.index, product_sales.values)
        plt.title('各产品总销售额')
        plt.xlabel('产品名称')
        plt.ylabel('销售额')
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        # 保存图表
        plt.savefig('test_chart.png', dpi=300, bbox_inches='tight')
        print("✅ 图表生成成功: test_chart.png")
        plt.close()
        
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")

def test_ai_analysis(df):
    """测试AI分析"""
    print("🧪 测试AI分析")
    print("=" * 50)
    
    if df is None:
        print("❌ 没有数据可分析")
        return
    
    # 测试查询
    query = "分析2024年各产品总销售额，并生成柱状图"
    print(f"🔍 查询: {query}")
    print("-" * 30)
    
    try:
        result = analyze_data(df, query)
        
        if result and result.get('success'):
            print("✅ AI分析成功")
            print(f"📝 生成的代码:")
            print(result.get('code', '无代码'))
            print()
            
            print(f"📊 分析结果:")
            output = result.get('output', '')
            if output:
                print(output[:500])  # 显示前500字符
            else:
                print("无输出")
            print()
            
            if result.get('has_chart'):
                print("✅ 图表已生成")
            else:
                print("❌ 未生成图表")
                
        else:
            print("❌ AI分析失败")
            if result:
                print(f"错误: {result.get('error', '未知错误')}")
                
    except Exception as e:
        print(f"❌ AI分析异常: {e}")

if __name__ == "__main__":
    print("🔧 数据分析和图表生成测试")
    print("=" * 60)
    
    # 1. 测试数据加载
    df = test_data_loading()
    
    # 2. 手动分析测试
    test_manual_analysis(df)
    
    # 3. AI分析测试
    test_ai_analysis(df)
    
    print("\n" + "=" * 60)
    print("🎯 测试完成！")
    print("检查生成的 test_chart.png 文件以验证图表效果")
