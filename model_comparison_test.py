#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3-Coder vs qwen-plus 模型对比测试
"""

import pandas as pd
import sys
import time
sys.path.append('.')

from perfect_tongyi_integration import TongyiQianwenLLM, analyze_data

def test_model_comparison():
    """对比测试不同模型的代码生成效果"""
    
    # 创建测试数据
    data = {
        '地区': ['北京', '上海', '广州', '深圳', '杭州'],
        '销售额': [23400, 13980, 15300, 20700, 18500],
        '产品数量': [120, 89, 95, 110, 102]
    }
    df = pd.DataFrame(data)
    
    # 测试查询列表
    test_queries = [
        "请分析各地区的销售总额，生成饼图显示占比",
        "计算各地区的平均销售额，并生成柱状图",
        "找出销售额最高的地区，并显示详细信息",
        "分析销售额和产品数量的关系，生成散点图"
    ]
    
    models_to_test = ["qwen-plus", "qwen3-coder-plus"]
    
    print("🧪 模型对比测试开始")
    print("=" * 60)
    
    results = {}
    
    for model in models_to_test:
        print(f"\n🤖 测试模型: {model}")
        print("-" * 40)
        
        model_results = []
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📝 测试 {i}: {query}")
            
            try:
                # 创建LLM实例
                llm = TongyiQianwenLLM(model=model)
                
                start_time = time.time()
                result = analyze_data(df, query, "sales_data", use_metadata=True)
                end_time = time.time()
                
                # 分析结果质量
                code = result.get('code', '')
                success = result.get('success', False)
                uses_plotly = result.get('uses_plotly_native', False)
                uses_streamlit = result.get('uses_streamlit_native', False)
                
                # 代码质量评分
                quality_metrics = {
                    'has_import': any('import' in line for line in code.split('\n')),
                    'has_data_processing': any(keyword in code for keyword in ['groupby', 'sum()', 'mean()', 'max()', 'min()']),
                    'has_visualization': any(keyword in code for keyword in ['px.', 'st.', 'plt.']),
                    'has_output': 'print(' in code,
                    'code_length': len(code.split('\n')),
                    'response_time': end_time - start_time
                }
                
                quality_score = sum([
                    quality_metrics['has_import'],
                    quality_metrics['has_data_processing'], 
                    quality_metrics['has_visualization'],
                    quality_metrics['has_output']
                ])
                
                test_result = {
                    'query': query,
                    'success': success,
                    'uses_plotly': uses_plotly,
                    'uses_streamlit': uses_streamlit,
                    'quality_score': quality_score,
                    'code_length': quality_metrics['code_length'],
                    'response_time': quality_metrics['response_time'],
                    'code': code
                }
                
                model_results.append(test_result)
                
                print(f"  ✅ 成功: {success}")
                print(f"  📊 质量评分: {quality_score}/4")
                print(f"  ⏱️ 响应时间: {quality_metrics['response_time']:.2f}s")
                print(f"  📏 代码行数: {quality_metrics['code_length']}")
                
            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
                model_results.append({
                    'query': query,
                    'success': False,
                    'error': str(e)
                })
        
        results[model] = model_results
    
    # 生成对比报告
    print("\n" + "=" * 60)
    print("📊 模型对比报告")
    print("=" * 60)
    
    for model in models_to_test:
        model_results = results[model]
        successful_tests = [r for r in model_results if r.get('success', False)]
        
        if successful_tests:
            avg_quality = sum(r['quality_score'] for r in successful_tests) / len(successful_tests)
            avg_response_time = sum(r['response_time'] for r in successful_tests) / len(successful_tests)
            avg_code_length = sum(r['code_length'] for r in successful_tests) / len(successful_tests)
            
            print(f"\n🤖 {model}:")
            print(f"  ✅ 成功率: {len(successful_tests)}/{len(test_queries)} ({len(successful_tests)/len(test_queries)*100:.1f}%)")
            print(f"  ⭐ 平均质量评分: {avg_quality:.2f}/4")
            print(f"  ⏱️ 平均响应时间: {avg_response_time:.2f}s")
            print(f"  📏 平均代码行数: {avg_code_length:.1f}")
        else:
            print(f"\n🤖 {model}: ❌ 所有测试失败")
    
    # 推荐建议
    print(f"\n💡 推荐建议:")
    qwen_plus_results = results.get('qwen-plus', [])
    qwen3_coder_results = results.get('qwen3-coder-plus', [])
    
    qwen_plus_success = [r for r in qwen_plus_results if r.get('success', False)]
    qwen3_coder_success = [r for r in qwen3_coder_results if r.get('success', False)]
    
    if qwen3_coder_success and qwen_plus_success:
        qwen3_avg_quality = sum(r['quality_score'] for r in qwen3_coder_success) / len(qwen3_coder_success)
        qwen_plus_avg_quality = sum(r['quality_score'] for r in qwen_plus_success) / len(qwen_plus_success)
        
        if qwen3_avg_quality > qwen_plus_avg_quality:
            print("🎉 推荐使用 qwen3-coder-plus：代码质量更高")
        elif qwen3_avg_quality == qwen_plus_avg_quality:
            print("⚖️ 两个模型表现相当，可根据具体需求选择")
        else:
            print("🤔 qwen-plus 在某些方面表现更好")
    
    return results

if __name__ == "__main__":
    test_model_comparison()
