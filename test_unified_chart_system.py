#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一图表系统
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_unified_chart_system():
    """测试统一图表系统"""
    print("🎯 测试统一图表显示优先级系统")
    print("=" * 60)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据: {df.shape}")
    
    # 测试各种图表类型
    test_cases = [
        ("柱状图", "生成销售金额的柱状图", ["st.bar_chart", "st.plotly_chart", "matplotlib"]),
        ("折线图", "生成销售趋势的折线图", ["st.line_chart", "st.plotly_chart", "matplotlib"]),
        ("饼图", "生成销售金额分布的饼图", ["st.plotly_chart", "matplotlib"]),
        ("散点图", "生成销售金额和数量的散点图", ["st.scatter_chart", "st.plotly_chart", "matplotlib"]),
    ]
    
    results = []
    
    for chart_type, query, expected_priority in test_cases:
        print(f"\n📊 测试{chart_type}")
        print(f"  查询: {query}")
        print(f"  期望优先级: {' → '.join(expected_priority)}")
        
        try:
            result = analyze_data(df, query, table_name="sales_data")
            
            if result and result.get('success'):
                code = result.get('code', '')
                uses_streamlit_native = result.get('uses_streamlit_native', False)
                uses_plotly_native = result.get('uses_plotly_native', False)
                has_chart = result.get('has_chart', False)
                
                # 分析实际使用的技术
                actual_tech = analyze_chart_tech(code)
                
                print(f"  实际使用: {actual_tech}")
                print(f"  uses_streamlit_native: {uses_streamlit_native}")
                print(f"  uses_plotly_native: {uses_plotly_native}")
                print(f"  has_chart: {has_chart}")
                
                # 评估优先级遵循情况
                priority_score = evaluate_priority_compliance(actual_tech, expected_priority)
                print(f"  优先级遵循度: {priority_score}/5")
                
                # 检查显示逻辑
                display_logic_correct = check_display_logic(
                    uses_streamlit_native, uses_plotly_native, has_chart, actual_tech
                )
                print(f"  显示逻辑正确: {'✅' if display_logic_correct else '❌'}")
                
                results.append({
                    'chart_type': chart_type,
                    'actual_tech': actual_tech,
                    'priority_score': priority_score,
                    'display_logic_correct': display_logic_correct,
                    'uses_streamlit_native': uses_streamlit_native,
                    'uses_plotly_native': uses_plotly_native,
                    'has_chart': has_chart
                })
                
            else:
                print(f"  ❌ 查询失败")
                results.append({
                    'chart_type': chart_type,
                    'actual_tech': 'FAILED',
                    'priority_score': 0,
                    'display_logic_correct': False,
                    'uses_streamlit_native': False,
                    'uses_plotly_native': False,
                    'has_chart': False
                })
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")
            results.append({
                'chart_type': chart_type,
                'actual_tech': 'ERROR',
                'priority_score': 0,
                'display_logic_correct': False,
                'uses_streamlit_native': False,
                'uses_plotly_native': False,
                'has_chart': False
            })
    
    return results

def analyze_chart_tech(code):
    """分析代码使用的图表技术"""
    if 'st.bar_chart' in code:
        return "Streamlit原生st.bar_chart"
    elif 'st.line_chart' in code:
        return "Streamlit原生st.line_chart"
    elif 'st.scatter_chart' in code:
        return "Streamlit原生st.scatter_chart"
    elif 'st.area_chart' in code:
        return "Streamlit原生st.area_chart"
    elif 'st.plotly_chart' in code:
        return "Plotly原生"
    elif 'px.' in code or 'plotly' in code.lower():
        return "Plotly"
    elif 'plt.' in code or 'matplotlib' in code.lower():
        return "Matplotlib"
    else:
        return "未知"

def evaluate_priority_compliance(actual_tech, expected_priority):
    """评估优先级遵循情况"""
    if "Streamlit原生" in actual_tech and "st." in expected_priority[0]:
        return 5  # 完美：使用了最高优先级
    elif actual_tech == "Plotly原生" and len(expected_priority) >= 2:
        return 4  # 很好：使用了第二优先级
    elif actual_tech == "Plotly" and "st.plotly_chart" in expected_priority:
        return 3  # 一般：使用了Plotly但不是原生显示
    elif actual_tech == "Matplotlib":
        return 2  # 较差：使用了最低优先级
    else:
        return 1  # 差：不符合预期

def check_display_logic(uses_streamlit_native, uses_plotly_native, has_chart, actual_tech):
    """检查显示逻辑是否正确"""
    if "Streamlit原生" in actual_tech:
        return uses_streamlit_native and not uses_plotly_native and not has_chart
    elif actual_tech == "Plotly原生":
        return not uses_streamlit_native and uses_plotly_native and not has_chart
    elif actual_tech in ["Plotly", "Matplotlib"]:
        return not uses_streamlit_native and not uses_plotly_native and has_chart
    else:
        return False

def analyze_system_performance(results):
    """分析系统性能"""
    print(f"\n📊 统一图表系统性能分析")
    print("=" * 60)
    
    # 计算平均分数
    priority_scores = [r['priority_score'] for r in results if r['priority_score'] > 0]
    avg_priority = sum(priority_scores) / len(priority_scores) if priority_scores else 0
    
    display_logic_correct_count = sum(1 for r in results if r['display_logic_correct'])
    display_logic_rate = (display_logic_correct_count / len(results)) * 100
    
    print(f"📈 平均优先级遵循度: {avg_priority:.1f}/5")
    print(f"📈 显示逻辑正确率: {display_logic_rate:.1f}%")
    
    # 技术栈分布
    tech_distribution = {}
    for result in results:
        tech = result['actual_tech']
        tech_distribution[tech] = tech_distribution.get(tech, 0) + 1
    
    print(f"\n🔧 技术栈使用分布:")
    for tech, count in tech_distribution.items():
        percentage = (count / len(results)) * 100
        print(f"  {tech}: {count}次 ({percentage:.1f}%)")
    
    # 改进建议
    print(f"\n🛠️ 改进建议:")
    
    if avg_priority < 4:
        print("  📊 需要提高Streamlit原生组件使用率")
    
    if display_logic_rate < 90:
        print("  🔧 需要修复显示逻辑检测")
    
    streamlit_native_count = sum(1 for r in results if "Streamlit原生" in r['actual_tech'])
    if streamlit_native_count == 0:
        print("  ⚠️ 没有使用任何Streamlit原生组件")
    
    return avg_priority >= 4 and display_logic_rate >= 90

if __name__ == "__main__":
    results = test_unified_chart_system()
    system_performance_good = analyze_system_performance(results)
    
    print(f"\n🎯 系统评估结果:")
    if system_performance_good:
        print("🎉 统一图表系统工作良好！")
    else:
        print("⚠️ 统一图表系统需要进一步改进")
