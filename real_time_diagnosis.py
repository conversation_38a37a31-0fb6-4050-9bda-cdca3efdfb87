#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时问题诊断 - 基于实际运行状态
"""

import pandas as pd
import matplotlib.pyplot as plt
import warnings
import io
import sys
from contextlib import redirect_stdout, redirect_stderr

def diagnose_data_issue():
    """诊断数据显示问题"""
    print("🔍 诊断数据显示问题")
    print("=" * 50)
    
    # 加载实际使用的数据
    try:
        df = pd.read_csv('uploaded_files/sales_data.csv')
        print(f"✅ 数据加载成功: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        print(f"📊 数据类型:\n{df.dtypes}")
        print(f"\n📄 前5行数据:")
        print(df.head())
        print()
        
        return df
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def test_actual_ai_code(df):
    """测试实际AI生成的代码"""
    print("🧪 测试实际AI生成的代码")
    print("=" * 50)
    
    if df is None:
        print("❌ 没有数据可测试")
        return
    
    # 您提供的实际AI代码
    actual_code = """
product_sales = df.groupby('产品名称')['销售额'].sum().reset_index()
print(product_sales)
import matplotlib.pyplot as plt
plt.figure(figsize=(12, 8))
plt.bar(product_sales['产品名称'], product_sales['销售额'], alpha=0.8)
plt.title('2024年各产品总销售额', fontsize=16, fontweight='bold')
plt.xlabel('产品名称', fontsize=12)
plt.ylabel('总销售额', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.grid(True, alpha=0.3)
plt.tight_layout()
# save_chart()  # 注释掉以避免保存
plt.savefig('diagnosis_chart.png', dpi=300, bbox_inches='tight')
plt.close()
"""
    
    print("📝 执行的代码:")
    print(actual_code)
    print("\n🚀 执行结果:")
    
    # 捕获输出
    output_buffer = io.StringIO()
    error_buffer = io.StringIO()
    
    try:
        # 设置字体（测试是否有警告）
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 执行代码
        with redirect_stdout(output_buffer), redirect_stderr(error_buffer):
            exec(actual_code, {'df': df, 'plt': plt, 'pd': pd})
        
        # 获取输出
        output = output_buffer.getvalue()
        error_output = error_buffer.getvalue()
        
        print("📊 标准输出:")
        print(output)
        
        if error_output:
            print("⚠️ 错误/警告输出:")
            print(error_output)
        else:
            print("✅ 无错误或警告")
            
        print("✅ 代码执行成功")
        
        # 分析输出内容
        if output:
            print("\n🔍 输出分析:")
            lines = output.strip().split('\n')
            for i, line in enumerate(lines):
                print(f"  行{i+1}: {line}")
                
            # 检查数据格式
            if '产品名称' in output and '销售额' in output:
                print("✅ 输出包含正确的列名")
            else:
                print("❌ 输出列名可能有问题")
        
    except Exception as e:
        print(f"❌ 代码执行失败: {e}")

def test_font_warnings():
    """测试字体警告问题"""
    print("\n🔍 测试字体警告问题")
    print("=" * 50)
    
    # 重置警告过滤器
    warnings.resetwarnings()
    
    print("1️⃣ 不抑制警告的情况:")
    try:
        plt.figure(figsize=(8, 6))
        plt.text(0.5, 0.5, '测试中文字体：产品名称、销售额', fontsize=12, ha='center')
        plt.title('测试图表标题')
        plt.xlabel('X轴标签')
        plt.ylabel('Y轴标签')
        plt.savefig('font_test_1.png', dpi=150, bbox_inches='tight')
        plt.close()
        print("✅ 图表生成成功")
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")
    
    print("\n2️⃣ 抑制警告的情况:")
    # 抑制警告
    warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
    warnings.filterwarnings('ignore', message='.*Glyph.*missing from current font.*')
    
    try:
        plt.figure(figsize=(8, 6))
        plt.text(0.5, 0.5, '测试中文字体：产品名称、销售额', fontsize=12, ha='center')
        plt.title('测试图表标题')
        plt.xlabel('X轴标签')
        plt.ylabel('Y轴标签')
        plt.savefig('font_test_2.png', dpi=150, bbox_inches='tight')
        plt.close()
        print("✅ 图表生成成功（警告已抑制）")
    except Exception as e:
        print(f"❌ 图表生成失败: {e}")

def test_result_formatting():
    """测试结果格式化"""
    print("\n🔍 测试结果格式化")
    print("=" * 50)
    
    # 模拟实际的AI输出
    mock_output = """    产品名称    销售额
0   台式电脑  20200
1   平板电脑   6800
2     手机   9700
3    显示器   4700
4   智能手表   3800
5  笔记本电脑  25500
6     耳机   1700
7     键盘    650
8     鼠标    330"""

    print("📊 模拟的AI输出:")
    print(mock_output)
    
    # 测试格式化器
    try:
        from result_formatter import EnhancedResultFormatter
        
        mock_result = {
            'query': '分析2024年各产品总销售额',
            'output': mock_output,
            'success': True
        }
        
        output_type = EnhancedResultFormatter._detect_output_type(mock_output)
        print(f"\n🎯 检测到的输出类型: {output_type}")
        
        if output_type == 'tabular_data':
            print("✅ 识别为表格数据")
        elif output_type == 'series_data':
            print("✅ 识别为序列数据")
        else:
            print(f"⚠️ 识别为其他类型: {output_type}")
            
    except Exception as e:
        print(f"❌ 格式化器测试失败: {e}")

def check_streamlit_state():
    """检查Streamlit应用状态"""
    print("\n🔍 检查Streamlit应用状态")
    print("=" * 50)
    
    # 检查关键文件
    files_to_check = [
        'streamlit_app.py',
        'perfect_tongyi_integration.py',
        'result_formatter.py'
    ]
    
    for file in files_to_check:
        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"✅ {file}: {len(content)} 字符")
        except Exception as e:
            print(f"❌ {file}: 读取失败 - {e}")

if __name__ == "__main__":
    print("🔧 实时问题诊断")
    print("=" * 60)
    
    # 1. 诊断数据问题
    df = diagnose_data_issue()
    
    # 2. 测试实际AI代码
    test_actual_ai_code(df)
    
    # 3. 测试字体警告
    test_font_warnings()
    
    # 4. 测试结果格式化
    test_result_formatting()
    
    # 5. 检查应用状态
    check_streamlit_state()
    
    print("\n" + "=" * 60)
    print("🎯 诊断完成！")
    print("检查生成的图片文件以验证字体效果")
