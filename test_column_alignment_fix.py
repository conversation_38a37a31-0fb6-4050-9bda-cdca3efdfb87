#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试列对齐修复效果
"""

import sys
import os
import pandas as pd

# 添加当前目录到路径，以便导入本地模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_column_alignment_fix():
    """测试列对齐修复效果"""
    print("🧪 测试列对齐修复效果")
    print("=" * 50)
    
    # 模拟后端生成的正确DataFrame输出格式
    test_output = """  销售区域   产品名称  销售金额
0   上海   台式电脑  6200
1   北京  笔记本电脑  8500
2   广州   平板电脑  3200
3   杭州     耳机   450
4   深圳     手机  2800"""
    
    # 模拟result对象
    test_result = {
        'query': '分析2024年各地区的各个产品销售收入情况',
        'output': test_output,
        'success': True
    }
    
    print("📊 测试数据:")
    print(test_output)
    print("\n" + "=" * 50)
    
    try:
        # 导入修复后的结果格式化器
        from result_formatter import EnhancedResultFormatter
        
        print("✅ 成功导入EnhancedResultFormatter")
        
        # 测试输出类型检测
        output_type = EnhancedResultFormatter._detect_output_type(test_output)
        print(f"🔍 检测到的输出类型: {output_type}")
        
        # 测试数据解析
        if output_type == 'series_data':
            print("\n📋 测试数据解析逻辑:")
            
            # 手动调用解析逻辑进行测试
            lines = test_output.strip().split('\n')
            data_dict = {}
            header_line = None
            header_columns = []
            
            for i, line in enumerate(lines):
                line = line.strip()
                if line and not line.startswith('Name:') and not line.startswith('dtype:'):
                    # 检测并保存表头行
                    if i == 0 and any(header in line for header in ['产品名称', '地区', '类别', '销售员', '销售区域']):
                        header_line = line
                        header_columns = line.split()
                        print(f"📝 检测到表头: {header_columns}")
                        continue

                    # 检查是否是DataFrame格式（索引 + 多列数据）
                    parts = line.split()
                    if len(parts) >= 3 and parts[0].isdigit():
                        print(f"🔍 解析数据行: {parts}")
                        
                        # 动态解析多列数据
                        if header_columns and len(parts) >= len(header_columns):
                            # 根据表头确定列的含义
                            row_data = {}
                            for j, col_name in enumerate(header_columns):
                                if j + 1 < len(parts):  # +1 因为要跳过索引列
                                    row_data[col_name] = parts[j + 1]
                            
                            print(f"📊 行数据映射: {row_data}")
                            
                            # 寻找关键列（用作key和value）
                            key_col = None
                            value_col = None
                            
                            # 确定key列（区域、产品名称等）
                            for col in ['销售区域', '产品名称', '地区', '类别', '销售员']:
                                if col in row_data:
                                    key_col = col
                                    break
                            
                            # 确定value列（销售额、销量等数值）
                            for col in ['销售金额', '销售额', '销量', '数量', '金额']:
                                if col in row_data:
                                    value_col = col
                                    break
                            
                            print(f"🔑 Key列: {key_col}, Value列: {value_col}")
                            
                            # 如果找到了key和value列，添加到数据字典
                            if key_col and value_col:
                                try:
                                    key = row_data[key_col]
                                    value = float(row_data[value_col])
                                    data_dict[key] = value
                                    print(f"✅ 添加数据: {key} -> {value}")
                                except (ValueError, KeyError) as e:
                                    print(f"❌ 数据转换失败: {e}")
                                    continue
            
            print(f"\n📊 最终数据字典: {data_dict}")
            
            # 验证结果
            expected_data = {
                '上海': 6200.0,
                '北京': 8500.0,
                '广州': 3200.0,
                '杭州': 450.0,
                '深圳': 2800.0
            }
            
            print(f"\n🎯 期望结果: {expected_data}")
            
            if data_dict == expected_data:
                print("✅ 测试通过！列对齐问题已修复")
                return True
            else:
                print("❌ 测试失败！数据不匹配")
                print(f"差异: 实际={data_dict}, 期望={expected_data}")
                return False
        else:
            print(f"⚠️  输出类型不是series_data，而是: {output_type}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_streamlit_display():
    """测试Streamlit显示效果（模拟）"""
    print("\n🖥️  测试Streamlit显示效果")
    print("=" * 50)
    
    try:
        # 模拟Streamlit环境
        class MockStreamlit:
            @staticmethod
            def subheader(text):
                print(f"## {text}")
            
            @staticmethod
            def dataframe(df, **kwargs):
                print("📊 DataFrame显示:")
                print(df.to_string())
            
            @staticmethod
            def metric(label, value):
                print(f"📈 {label}: {value}")
            
            @staticmethod
            def columns(n):
                return [MockStreamlit() for _ in range(n)]
            
            def __enter__(self):
                return self
            
            def __exit__(self, *args):
                pass
        
        # 临时替换streamlit
        import sys
        sys.modules['streamlit'] = MockStreamlit()
        
        # 重新导入并测试
        from result_formatter import EnhancedResultFormatter
        
        test_output = """  销售区域   产品名称  销售金额
0   上海   台式电脑  6200
1   北京  笔记本电脑  8500
2   广州   平板电脑  3200
3   杭州     耳机   450
4   深圳     手机  2800"""
        
        test_result = {
            'query': '分析2024年各地区的各个产品销售收入情况',
            'output': test_output,
            'success': True
        }
        
        print("🎨 模拟Streamlit显示:")
        EnhancedResultFormatter.format_and_display_result(test_result)
        
        return True
        
    except Exception as e:
        print(f"❌ Streamlit显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 列对齐修复测试")
    print("=" * 60)
    
    # 测试1: 数据解析逻辑
    test1_result = test_column_alignment_fix()
    
    # 测试2: Streamlit显示效果
    test2_result = test_streamlit_display()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"✅ 数据解析测试: {'通过' if test1_result else '失败'}")
    print(f"✅ 显示效果测试: {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！列对齐问题已成功修复！")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
