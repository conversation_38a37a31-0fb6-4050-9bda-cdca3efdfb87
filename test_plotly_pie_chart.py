#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Plotly饼图功能
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_plotly_pie_chart():
    """测试Plotly饼图生成"""
    print("🥧 测试Plotly饼图生成")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据: {df.shape}")
    
    # 测试Plotly饼图查询
    queries = [
        "请为我生成销售金额分布的饼图",
        "用饼图显示各产品的销售金额占比",
        "创建一个饼图展示产品销售数据"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n{i}️⃣ 测试查询: {query}")
        print("-" * 40)
        
        try:
            result = analyze_data(df, query, table_name="sales_data")
            
            if result and result.get('success'):
                print("✅ 查询成功")
                
                code = result.get('code', '')
                output = result.get('output', '')
                has_chart = result.get('has_chart', False)
                
                print(f"\n📝 生成的代码:")
                print(code)
                
                # 检查代码类型
                is_plotly = 'plotly' in code.lower() or 'px.pie' in code
                is_matplotlib = 'plt.pie' in code
                has_streamlit = 'st.plotly_chart' in code
                has_legend = 'legend' in code.lower()
                
                print(f"\n🔍 代码分析:")
                print(f"  📊 使用Plotly: {'✅' if is_plotly else '❌'}")
                print(f"  📊 使用Matplotlib: {'✅' if is_matplotlib else '❌'}")
                print(f"  🎨 Streamlit原生显示: {'✅' if has_streamlit else '❌'}")
                print(f"  🏷️ 包含图例: {'✅' if has_legend else '❌'}")
                
                print(f"\n📊 执行输出:")
                print(output if output.strip() else "(无输出)")
                
                print(f"\n📈 图表生成: {'✅' if has_chart else '❌'}")
                
                # 评估结果
                if is_plotly and has_streamlit:
                    print("🎉 完美！使用Plotly原生Streamlit风格")
                elif is_matplotlib:
                    print("⚠️ 使用Matplotlib备用方案")
                else:
                    print("❓ 图表类型不明确")
                    
            else:
                print("❌ 查询失败")
                if result:
                    print(f"错误: {result.get('error', '未知错误')}")
                    
        except Exception as e:
            print(f"❌ 查询异常: {e}")
            import traceback
            traceback.print_exc()

def test_manual_plotly_code():
    """手动测试Plotly代码"""
    print(f"\n🧪 手动测试Plotly代码")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 手动执行Plotly代码
    try:
        import plotly.express as px
        
        product_sales = df.groupby('产品名称')['销售金额'].sum().reset_index()
        print("📊 销售金额分布:")
        print(product_sales)
        
        fig = px.pie(product_sales, values='销售金额', names='产品名称', 
                     title='销售金额分布饼图')
        fig.update_traces(textposition='inside', textinfo='percent+label')
        fig.update_layout(showlegend=True, 
                         legend=dict(orientation="v", yanchor="middle", y=0.5, 
                                   xanchor="left", x=1.01))
        
        print("✅ Plotly饼图创建成功")
        print(f"📊 图表类型: {type(fig)}")
        print(f"🏷️ 图例设置: 已启用")
        print(f"🎨 样式: Plotly原生风格")
        
        # 保存为HTML文件测试
        fig.write_html("test_plotly_pie.html")
        print("💾 已保存为test_plotly_pie.html")
        
        return True
        
    except ImportError:
        print("❌ Plotly不可用")
        return False
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        return False

def compare_chart_styles():
    """比较不同图表样式"""
    print(f"\n🎨 比较不同图表样式")
    print("=" * 50)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 1. Plotly风格
    print("1️⃣ Plotly风格:")
    print("   - 原生Streamlit集成")
    print("   - 自动图例")
    print("   - 交互功能")
    print("   - 现代化外观")
    
    # 2. Matplotlib风格
    print("\n2️⃣ Matplotlib风格:")
    print("   - 传统图表库")
    print("   - 需要手动图例")
    print("   - 静态图表")
    print("   - 需要样式调整")
    
    # 3. Streamlit原生组件风格
    print("\n3️⃣ Streamlit原生组件风格:")
    print("   - st.bar_chart: 简洁现代")
    print("   - st.line_chart: 简洁现代")
    print("   - st.pie_chart: 不存在")
    
    print(f"\n🏆 推荐: Plotly饼图")
    print("   理由: 最接近Streamlit原生组件的视觉风格")

if __name__ == "__main__":
    test_plotly_pie_chart()
    test_manual_plotly_code()
    compare_chart_styles()
