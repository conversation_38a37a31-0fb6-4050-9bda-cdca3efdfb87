#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源级别的清理器
在数据加载时就清理所有异常值，从根源解决问题
"""

import pandas as pd
import numpy as np
import re
import streamlit as st

class DataSourceCleaner:
    """数据源清理器 - 从根源解决Vega-Lite问题"""
    
    @staticmethod
    def clean_dataframe_at_source(df):
        """在数据源级别清理DataFrame"""
        print("🧹 执行数据源级别清理...")
        
        if df is None or df.empty:
            return df
        
        # 创建副本避免修改原始数据
        cleaned_df = df.copy()
        
        # 1. 清理列名中的特殊字符
        print("📝 清理列名...")
        original_columns = list(cleaned_df.columns)
        cleaned_df.columns = [re.sub(r'[^\w\u4e00-\u9fff]', '_', str(col)) for col in cleaned_df.columns]
        
        column_mapping = dict(zip(original_columns, cleaned_df.columns))
        changed_columns = {old: new for old, new in column_mapping.items() if old != new}
        if changed_columns:
            print(f"列名变更: {changed_columns}")
        
        # 2. 处理所有数值列中的异常值
        print("🔢 清理数值列异常值...")
        numeric_cols = cleaned_df.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            # 统计异常值
            inf_count = np.isinf(cleaned_df[col]).sum()
            nan_count = cleaned_df[col].isnull().sum()
            
            if inf_count > 0 or nan_count > 0:
                print(f"  列 '{col}': 无穷大值={inf_count}, NaN值={nan_count}")
                
                # 替换无穷大值为NaN
                cleaned_df[col] = cleaned_df[col].replace([np.inf, -np.inf], np.nan)
                
                # 用0填充NaN值（或者用均值，根据需要调整）
                cleaned_df[col] = cleaned_df[col].fillna(0)
                
                # 确保数据类型正确
                cleaned_df[col] = pd.to_numeric(cleaned_df[col], errors='coerce').fillna(0)
        
        # 3. 处理重复索引
        if cleaned_df.index.duplicated().any():
            print("🔄 处理重复索引...")
            cleaned_df = cleaned_df.reset_index(drop=True)
        
        # 4. 验证清理结果
        final_inf_count = np.isinf(cleaned_df.select_dtypes(include=[np.number])).sum().sum()
        final_nan_count = cleaned_df.isnull().sum().sum()
        
        print(f"✅ 清理完成: 剩余无穷大值={final_inf_count}, 剩余NaN值={final_nan_count}")
        
        return cleaned_df
    
    @staticmethod
    def apply_to_session_state():
        """应用清理到session_state中的数据"""
        if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
            print("🎯 清理session_state中的数据...")
            
            original_data = st.session_state.current_data
            print(f"原始数据形状: {original_data.shape}")
            print(f"原始列名: {list(original_data.columns)}")
            
            # 检查原始数据中的异常值
            numeric_cols = original_data.select_dtypes(include=[np.number]).columns
            total_inf = np.isinf(original_data[numeric_cols]).sum().sum()
            total_nan = original_data[numeric_cols].isnull().sum().sum()
            
            print(f"原始数据异常值: 无穷大值={total_inf}, NaN值={total_nan}")
            
            # 清理数据
            cleaned_data = DataSourceCleaner.clean_dataframe_at_source(original_data)
            
            # 更新session_state
            st.session_state.current_data = cleaned_data
            
            print("✅ session_state数据已更新")
            return True
        else:
            print("⚠️ session_state中没有数据")
            return False

def patch_streamlit_app():
    """修补streamlit_app.py以在数据加载时自动清理"""
    print("🔧 修补Streamlit应用...")
    
    # 检查是否需要清理当前数据
    if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
        # 检查数据是否包含异常值
        df = st.session_state.current_data
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        has_inf = np.isinf(df[numeric_cols]).sum().sum() > 0
        has_problematic_columns = any('销售额_start' in str(col) or '销售额_end' in str(col) for col in df.columns)
        
        if has_inf or has_problematic_columns:
            print("🚨 检测到需要清理的数据，执行自动清理...")
            DataSourceCleaner.apply_to_session_state()
            return True
    
    return False

def create_data_upload_wrapper():
    """创建数据上传包装器，在上传时自动清理"""
    
    def clean_uploaded_data(df, filename):
        """清理上传的数据"""
        print(f"📁 处理上传文件: {filename}")
        
        # 应用数据源清理
        cleaned_df = DataSourceCleaner.clean_dataframe_at_source(df)
        
        return cleaned_df
    
    return clean_uploaded_data

def emergency_data_fix():
    """紧急数据修复 - 立即清理当前数据"""
    print("🚨 执行紧急数据修复...")
    
    success = DataSourceCleaner.apply_to_session_state()
    
    if success:
        print("✅ 紧急修复完成！")
        print("💡 建议刷新页面以确保修复生效")
    else:
        print("❌ 紧急修复失败 - 没有找到数据")
    
    return success

def main():
    """主函数 - 用于测试"""
    print("🧪 数据源清理器测试")
    print("=" * 50)
    
    # 创建包含问题的测试数据
    test_data = {
        '产品名称@#$': ['iPhone', 'iPad', 'MacBook', 'AirPods'],
        '销售额_start': [1000000, np.inf, 2000000, 3000000],
        '销售额_end': [1500000, 2500000, -np.inf, 3500000],
        '销售额': [1200000, np.nan, 2200000, 3200000],
        '销量': [100, 200, 300, 400]
    }
    
    df = pd.DataFrame(test_data)
    
    print("原始测试数据:")
    print(df)
    print(f"列名: {list(df.columns)}")
    print(f"无穷大值: {np.isinf(df.select_dtypes(include=[np.number])).sum().sum()}")
    print(f"NaN值: {df.isnull().sum().sum()}")
    
    # 应用清理
    cleaned_df = DataSourceCleaner.clean_dataframe_at_source(df)
    
    print("\n清理后数据:")
    print(cleaned_df)
    print(f"列名: {list(cleaned_df.columns)}")
    print(f"无穷大值: {np.isinf(cleaned_df.select_dtypes(include=[np.number])).sum().sum()}")
    print(f"NaN值: {cleaned_df.isnull().sum().sum()}")
    
    # 验证特定问题字段
    problematic_fields = ['销售额_start', '销售额_end']
    for field in problematic_fields:
        if field in cleaned_df.columns:
            field_data = cleaned_df[field]
            has_inf = np.isinf(field_data).any()
            has_nan = field_data.isnull().any()
            print(f"字段 '{field}': 无穷大值={has_inf}, NaN值={has_nan}")
    
    print("\n✅ 数据源清理器测试完成")

if __name__ == "__main__":
    main()
