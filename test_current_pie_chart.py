#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试当前饼图生成效果
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_current_pie_chart():
    """测试当前饼图生成效果"""
    print("🔍 测试当前饼图生成效果")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据: {df.shape}")
    print(df.head())
    
    # 测试饼图生成
    query = "请为我生成销售金额分布的饼图，要包含图例"
    print(f"\n🔍 查询: {query}")
    
    result = analyze_data(df, query, table_name="sales_data")
    
    if result and result.get('success'):
        print("✅ 查询成功")
        
        code = result.get('code', '')
        print(f"\n📝 当前生成的代码:")
        print(code)
        
        # 分析代码问题
        print(f"\n🔍 代码分析:")
        has_legend = 'legend' in code.lower()
        has_pie = 'plt.pie(' in code
        has_save_chart = 'save_chart()' in code
        
        print(f"  📊 包含饼图: {'✅' if has_pie else '❌'}")
        print(f"  🏷️ 包含图例: {'✅' if has_legend else '❌'}")
        print(f"  💾 包含保存: {'✅' if has_save_chart else '❌'}")
        
        if not has_legend:
            print("  ⚠️ 问题：缺少图例")
        
        print(f"\n📊 输出:")
        print(result.get('output', ''))
        
    else:
        print("❌ 查询失败")
        if result:
            print(f"错误: {result.get('error', '')}")

if __name__ == "__main__":
    test_current_pie_chart()
