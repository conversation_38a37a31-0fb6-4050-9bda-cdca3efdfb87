#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度诊断脚本 - 排查前端显示表格但元数据为空的问题
"""

import sys
import json
import importlib
from pathlib import Path
import pandas as pd

def check_metadata_files():
    """检查所有元数据相关文件"""
    print("🔍 检查元数据文件")
    print("=" * 50)
    
    # 检查主配置文件
    config_file = Path("metadata_config/tables_metadata.json")
    if config_file.exists():
        with open(config_file, 'r', encoding='utf-8') as f:
            content = json.load(f)
        print(f"📄 主配置文件: {config_file}")
        print(f"   内容: {content}")
        print(f"   表格数: {len(content)}")
    else:
        print(f"❌ 主配置文件不存在: {config_file}")
    
    # 检查备份文件
    backup_dir = Path("metadata_config/backups")
    if backup_dir.exists():
        backup_files = list(backup_dir.glob("*.json"))
        print(f"\n💾 备份文件 ({len(backup_files)}个):")
        for backup_file in backup_files:
            try:
                with open(backup_file, 'r', encoding='utf-8') as f:
                    backup_content = json.load(f)
                tables_count = backup_content.get('tables_count', 0)
                print(f"   - {backup_file.name}: {tables_count} 个表格")
                if tables_count > 0:
                    tables = list(backup_content.get('tables_metadata', {}).keys())
                    print(f"     表格: {tables}")
            except Exception as e:
                print(f"   - {backup_file.name}: 读取失败 ({e})")
    
    # 检查其他可能的配置文件
    other_files = [
        "metadata_config/column_templates.json",
        "metadata_config/exports/metadata_report.json"
    ]
    
    for file_path in other_files:
        file_obj = Path(file_path)
        if file_obj.exists():
            try:
                with open(file_obj, 'r', encoding='utf-8') as f:
                    content = json.load(f)
                print(f"\n📄 其他配置文件: {file_path}")
                if 'tables' in content:
                    print(f"   包含表格: {list(content['tables'].keys())}")
                elif isinstance(content, dict) and len(content) > 0:
                    print(f"   内容键: {list(content.keys())}")
            except Exception as e:
                print(f"   读取失败: {e}")

def check_module_state():
    """检查模块状态"""
    print("\n🔍 检查模块状态")
    print("=" * 50)
    
    # 检查是否已导入metadata_manager
    if 'metadata_manager' in sys.modules:
        print("✅ metadata_manager模块已导入")
        
        # 重新导入模块
        importlib.reload(sys.modules['metadata_manager'])
        print("🔄 已重新加载metadata_manager模块")
        
        # 获取新的实例
        from metadata_manager import metadata_manager
        tables = metadata_manager.get_all_tables()
        print(f"📋 重新加载后的表格列表: {tables}")
        print(f"📊 表格数量: {len(tables)}")
        
        # 检查内部状态
        print(f"📁 配置目录: {metadata_manager.config_dir}")
        print(f"📄 配置文件: {metadata_manager.tables_config_file}")
        print(f"🗂️ 内存中的表格: {list(metadata_manager.tables_metadata.keys())}")
        
    else:
        print("❌ metadata_manager模块未导入")
        
        # 首次导入
        from metadata_manager import metadata_manager
        tables = metadata_manager.get_all_tables()
        print(f"📋 首次导入的表格列表: {tables}")

def check_streamlit_cache():
    """检查Streamlit缓存"""
    print("\n🔍 检查Streamlit缓存")
    print("=" * 50)
    
    try:
        import streamlit as st
        print("✅ Streamlit可用")
        
        # 检查缓存目录
        import os
        cache_dir = os.path.expanduser("~/.streamlit")
        if Path(cache_dir).exists():
            print(f"📁 Streamlit缓存目录: {cache_dir}")
            cache_files = list(Path(cache_dir).rglob("*"))
            print(f"📄 缓存文件数: {len(cache_files)}")
        else:
            print("📁 Streamlit缓存目录不存在")
            
    except ImportError:
        print("❌ Streamlit不可用")

def check_global_variables():
    """检查全局变量"""
    print("\n🔍 检查全局变量")
    print("=" * 50)
    
    # 检查当前模块的全局变量
    current_globals = globals()
    metadata_vars = [k for k in current_globals.keys() if 'metadata' in k.lower()]
    print(f"📋 当前模块中的元数据相关变量: {metadata_vars}")
    
    # 检查sys.modules中的相关模块
    metadata_modules = [k for k in sys.modules.keys() if 'metadata' in k.lower()]
    print(f"📋 已导入的元数据相关模块: {metadata_modules}")

def check_data_sources():
    """检查可能的数据源"""
    print("\n🔍 检查可能的数据源")
    print("=" * 50)
    
    # 检查上传文件目录
    upload_dir = Path("uploaded_files")
    if upload_dir.exists():
        files = list(upload_dir.glob("*"))
        print(f"📁 上传文件目录: {len(files)} 个文件")
        for file in files:
            print(f"   - {file.name}")
    else:
        print("📁 上传文件目录不存在")
    
    # 检查演示数据
    demo_file = Path("demo_data.csv")
    if demo_file.exists():
        print(f"📄 演示数据文件存在: {demo_file}")
        try:
            df = pd.read_csv(demo_file)
            print(f"   形状: {df.shape}")
            print(f"   列名: {list(df.columns)}")
        except Exception as e:
            print(f"   读取失败: {e}")
    else:
        print("📄 演示数据文件不存在")

def simulate_frontend_call():
    """模拟前端调用"""
    print("\n🔍 模拟前端调用")
    print("=" * 50)
    
    try:
        # 模拟前端的导入和调用
        from metadata_manager import metadata_manager
        from metadata_ui import MetadataUI
        
        print("✅ 成功导入前端模块")
        
        # 模拟获取表格列表的调用
        tables = metadata_manager.get_all_tables()
        print(f"📋 前端调用get_all_tables()结果: {tables}")
        
        # 检查元数据管理器的内部状态
        print(f"🗂️ 内存中的表格元数据: {list(metadata_manager.tables_metadata.keys())}")
        
        # 尝试重新加载配置
        metadata_manager._load_configurations()
        tables_after_reload = metadata_manager.get_all_tables()
        print(f"📋 重新加载配置后的表格: {tables_after_reload}")
        
    except Exception as e:
        print(f"❌ 模拟前端调用失败: {e}")
        import traceback
        traceback.print_exc()

def check_process_memory():
    """检查进程内存中的数据"""
    print("\n🔍 检查进程内存")
    print("=" * 50)
    
    try:
        import gc
        
        # 获取所有对象
        all_objects = gc.get_objects()
        
        # 查找可能包含表格数据的对象
        table_objects = []
        for obj in all_objects:
            if hasattr(obj, '__dict__'):
                obj_dict = obj.__dict__
                if any('customer' in str(v).lower() or 'order' in str(v).lower() or 'product' in str(v).lower() 
                       for v in obj_dict.values() if isinstance(v, (str, list, dict))):
                    table_objects.append(type(obj).__name__)
        
        print(f"📋 可能包含表格数据的对象类型: {set(table_objects)}")
        
    except Exception as e:
        print(f"❌ 检查进程内存失败: {e}")

def main():
    """主函数"""
    print("🔍 深度诊断 - 前端表格显示问题")
    print("=" * 60)
    
    # 1. 检查元数据文件
    check_metadata_files()
    
    # 2. 检查模块状态
    check_module_state()
    
    # 3. 检查Streamlit缓存
    check_streamlit_cache()
    
    # 4. 检查全局变量
    check_global_variables()
    
    # 5. 检查数据源
    check_data_sources()
    
    # 6. 模拟前端调用
    simulate_frontend_call()
    
    # 7. 检查进程内存
    check_process_memory()
    
    print("\n" + "=" * 60)
    print("🎯 诊断总结")
    print("如果前端仍显示表格，可能的原因:")
    print("1. 前端应用进程未重启，仍在使用旧的内存数据")
    print("2. 浏览器缓存问题")
    print("3. Streamlit session_state中有缓存数据")
    print("4. 有其他的数据源或配置文件")
    print("5. Python模块缓存问题")

if __name__ == "__main__":
    main()
