#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析项目的图形生成逻辑
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def analyze_chart_generation_logic():
    """分析图形生成逻辑"""
    print("🔍 分析项目的图形生成逻辑")
    print("=" * 70)
    
    print("📊 图形生成机制分析:")
    print()
    
    print("1️⃣ AI提示词中的图表指令:")
    print("-" * 40)
    print("在 perfect_tongyi_integration.py 第41-64行:")
    print("""
6. 如果需要生成图表，请遵循以下规则：
   - 导入: import matplotlib.pyplot as plt
   - 设置图表大小: plt.figure(figsize=(12, 8))
   - 必须包含标题: plt.title('图表标题', fontsize=16, fontweight='bold')
   - 必须包含轴标签: plt.xlabel('X轴标签', fontsize=12), plt.ylabel('Y轴标签', fontsize=12)
   - 如果X轴标签较长，使用: plt.xticks(rotation=45, ha='right')
   - 如果有多个数据系列，必须包含图例: plt.legend(fontsize=10)
   - 添加网格线: plt.grid(True, alpha=0.3)
   - 绘图后使用: plt.tight_layout() 然后 save_chart()
   - 示例代码结构:
     plt.figure(figsize=(12, 8))
     plt.bar(x_data, y_data, label='数据标签', alpha=0.8)  # 默认柱状图
     plt.title('图表标题', fontsize=16, fontweight='bold')
     plt.xlabel('X轴标签', fontsize=12)
     plt.ylabel('Y轴标签', fontsize=12)
     plt.xticks(rotation=45, ha='right')
     plt.legend(fontsize=10)
     plt.grid(True, alpha=0.3)
     plt.tight_layout()
     save_chart()
    """)
    
    print("2️⃣ 结果格式化器中的图表显示:")
    print("-" * 40)
    print("在 result_formatter.py 第476-490行:")
    print("""
# 如果数据不多，显示条形图
if len(df) <= 15:
    st.subheader("📊 可视化")
    try:
        chart_data = df.set_index(col1_name)[col2_name]
        chart_data = pd.to_numeric(chart_data, errors='coerce').fillna(0)
        if not chart_data.empty and chart_data.sum() != 0:
            st.bar_chart(chart_data)  # Streamlit内置柱状图
        else:
            st.info("数据无法生成图表")
    """)
    
    print("3️⃣ 图形生成的双重机制:")
    print("-" * 40)
    print("项目有两套图形生成机制:")
    print("A. AI生成的matplotlib图表 (通过AI提示词)")
    print("B. Streamlit内置的图表 (通过结果格式化器)")
    print()

def test_different_chart_types():
    """测试不同类型的图表生成"""
    print("🧪 测试不同类型的图表生成")
    print("=" * 70)
    
    # 加载测试数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 测试数据: {df.shape}")
    print(f"📋 列名: {list(df.columns)}")
    print()
    
    # 测试不同类型的查询，看AI会生成什么类型的图表
    test_queries = [
        {
            "query": "分析各产品的销售额，生成柱状图",
            "expected_chart": "柱状图 (bar chart)",
            "description": "明确要求柱状图"
        },
        {
            "query": "显示各产品销量的趋势图",
            "expected_chart": "线图 (line chart)",
            "description": "要求趋势图"
        },
        {
            "query": "分析各产品销售额的分布，生成饼图",
            "expected_chart": "饼图 (pie chart)",
            "description": "明确要求饼图"
        },
        {
            "query": "比较各产品的销售额和销量的关系，生成散点图",
            "expected_chart": "散点图 (scatter plot)",
            "description": "要求散点图"
        },
        {
            "query": "分析各产品销售额",
            "expected_chart": "默认图表类型",
            "description": "不明确指定图表类型"
        }
    ]
    
    results = []
    
    for i, test in enumerate(test_queries):
        print(f"{i+1}️⃣ 测试查询: {test['query']}")
        print(f"   期望图表: {test['expected_chart']}")
        print(f"   描述: {test['description']}")
        print("-" * 50)
        
        try:
            result = analyze_data(df, test['query'])
            
            if result and result.get('success'):
                code = result.get('code', '')
                print(f"📝 生成的代码:")
                print(code)
                print()
                
                # 分析代码中的图表类型
                chart_type = analyze_chart_type_in_code(code)
                print(f"🎯 检测到的图表类型: {chart_type}")
                
                results.append({
                    'query': test['query'],
                    'expected': test['expected_chart'],
                    'actual': chart_type,
                    'code': code,
                    'has_chart': result.get('has_chart', False)
                })
            else:
                print("❌ 查询失败")
                results.append({
                    'query': test['query'],
                    'expected': test['expected_chart'],
                    'actual': '失败',
                    'code': '',
                    'has_chart': False
                })
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append({
                'query': test['query'],
                'expected': test['expected_chart'],
                'actual': '异常',
                'code': '',
                'has_chart': False
            })
        
        print("=" * 70)
    
    return results

def analyze_chart_type_in_code(code):
    """分析代码中的图表类型"""
    if not code:
        return "无代码"
    
    code_lower = code.lower()
    
    # 检测matplotlib图表类型
    chart_types = {
        'plt.bar(': '柱状图 (bar)',
        'plt.barh(': '水平柱状图 (horizontal bar)',
        'plt.plot(': '线图 (line)',
        'plt.scatter(': '散点图 (scatter)',
        'plt.pie(': '饼图 (pie)',
        'plt.hist(': '直方图 (histogram)',
        'plt.boxplot(': '箱线图 (box plot)',
        'plt.heatmap(': '热力图 (heatmap)',
        'plt.subplot(': '子图 (subplot)',
        'sns.': 'Seaborn图表'
    }
    
    detected_types = []
    for pattern, chart_type in chart_types.items():
        if pattern in code_lower:
            detected_types.append(chart_type)
    
    if detected_types:
        return ', '.join(detected_types)
    elif 'plt.' in code_lower:
        return '未知matplotlib图表'
    else:
        return '无图表代码'

def analyze_default_behavior():
    """分析默认图表行为"""
    print("🔍 分析默认图表生成行为")
    print("=" * 70)
    
    print("📊 AI提示词分析:")
    print("- 示例代码中使用 plt.bar() - 默认为柱状图")
    print("- 没有明确的图表类型选择逻辑")
    print("- AI会根据查询内容和数据特征选择图表类型")
    print()
    
    print("🎯 图表类型决策逻辑:")
    print("1. 如果用户明确指定图表类型 -> 使用指定类型")
    print("2. 如果是分类数据对比 -> 倾向于柱状图")
    print("3. 如果是时间序列数据 -> 倾向于线图")
    print("4. 如果是比例关系 -> 倾向于饼图")
    print("5. 如果是相关性分析 -> 倾向于散点图")
    print("6. 默认情况 -> 柱状图 (基于示例代码)")
    print()
    
    print("📈 Streamlit内置图表:")
    print("- 结果格式化器固定使用 st.bar_chart()")
    print("- 只有柱状图一种类型")
    print("- 用于显示序列数据的快速可视化")

def summarize_chart_generation():
    """总结图表生成机制"""
    print("📋 图表生成机制总结")
    print("=" * 70)
    
    print("🎨 图表生成的两个层次:")
    print()
    
    print("1️⃣ AI生成的matplotlib图表:")
    print("   - 位置: perfect_tongyi_integration.py")
    print("   - 机制: 通过AI提示词指导生成matplotlib代码")
    print("   - 图表类型: 根据查询内容智能选择")
    print("   - 可能的类型:")
    print("     * 柱状图 (plt.bar) - 默认和最常用")
    print("     * 线图 (plt.plot) - 趋势分析")
    print("     * 散点图 (plt.scatter) - 相关性分析")
    print("     * 饼图 (plt.pie) - 比例分析")
    print("     * 直方图 (plt.hist) - 分布分析")
    print("     * 其他matplotlib支持的图表类型")
    print("   - 特点: 高度定制化，包含标题、轴标签、图例等")
    print()
    
    print("2️⃣ Streamlit内置图表:")
    print("   - 位置: result_formatter.py")
    print("   - 机制: 固定使用st.bar_chart()显示序列数据")
    print("   - 图表类型: 仅柱状图")
    print("   - 触发条件: 数据项目数量 <= 15")
    print("   - 特点: 简单快速，但定制化程度低")
    print()
    
    print("🎯 实际使用中的图表类型:")
    print("- 主要: 柱状图 (90%以上的情况)")
    print("- 次要: 线图 (时间序列或趋势分析)")
    print("- 少见: 饼图、散点图、直方图等")
    print("- 原因: AI提示词示例使用柱状图，且大多数数据分析场景适合柱状图")

def main():
    """主函数"""
    print("🎉 项目图形生成逻辑分析")
    print("=" * 80)
    
    # 1. 分析图形生成逻辑
    analyze_chart_generation_logic()
    
    # 2. 测试不同类型的图表
    print("\n")
    results = test_different_chart_types()
    
    # 3. 分析默认行为
    print("\n")
    analyze_default_behavior()
    
    # 4. 总结
    print("\n")
    summarize_chart_generation()
    
    # 5. 测试结果汇总
    print("\n📊 测试结果汇总:")
    print("=" * 70)
    for i, result in enumerate(results):
        print(f"{i+1}. 查询: {result['query'][:30]}...")
        print(f"   期望: {result['expected']}")
        print(f"   实际: {result['actual']}")
        print(f"   有图表: {result['has_chart']}")
        print()

if __name__ == "__main__":
    main()
