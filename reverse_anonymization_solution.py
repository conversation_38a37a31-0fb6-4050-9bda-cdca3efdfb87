#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反脱敏解决方案
解决脱敏后LLM结果中的名称映射问题
"""

import pandas as pd
import re
import json
from typing import Dict, Tuple, Any

class ReverseAnonymizer:
    """反脱敏器 - 将LLM结果中的脱敏名称还原为真实名称"""
    
    def __init__(self):
        self.mapping_cache = {}  # 存储脱敏映射关系
        self.reverse_mapping = {}  # 存储反向映射关系
        
    def register_mapping(self, original_value: str, anonymized_value: str, category: str = "general"):
        """
        注册映射关系
        
        Args:
            original_value: 原始值
            anonymized_value: 脱敏后的值
            category: 类别（product, employee, region等）
        """
        if category not in self.mapping_cache:
            self.mapping_cache[category] = {}
            self.reverse_mapping[category] = {}
        
        self.mapping_cache[category][original_value] = anonymized_value
        self.reverse_mapping[category][anonymized_value] = original_value
    
    def build_mapping_from_dataframes(self, df_original: pd.DataFrame, df_anonymized: pd.DataFrame):
        """
        从原始和脱敏后的DataFrame构建映射关系
        
        Args:
            df_original: 原始DataFrame
            df_anonymized: 脱敏后的DataFrame
        """
        # 检查每一列的映射关系
        for column in df_original.columns:
            if column in df_anonymized.columns:
                original_values = df_original[column].unique()
                anonymized_values = df_anonymized[column].unique()
                
                # 如果值发生了变化，说明这一列被脱敏了
                if not set(original_values) == set(anonymized_values):
                    self._build_column_mapping(
                        df_original[column], 
                        df_anonymized[column], 
                        column
                    )
    
    def _build_column_mapping(self, original_series: pd.Series, anonymized_series: pd.Series, column_name: str):
        """构建单列的映射关系"""
        # 创建映射字典
        mapping_dict = {}
        
        # 通过索引对应关系建立映射
        for idx in original_series.index:
            if idx in anonymized_series.index:
                original_val = original_series[idx]
                anonymized_val = anonymized_series[idx]
                
                if original_val != anonymized_val:
                    mapping_dict[original_val] = anonymized_val
        
        # 注册映射关系
        category = self._determine_category(column_name)
        for original, anonymized in mapping_dict.items():
            self.register_mapping(original, anonymized, category)
    
    def _determine_category(self, column_name: str) -> str:
        """根据列名确定类别"""
        column_lower = column_name.lower()
        
        if any(keyword in column_lower for keyword in ['产品', 'product', '商品']):
            return "product"
        elif any(keyword in column_lower for keyword in ['员工', 'employee', '销售员', '姓名']):
            return "employee"
        elif any(keyword in column_lower for keyword in ['地区', 'region', '城市', '地址']):
            return "region"
        elif any(keyword in column_lower for keyword in ['公司', 'company', '企业']):
            return "company"
        else:
            return "general"
    
    def reverse_anonymize_text(self, text: str, preserve_financial_anonymization: bool = True) -> Tuple[str, Dict]:
        """
        对文本进行反脱敏处理
        
        Args:
            text: 需要反脱敏的文本
            preserve_financial_anonymization: 是否保持财务数据的脱敏状态
            
        Returns:
            tuple: (反脱敏后的文本, 替换记录)
        """
        result_text = text
        replacement_log = {}
        
        # 按类别进行反脱敏
        for category, mappings in self.reverse_mapping.items():
            category_replacements = []
            
            for anonymized, original in mappings.items():
                # 使用正则表达式进行精确匹配和替换
                pattern = r'\b' + re.escape(anonymized) + r'\b'
                matches = re.findall(pattern, result_text)
                
                if matches:
                    result_text = re.sub(pattern, original, result_text)
                    category_replacements.append({
                        'from': anonymized,
                        'to': original,
                        'count': len(matches)
                    })
            
            if category_replacements:
                replacement_log[category] = category_replacements
        
        return result_text, replacement_log
    
    def create_anonymization_notice(self, replacement_log: Dict, financial_scale_factor: float = None) -> str:
        """
        创建脱敏说明
        
        Args:
            replacement_log: 替换记录
            financial_scale_factor: 财务数据缩放比例
            
        Returns:
            str: 脱敏说明文本
        """
        notices = []
        
        if replacement_log:
            notices.append("🔄 以下信息已从脱敏状态还原:")
            
            for category, replacements in replacement_log.items():
                category_names = {
                    'product': '产品名称',
                    'employee': '员工姓名', 
                    'region': '地区信息',
                    'company': '公司名称'
                }
                
                category_display = category_names.get(category, category)
                count = sum(r['count'] for r in replacements)
                notices.append(f"  • {category_display}: {count}处")
        
        if financial_scale_factor:
            notices.append(f"💰 财务数据已按{financial_scale_factor:.1%}比例缩放，相对关系保持不变")
        
        return "\n".join(notices) if notices else ""

class EnhancedAnonymizationSystem:
    """增强的脱敏系统 - 支持反脱敏"""
    
    def __init__(self):
        self.anonymizer = None  # DataAnonymizer实例
        self.reverse_anonymizer = ReverseAnonymizer()
        self.financial_scale_factor = None
        
    def anonymize_and_analyze(self, df: pd.DataFrame, query: str, llm_callable) -> Dict[str, Any]:
        """
        执行脱敏、分析和反脱敏的完整流程
        
        Args:
            df: 原始DataFrame
            query: 用户查询
            llm_callable: LLM调用函数
            
        Returns:
            dict: 包含原始结果和反脱敏结果的字典
        """
        # 1. 数据脱敏
        from data_anonymization_solution import DataAnonymizer
        self.anonymizer = DataAnonymizer()
        df_anonymized, anonymization_report = self.anonymizer.anonymize_dataframe(df)
        
        # 2. 构建映射关系
        self.reverse_anonymizer.build_mapping_from_dataframes(df, df_anonymized)
        
        # 3. 记录财务数据缩放比例
        if 'scaled_by_' in str(anonymization_report.get('anonymization_methods', {})):
            for method in anonymization_report['anonymization_methods'].values():
                if isinstance(method, str) and method.startswith('scaled_by_'):
                    self.financial_scale_factor = float(method.split('_')[-1])
                    break
        
        # 4. 调用LLM分析脱敏数据
        llm_result = llm_callable(query, df_anonymized.to_string())
        
        # 5. 反脱敏LLM结果
        if isinstance(llm_result, dict) and 'output' in llm_result:
            # 如果LLM返回的是结果字典
            original_output = llm_result['output']
            restored_output, replacement_log = self.reverse_anonymizer.reverse_anonymize_text(original_output)
            
            # 创建脱敏说明
            anonymization_notice = self.reverse_anonymizer.create_anonymization_notice(
                replacement_log, 
                self.financial_scale_factor
            )
            
            # 构建增强结果
            enhanced_result = llm_result.copy()
            enhanced_result['original_output'] = original_output  # 保留原始脱敏输出
            enhanced_result['output'] = restored_output  # 反脱敏后的输出
            enhanced_result['anonymization_notice'] = anonymization_notice
            enhanced_result['replacement_log'] = replacement_log
            enhanced_result['anonymization_used'] = True
            
            return enhanced_result
        
        elif isinstance(llm_result, str):
            # 如果LLM直接返回字符串
            restored_text, replacement_log = self.reverse_anonymizer.reverse_anonymize_text(llm_result)
            
            anonymization_notice = self.reverse_anonymizer.create_anonymization_notice(
                replacement_log,
                self.financial_scale_factor
            )
            
            return {
                'original_output': llm_result,
                'output': restored_text,
                'anonymization_notice': anonymization_notice,
                'replacement_log': replacement_log,
                'anonymization_used': True
            }
        
        return llm_result

def demo_reverse_anonymization():
    """演示反脱敏功能"""
    print("🔄 反脱敏功能完整演示")
    print("=" * 60)
    
    # 创建增强脱敏系统
    enhanced_system = EnhancedAnonymizationSystem()
    
    # 加载测试数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 模拟LLM调用函数
    def mock_llm_call(query, data_string):
        """模拟LLM调用"""
        return {
            'output': """根据数据分析结果：
1. 电脑类产品A的总销量最高，达到16台
2. 员工258的销售业绩最好，总销售额为1584元
3. 华北地区的销售表现最佳
4. 电脑类产品A在华北地区最受欢迎
5. 建议重点关注员工258的销售策略""",
            'success': True,
            'code': 'print("分析完成")'
        }
    
    # 执行完整流程
    query = "分析销售数据，找出表现最好的产品和员工"
    result = enhanced_system.anonymize_and_analyze(df, query, mock_llm_call)
    
    print("🤖 LLM原始输出（脱敏状态）:")
    print(result['original_output'])
    print()
    
    print("✨ 反脱敏后的输出:")
    print(result['output'])
    print()
    
    print("📋 脱敏说明:")
    print(result['anonymization_notice'])
    print()
    
    print("🔍 替换详情:")
    for category, replacements in result['replacement_log'].items():
        print(f"  {category}:")
        for replacement in replacements:
            print(f"    '{replacement['from']}' → '{replacement['to']}' ({replacement['count']}次)")

if __name__ == "__main__":
    demo_reverse_anonymization()
