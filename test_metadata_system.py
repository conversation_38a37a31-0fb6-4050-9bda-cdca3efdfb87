#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
元数据系统测试脚本
验证元数据功能对查询准确性的提升效果
"""

import pandas as pd
import os
import sys
from pathlib import Path
import time
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from metadata_manager import metadata_manager
from metadata_inference import metadata_inference
from perfect_tongyi_integration import analyze_data

def create_test_data():
    """创建测试数据"""
    print("📊 创建测试数据...")
    
    # 销售数据
    sales_data = {
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑', '手机', '智能手表'],
        '销售额': [8500, 6200, 3200, 4500, 1800],
        '销量': [5, 3, 8, 15, 12],
        '地区': ['北京', '上海', '广州', '深圳', '杭州'],
        '销售员': ['张三', '李四', '王五', '赵六', '钱七']
    }
    
    # 财务数据
    finance_data = {
        '月份': ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05'],
        '收入': [150000, 180000, 165000, 200000, 175000],
        '成本': [90000, 108000, 99000, 120000, 105000],
        '利润': [60000, 72000, 66000, 80000, 70000],
        '部门': ['销售部', '市场部', '技术部', '运营部', '财务部']
    }
    
    # 库存数据
    inventory_data = {
        '商品编号': ['P001', 'P002', 'P003', 'P004', 'P005'],
        '商品名称': ['iPhone 15', 'MacBook Pro', 'iPad Air', 'AirPods', 'Apple Watch'],
        '库存数量': [150, 80, 200, 300, 120],
        '单价': [6999, 14999, 4599, 1899, 3199],
        '供应商': ['苹果公司', '苹果公司', '苹果公司', '苹果公司', '苹果公司'],
        '仓库位置': ['A区', 'B区', 'A区', 'C区', 'B区']
    }
    
    return {
        'sales_data': pd.DataFrame(sales_data),
        'finance_data': pd.DataFrame(finance_data),
        'inventory_data': pd.DataFrame(inventory_data)
    }

def test_metadata_inference():
    """测试元数据推断功能"""
    print("\n🧠 测试智能元数据推断...")
    
    test_datasets = create_test_data()
    
    for table_name, df in test_datasets.items():
        print(f"\n📋 分析表格: {table_name}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
        # 执行推断
        start_time = time.time()
        metadata = metadata_inference.infer_table_metadata(table_name, df)
        inference_time = time.time() - start_time
        
        print(f"⏱️ 推断耗时: {inference_time:.3f}秒")
        print(f"🎯 业务领域: {metadata['business_domain']}")
        print(f"📊 置信度: {metadata['inference_info']['confidence_score']:.2f}")
        
        # 显示列推断结果
        print("📋 列推断结果:")
        for col_name, col_metadata in metadata['columns'].items():
            print(f"  - {col_name}: {col_metadata['inferred_type']} | {', '.join(col_metadata['tags'])}")
        
        if metadata['primary_keys']:
            print(f"🔑 推荐主键: {', '.join(metadata['primary_keys'])}")
        
        print("-" * 50)

def test_metadata_registration():
    """测试元数据注册功能"""
    print("\n📝 测试元数据注册...")
    
    test_datasets = create_test_data()
    
    for table_name, df in test_datasets.items():
        print(f"\n注册表格: {table_name}")
        
        # 注册表格
        table_metadata = metadata_manager.register_table(table_name, df, use_smart_inference=True)
        
        print(f"✅ 注册成功")
        print(f"业务领域: {table_metadata.business_domain}")
        print(f"列数量: {len(table_metadata.columns)}")
        print(f"主键: {table_metadata.primary_keys}")
        
        # 验证元数据
        validation_result = metadata_manager.validate_metadata(table_name)
        print(f"验证结果: {len(validation_result['errors'])} 错误, {len(validation_result['warnings'])} 警告")

def test_llm_context_generation():
    """测试LLM上下文生成"""
    print("\n🤖 测试LLM上下文生成...")
    
    test_datasets = create_test_data()
    
    for table_name, df in test_datasets.items():
        print(f"\n生成 {table_name} 的LLM上下文:")
        
        # 确保表格已注册
        if not metadata_manager.get_table_metadata(table_name):
            metadata_manager.register_table(table_name, df, use_smart_inference=True)
        
        # 生成上下文
        context = metadata_manager.generate_llm_context(table_name, df)
        
        print("📄 生成的上下文:")
        print(context[:500] + "..." if len(context) > 500 else context)
        print(f"上下文长度: {len(context)} 字符")
        print("-" * 50)

def test_query_accuracy():
    """测试查询准确性提升"""
    print("\n🎯 测试查询准确性...")
    
    # 使用销售数据进行测试
    df = create_test_data()['sales_data']
    table_name = 'sales_data'
    
    # 确保表格已注册
    if not metadata_manager.get_table_metadata(table_name):
        metadata_manager.register_table(table_name, df, use_smart_inference=True)
    
    # 测试查询
    test_queries = [
        "计算总销售额",
        "找出销量最高的产品",
        "按地区统计销售额",
        "哪个销售员业绩最好？",
        "显示每个产品的平均销售额"
    ]
    
    print("测试查询列表:")
    for i, query in enumerate(test_queries, 1):
        print(f"{i}. {query}")
    
    print("\n开始执行查询...")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🔍 查询 {i}: {query}")
        
        try:
            # 使用元数据增强的分析
            start_time = time.time()
            result = analyze_data(df, query, table_name=table_name, use_metadata=True)
            execution_time = time.time() - start_time
            
            print(f"⏱️ 执行时间: {execution_time:.3f}秒")
            print(f"✅ 成功: {result['success']}")
            
            if result['success'] and result.get('output'):
                output_preview = result['output'][:200].replace('\n', ' ')
                print(f"📄 输出预览: {output_preview}...")
            
            if result.get('error'):
                print(f"⚠️ 警告: {result['error']}")
            
            print(f"🎯 使用元数据: {result.get('metadata_used', False)}")
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
        
        print("-" * 30)

def test_metadata_export_import():
    """测试元数据导入导出"""
    print("\n📤 测试元数据导入导出...")
    
    # 导出元数据
    export_file = "test_metadata_export.json"
    success = metadata_manager.export_metadata(export_file)
    
    if success:
        print(f"✅ 元数据导出成功: {export_file}")
        
        # 检查文件大小
        file_size = os.path.getsize(export_file)
        print(f"📁 文件大小: {file_size} 字节")
        
        # 清空当前元数据
        original_metadata = metadata_manager.tables_metadata.copy()
        metadata_manager.tables_metadata.clear()
        print("🗑️ 已清空内存中的元数据")
        
        # 导入元数据
        success = metadata_manager.import_metadata(export_file)
        
        if success:
            print("✅ 元数据导入成功")
            print(f"📊 恢复了 {len(metadata_manager.tables_metadata)} 个表格的元数据")
        else:
            print("❌ 元数据导入失败")
            # 恢复原始数据
            metadata_manager.tables_metadata = original_metadata
        
        # 清理测试文件
        try:
            os.remove(export_file)
            print(f"🗑️ 已清理测试文件: {export_file}")
        except:
            pass
    else:
        print("❌ 元数据导出失败")

def generate_test_report():
    """生成测试报告"""
    print("\n📊 生成测试报告...")
    
    report = {
        "test_time": time.strftime("%Y-%m-%d %H:%M:%S"),
        "tables_registered": len(metadata_manager.get_all_tables()),
        "total_columns": sum(len(table.columns) for table in metadata_manager.tables_metadata.values()),
        "business_domains": list(set(table.business_domain for table in metadata_manager.tables_metadata.values())),
        "metadata_files": {
            "tables_config": metadata_manager.tables_config_file.exists(),
            "templates_config": metadata_manager.templates_config_file.exists()
        }
    }
    
    # 保存报告
    report_file = "metadata_test_report.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"📄 测试报告已保存: {report_file}")
    print("📊 测试摘要:")
    print(f"  - 注册表格数: {report['tables_registered']}")
    print(f"  - 总列数: {report['total_columns']}")
    print(f"  - 业务领域: {', '.join(report['business_domains'])}")
    
    return report

def main():
    """主测试函数"""
    print("🚀 开始元数据系统测试")
    print("=" * 60)
    
    try:
        # 1. 测试元数据推断
        test_metadata_inference()
        
        # 2. 测试元数据注册
        test_metadata_registration()
        
        # 3. 测试LLM上下文生成
        test_llm_context_generation()
        
        # 4. 测试查询准确性
        test_query_accuracy()
        
        # 5. 测试导入导出
        test_metadata_export_import()
        
        # 6. 生成测试报告
        report = generate_test_report()
        
        print("\n" + "=" * 60)
        print("🎉 元数据系统测试完成！")
        print("\n✅ 测试结果:")
        print("- 智能推断功能正常")
        print("- 元数据注册功能正常")
        print("- LLM上下文生成正常")
        print("- 查询准确性提升验证完成")
        print("- 导入导出功能正常")
        
        print(f"\n📊 系统状态:")
        print(f"- 已注册表格: {report['tables_registered']} 个")
        print(f"- 总列数: {report['total_columns']} 个")
        print(f"- 覆盖业务领域: {len(report['business_domains'])} 个")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
