#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制刷新元数据缓存
解决Streamlit应用中元数据管理器显示已删除表格的问题
"""

import json
from pathlib import Path
from metadata_manager import metadata_manager

def check_current_metadata_state():
    """检查当前元数据状态"""
    print("🔍 检查当前元数据状态")
    print("=" * 50)
    
    # 检查文件内容
    metadata_file = Path("metadata_config/tables_metadata.json")
    if metadata_file.exists():
        with open(metadata_file, 'r', encoding='utf-8') as f:
            file_data = json.load(f)
        
        print(f"📁 文件中的表格数量: {len(file_data)}")
        print(f"📁 文件中的表格: {list(file_data.keys())}")
    else:
        print("❌ 元数据文件不存在")
        return
    
    # 检查内存缓存
    memory_tables = metadata_manager.get_all_tables()
    print(f"💾 内存中的表格数量: {len(memory_tables)}")
    print(f"💾 内存中的表格: {memory_tables}")
    
    # 查找问题表格
    problematic_tables = []
    for table_name in memory_tables:
        if any(keyword in table_name.lower() for keyword in ['test', 'chart_test', 'problematic', 'vega_lite']):
            problematic_tables.append(table_name)
    
    if problematic_tables:
        print(f"❌ 发现问题表格: {problematic_tables}")
        return True
    else:
        print("✅ 未发现问题表格")
        return False

def force_reload_metadata():
    """强制重新加载元数据"""
    print("\n🔄 强制重新加载元数据")
    print("=" * 50)
    
    try:
        # 清空内存缓存
        print("🧹 清空内存缓存...")
        metadata_manager.tables_metadata.clear()
        
        # 重新加载配置
        print("📥 重新加载配置文件...")
        metadata_manager._load_configurations()
        
        # 检查重新加载后的状态
        reloaded_tables = metadata_manager.get_all_tables()
        print(f"✅ 重新加载完成，表格数量: {len(reloaded_tables)}")
        print(f"✅ 重新加载的表格: {reloaded_tables}")
        
        return True
        
    except Exception as e:
        print(f"❌ 强制重新加载失败: {e}")
        return False

def cleanup_remaining_test_tables():
    """清理剩余的测试表格"""
    print("\n🧹 清理剩余的测试表格")
    print("=" * 50)
    
    try:
        # 使用内置的清理方法
        result = metadata_manager.cleanup_test_tables()
        
        print(f"✅ 清理结果:")
        print(f"  删除的表格数量: {result['deleted']}")
        print(f"  剩余的表格数量: {result['remaining']}")
        print(f"  删除的表格: {result['deleted_tables']}")
        
        return result['deleted'] > 0
        
    except Exception as e:
        print(f"❌ 清理测试表格失败: {e}")
        return False

def refresh_metadata_from_files():
    """从文件系统刷新元数据"""
    print("\n🔄 从文件系统刷新元数据")
    print("=" * 50)
    
    try:
        # 使用内置的刷新方法
        result = metadata_manager.refresh_metadata("uploaded_files")
        
        print(f"✅ 刷新结果:")
        print(f"  扫描的文件数量: {result['scanned_files']}")
        print(f"  清理的孤立元数据: {result['orphaned_cleaned']}")
        print(f"  更新的表格: {result['tables_updated']}")
        print(f"  注册的新文件: {result['new_files_registered']}")
        print(f"  总表格数量: {result['total_tables']}")
        
        if result['orphaned_tables']:
            print(f"  清理的孤立表格: {result['orphaned_tables']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 刷新元数据失败: {e}")
        return False

def verify_final_state():
    """验证最终状态"""
    print("\n🔍 验证最终状态")
    print("=" * 50)
    
    # 检查最终的表格列表
    final_tables = metadata_manager.get_all_tables()
    print(f"📊 最终表格数量: {len(final_tables)}")
    print(f"📊 最终表格列表: {final_tables}")
    
    # 检查是否还有问题表格
    problematic_tables = []
    for table_name in final_tables:
        if any(keyword in table_name.lower() for keyword in ['test', 'chart_test', 'problematic', 'vega_lite']):
            problematic_tables.append(table_name)
    
    if problematic_tables:
        print(f"❌ 仍有问题表格: {problematic_tables}")
        return False
    else:
        print("✅ 所有问题表格已清理")
        return True

def create_streamlit_cache_clear_script():
    """创建Streamlit缓存清理脚本"""
    print("\n📝 创建Streamlit缓存清理脚本")
    print("=" * 50)
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit缓存清理脚本
在Streamlit应用中运行以清理元数据缓存
"""

import streamlit as st
from metadata_manager import metadata_manager

def clear_metadata_cache():
    """清理元数据缓存"""
    st.write("🔄 正在清理元数据缓存...")
    
    try:
        # 清空内存缓存
        metadata_manager.tables_metadata.clear()
        
        # 重新加载配置
        metadata_manager._load_configurations()
        
        # 获取最新的表格列表
        tables = metadata_manager.get_all_tables()
        
        st.success(f"✅ 元数据缓存已清理，当前表格数量: {len(tables)}")
        st.write("📊 当前表格列表:")
        for table in tables:
            st.write(f"  - {table}")
        
        return True
        
    except Exception as e:
        st.error(f"❌ 清理失败: {e}")
        return False

if __name__ == "__main__":
    st.title("🔄 元数据缓存清理工具")
    
    if st.button("清理元数据缓存"):
        clear_metadata_cache()
    
    st.write("---")
    st.write("💡 使用说明:")
    st.write("1. 点击上方按钮清理元数据缓存")
    st.write("2. 清理完成后，返回主页面")
    st.write("3. 检查元数据管理页面是否已更新")
'''
    
    script_path = Path("streamlit_cache_clear.py")
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✅ 脚本已创建: {script_path}")
    print("💡 您可以在Streamlit应用中运行此脚本来清理缓存")

def main():
    """主函数"""
    print("🔄 强制刷新元数据缓存")
    print("=" * 60)
    
    # 1. 检查当前状态
    has_issues = check_current_metadata_state()
    
    if not has_issues:
        print("\n✅ 元数据状态正常，无需刷新")
        return
    
    # 2. 强制重新加载
    print("\n🔧 开始修复...")
    reload_success = force_reload_metadata()
    
    if not reload_success:
        print("❌ 重新加载失败，尝试其他方法...")
    
    # 3. 清理测试表格
    cleanup_success = cleanup_remaining_test_tables()
    
    # 4. 从文件系统刷新
    refresh_success = refresh_metadata_from_files()
    
    # 5. 验证最终状态
    final_success = verify_final_state()
    
    # 6. 创建Streamlit缓存清理脚本
    create_streamlit_cache_clear_script()
    
    print("\n📊 修复总结:")
    print(f"✅ 重新加载: {'成功' if reload_success else '失败'}")
    print(f"✅ 清理测试表格: {'成功' if cleanup_success else '失败'}")
    print(f"✅ 刷新元数据: {'成功' if refresh_success else '失败'}")
    print(f"✅ 最终验证: {'成功' if final_success else '失败'}")
    
    if final_success:
        print("\n🎉 元数据缓存刷新完成！")
        print("\n💡 下一步:")
        print("1. 重启Streamlit应用")
        print("2. 或者在应用中运行 streamlit_cache_clear.py")
        print("3. 检查元数据管理页面是否已更新")
    else:
        print("\n⚠️ 部分修复失败，可能需要重启Streamlit应用")

if __name__ == "__main__":
    main()
