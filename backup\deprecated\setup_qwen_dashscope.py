"""
阿里通义千问 (Dashscope) API 密钥配置脚本
帮助用户安全地设置 Dashscope API 密钥并测试连接
"""

import os
import getpass

def setup_dashscope_api():
    print("🔧 阿里通义千问 (Dashscope) API 密钥配置")
    print("=" * 60)
    
    # 检查是否已经设置了 API 密钥
    current_key = os.getenv("DASHSCOPE_API_KEY")
    if current_key:
        print(f"✅ 检测到已设置的 API 密钥: {current_key[:10]}...")
        choice = input("是否要更新 API 密钥? (y/n): ").lower()
        if choice != 'y':
            print("保持当前设置")
            return True
    
    print("\n📋 获取 Dashscope API 密钥的步骤:")
    print("1. 访问 https://dashscope.console.aliyun.com/")
    print("2. 注册/登录阿里云账户")
    print("3. 在控制台中创建 API 密钥")
    print("4. 复制生成的 API 密钥")
    print()
    
    # 安全地输入 API 密钥
    api_key = getpass.getpass("请输入您的 Dashscope API 密钥 (输入时不会显示): ").strip()
    
    if not api_key:
        print("❌ API 密钥不能为空")
        return False
    
    # 设置环境变量
    os.environ["DASHSCOPE_API_KEY"] = api_key
    print("✅ API 密钥已设置到当前会话")
    
    # 测试 API 密钥
    print("\n🧪 测试 API 密钥连接...")
    try:
        from pandasai_litellm import LiteLLM
        
        # 创建 LiteLLM 实例
        llm = LiteLLM(
            model="dashscope/qwen-plus",
            temperature=0.1
        )
        
        print("✅ 通义千问模型配置成功!")
        print("✅ API 密钥验证通过!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装: pip install pandasai-litellm")
        return False
    except Exception as e:
        print(f"❌ API 密钥验证失败: {e}")
        print("请检查您的 API 密钥是否正确")
        return False

def show_permanent_setup_instructions():
    print("\n" + "=" * 60)
    print("🔒 永久设置 API 密钥 (推荐)")
    print("=" * 60)
    print("如果您想永久设置 API 密钥，可以选择以下方法之一:")
    print()
    print("方法 1: 系统环境变量 (推荐)")
    print("1. 按 Win + R，输入 'sysdm.cpl'")
    print("2. 点击 '环境变量' 按钮")
    print("3. 在 '用户变量' 中点击 '新建'")
    print("4. 变量名: DASHSCOPE_API_KEY")
    print("5. 变量值: 您的 API 密钥")
    print("6. 重启 PowerShell/命令提示符")
    print()
    print("方法 2: PowerShell 临时设置")
    print("$env:DASHSCOPE_API_KEY='your-api-key-here'")
    print()
    print("方法 3: CMD 临时设置")
    print("set DASHSCOPE_API_KEY=your-api-key-here")

def main():
    success = setup_dashscope_api()
    
    if success:
        print("\n🎉 配置完成!")
        print("现在您可以运行验证脚本:")
        print("  python test_qwen_dashscope.py")
        print("  python test_qwen_simple.py")
        
        show_permanent_setup_instructions()
    else:
        print("\n❌ 配置失败，请检查您的 API 密钥")
    
    return success

if __name__ == "__main__":
    main()
