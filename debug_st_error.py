#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 st 变量错误
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_st_error():
    """重现 st 变量错误"""
    print("🔍 调试 st 变量错误")
    print("=" * 40)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500, 20200, 15000, 9700, 8000],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    # 测试查询
    query = "请分析2024年各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 30)
    
    try:
        # 调用分析函数
        result = analyze_data(df, query, "sales_data", use_metadata=True)
        
        print("✅ 分析成功")
        print(f"代码: {result.get('code', 'N/A')}")
        print(f"输出: {result.get('output', 'N/A')}")
        print(f"错误: {result.get('error', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        print(f"错误类型: {type(e).__name__}")
        
        # 打印详细的错误信息
        import traceback
        print("详细错误信息:")
        traceback.print_exc()

def test_streamlit_import():
    """测试 Streamlit 导入"""
    print("\n🔍 测试 Streamlit 导入")
    print("=" * 30)
    
    try:
        import streamlit as st
        print("✅ Streamlit 导入成功")
        print(f"Streamlit 版本: {st.__version__}")
    except ImportError as e:
        print(f"❌ Streamlit 导入失败: {e}")
        print("这可能是问题的根源")

def test_mock_streamlit():
    """测试模拟 Streamlit 对象"""
    print("\n🔍 测试模拟 Streamlit 对象")
    print("=" * 30)
    
    class MockStreamlit:
        def __getattr__(self, name):
            def mock_method(*args, **kwargs):
                print(f"[模拟] st.{name}() 被调用")
                return None
            return mock_method
    
    st = MockStreamlit()
    
    # 测试调用
    try:
        st.subheader("测试标题")
        st.bar_chart([1, 2, 3])
        st.write("测试文本")
        print("✅ 模拟 Streamlit 对象工作正常")
    except Exception as e:
        print(f"❌ 模拟 Streamlit 对象失败: {e}")

def test_code_execution():
    """测试代码执行环境"""
    print("\n🔍 测试代码执行环境")
    print("=" * 30)
    
    # 模拟生成的代码
    test_code = """
import streamlit as st
import pandas as pd

# 使用Streamlit原生柱状图
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
print("各产品销售额:")
print(product_sales)

# 使用Streamlit原生柱状图显示
st.subheader("📊 各产品销售额对比")
st.bar_chart(product_sales)

# 显示具体数值
st.write("**详细数据:**")
for product, sales in product_sales.items():
    st.write(f"• {product}: ¥{sales:,.0f}")
"""

    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro'],
        '销售额': [25500, 20200, 15000]
    })
    
    # 创建执行环境
    try:
        import streamlit as st
        streamlit_available = True
    except ImportError:
        class MockStreamlit:
            def __getattr__(self, name):
                def mock_method(*args, **kwargs):
                    print(f"[模拟] st.{name}() 被调用")
                    return None
                return mock_method
        st = MockStreamlit()
        streamlit_available = False
    
    exec_globals = {
        'df': df,
        'pd': pd,
        'st': st,
        'print': print
    }
    
    try:
        exec(test_code, exec_globals)
        print("✅ 代码执行成功")
    except Exception as e:
        print(f"❌ 代码执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_streamlit_import()
    test_mock_streamlit()
    test_code_execution()
    test_st_error()
