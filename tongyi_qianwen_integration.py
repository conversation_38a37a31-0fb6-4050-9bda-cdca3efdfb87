#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PandasAI V2 + 通义千问集成示例
使用OpenAI兼容接口连接通义千问
"""

import os
import pandas as pd
from pandasai import SmartDataframe
from pandasai.llm import OpenAI
from dotenv import load_dotenv
import warnings
warnings.filterwarnings('ignore')

class TongyiQianwenConfig:
    """通义千问配置类"""
    
    # 通义千问模型配置
    MODELS = {
        'qwen-turbo': {
            'name': 'qwen-turbo',
            'description': '快速模型，适合简单查询',
            'max_tokens': 1500,
            'temperature': 0.1
        },
        'qwen-plus': {
            'name': 'qwen-plus',
            'description': '平衡模型，推荐使用',
            'max_tokens': 2000,
            'temperature': 0.1
        },
        'qwen-max': {
            'name': 'qwen-max',
            'description': '最强模型，适合复杂分析',
            'max_tokens': 2000,
            'temperature': 0.1
        }
    }
    
    # API配置
    BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    def __init__(self, model_name='qwen-plus'):
        """初始化配置"""
        # 加载环境变量
        load_dotenv()
        
        # 获取API密钥
        self.api_key = os.getenv('DASHSCOPE_API_KEY')
        if not self.api_key:
            raise ValueError("未找到DASHSCOPE_API_KEY环境变量，请检查.env文件配置")
        
        # 验证模型名称
        if model_name not in self.MODELS:
            raise ValueError(f"不支持的模型: {model_name}。支持的模型: {list(self.MODELS.keys())}")
        
        self.model_config = self.MODELS[model_name]
        self.model_name = model_name
        
        print(f"✅ 通义千问配置初始化成功")
        print(f"   模型: {self.model_name}")
        print(f"   描述: {self.model_config['description']}")
        print(f"   API密钥: {self.api_key[:10]}...{self.api_key[-4:]}")
    
    def create_llm(self):
        """创建通义千问LLM实例"""
        try:
            llm = OpenAI(
                api_token=self.api_key,
                base_url=self.BASE_URL,
                model=self.model_config['name'],
                temperature=self.model_config['temperature'],
                max_tokens=self.model_config['max_tokens']
            )
            
            print(f"✅ 通义千问LLM创建成功")
            return llm
            
        except Exception as e:
            print(f"❌ 通义千问LLM创建失败: {e}")
            raise
    
    def test_connection(self):
        """测试API连接"""
        print("🔍 测试通义千问API连接...")
        
        try:
            llm = self.create_llm()
            
            # 创建简单测试数据
            test_df = pd.DataFrame({
                'number': [1, 2, 3],
                'value': [10, 20, 30]
            })
            
            # 创建SmartDataframe进行测试
            smart_df = SmartDataframe(test_df, config={
                "llm": llm,
                "verbose": False,
                "conversational": False
            })
            
            # 执行简单查询测试
            result = smart_df.chat("What is the sum of the value column?")
            
            print("✅ API连接测试成功!")
            print(f"   测试查询结果: {result}")
            return True
            
        except Exception as e:
            print(f"❌ API连接测试失败: {e}")
            return False

def create_sample_data():
    """创建示例数据"""
    print("📊 创建示例数据...")
    
    # 销售数据示例
    sales_data = {
        '产品名称': ['笔记本电脑', '鼠标', '键盘', '显示器', '耳机', '音响', '摄像头', '打印机'],
        '销售数量': [150, 800, 600, 200, 400, 100, 250, 80],
        '单价': [5000, 50, 200, 1500, 300, 800, 400, 1200],
        '销售额': [750000, 40000, 120000, 300000, 120000, 80000, 100000, 96000],
        '类别': ['电脑', '配件', '配件', '电脑', '配件', '配件', '配件', '办公设备'],
        '销售月份': ['2024-01', '2024-01', '2024-01', '2024-02', '2024-02', '2024-02', '2024-03', '2024-03']
    }
    
    df = pd.DataFrame(sales_data)
    print("✅ 示例数据创建成功")
    print(df.head())
    print()
    
    return df

def demo_basic_queries(smart_df):
    """演示基本查询功能"""
    print("🔍 演示基本查询功能")
    print("=" * 50)
    
    queries = [
        "总销售额是多少？",
        "哪个产品的销售额最高？",
        "按类别统计销售额",
        "平均单价是多少？",
        "销售数量超过200的产品有哪些？"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n{i}. 查询: {query}")
        try:
            result = smart_df.chat(query)
            print(f"   结果: {result}")
        except Exception as e:
            print(f"   错误: {e}")
        print("-" * 30)

def demo_advanced_queries(smart_df):
    """演示高级查询功能"""
    print("\n🚀 演示高级查询功能")
    print("=" * 50)
    
    advanced_queries = [
        "创建一个显示各类别销售额的柱状图",
        "计算每个月的总销售额",
        "找出销售额低于平均值的产品",
        "按销售额排序显示前5名产品",
        "计算配件类产品的平均销售数量"
    ]
    
    for i, query in enumerate(advanced_queries, 1):
        print(f"\n{i}. 高级查询: {query}")
        try:
            result = smart_df.chat(query)
            print(f"   结果: {result}")
        except Exception as e:
            print(f"   错误: {e}")
        print("-" * 30)

def main():
    """主函数"""
    print("🎯 PandasAI V2 + 通义千问集成演示")
    print("=" * 60)
    
    try:
        # 1. 初始化通义千问配置
        print("\n1️⃣ 初始化通义千问配置")
        config = TongyiQianwenConfig(model_name='qwen-plus')
        
        # 2. 测试API连接
        print("\n2️⃣ 测试API连接")
        if not config.test_connection():
            print("❌ API连接失败，请检查配置")
            return
        
        # 3. 创建LLM实例
        print("\n3️⃣ 创建LLM实例")
        llm = config.create_llm()
        
        # 4. 创建示例数据
        print("\n4️⃣ 创建示例数据")
        df = create_sample_data()
        
        # 5. 创建SmartDataframe
        print("\n5️⃣ 创建SmartDataframe")
        smart_df = SmartDataframe(df, config={
            "llm": llm,
            "verbose": True,
            "conversational": False,
            "save_charts": True,
            "save_charts_path": "./charts/"
        })
        print("✅ SmartDataframe创建成功")
        
        # 6. 演示基本查询
        demo_basic_queries(smart_df)
        
        # 7. 演示高级查询
        demo_advanced_queries(smart_df)
        
        print("\n" + "=" * 60)
        print("🎉 通义千问集成演示完成!")
        print("\n✅ 集成成功特点:")
        print("- 使用OpenAI兼容接口连接通义千问")
        print("- 支持中文自然语言查询")
        print("- 支持复杂数据分析和可视化")
        print("- 配置简单，性能稳定")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
