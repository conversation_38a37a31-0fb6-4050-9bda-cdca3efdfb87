#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试提示词一致性修复效果
验证AI是否会生成Streamlit原生图表代码而不是matplotlib
"""

import pandas as pd
from enhanced_tongyi_integration import EnhancedTongyiQianwenLLM

def test_prompt_consistency():
    """测试提示词一致性"""
    print("🧪 测试提示词一致性修复效果")
    print("=" * 60)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500, 20200, 15000, 9700, 8000],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    # 创建增强版LLM实例
    llm = EnhancedTongyiQianwenLLM()
    llm.set_current_data("test_data", df)
    
    # 测试查询列表
    test_queries = [
        "请分析各产品销售额，用柱状图展示",
        "生成销售额的折线图",
        "创建产品销量的条形图",
        "用柱状图显示各产品的销售情况"
    ]
    
    results = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 测试 {i}: {query}")
        print("-" * 40)
        
        try:
            # 调用LLM生成代码
            code = llm.call(query, df.to_string())
            
            print("生成的代码:")
            print(code)
            print()
            
            # 分析代码内容
            analysis = analyze_generated_code(code)
            results.append({
                'query': query,
                'code': code,
                'analysis': analysis
            })
            
            # 显示分析结果
            print("📊 代码分析:")
            for key, value in analysis.items():
                status = "✅" if value else "❌"
                print(f"  {status} {key}")
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append({
                'query': query,
                'code': None,
                'analysis': {'error': str(e)}
            })
    
    # 总结结果
    print("\n" + "=" * 60)
    print("🎯 测试总结")
    print("=" * 60)
    
    success_count = 0
    total_count = len([r for r in results if r['code'] is not None])
    
    for result in results:
        if result['code'] and result['analysis'].get('uses_streamlit_native'):
            success_count += 1
    
    success_rate = (success_count / total_count * 100) if total_count > 0 else 0
    
    print(f"成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
    print()
    
    if success_rate >= 80:
        print("✅ 提示词修复成功！AI现在优先生成Streamlit原生图表代码")
    elif success_rate >= 50:
        print("⚠️ 部分成功，但仍需进一步优化提示词")
    else:
        print("❌ 提示词修复效果不佳，需要更强的约束")
    
    return results

def analyze_generated_code(code):
    """分析生成的代码"""
    if not code:
        return {'error': 'No code generated'}
    
    analysis = {
        'uses_streamlit_native': False,
        'uses_matplotlib': False,
        'uses_plotly': False,
        'has_syntax_error': False,
        'has_import_statements': False
    }
    
    # 检查Streamlit原生方法
    streamlit_methods = ['st.bar_chart', 'st.line_chart', 'st.scatter_chart', 'st.area_chart']
    analysis['uses_streamlit_native'] = any(method in code for method in streamlit_methods)
    
    # 检查matplotlib
    analysis['uses_matplotlib'] = 'import matplotlib.pyplot as plt' in code or 'plt.' in code
    
    # 检查plotly
    analysis['uses_plotly'] = 'import plotly' in code or 'px.' in code or 'st.plotly_chart' in code
    
    # 检查导入语句
    analysis['has_import_statements'] = 'import ' in code
    
    # 检查语法错误
    try:
        compile(code, '<string>', 'exec')
        analysis['has_syntax_error'] = False
    except SyntaxError:
        analysis['has_syntax_error'] = True
    
    return analysis

def test_specific_code_sample():
    """测试特定的代码示例"""
    print("\n🔍 测试特定代码示例分析")
    print("=" * 40)
    
    # 您提供的代码示例
    sample_code = """# 使用Streamlit原生柱状图
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
print("各产品销售额:")
print(product_sales)

# 使用Streamlit原生柱状图显示
st.subheader("📊 各产品销售额对比")
st.bar_chart(product_sales)

# 显示具体数值
st.write("**详细数据:**")
for product, sales in product_sales.items():
    st.write(f"• {product}: ¥{sales:,.0f}")"""

    print("代码示例:")
    print(sample_code)
    print()
    
    # 分析代码
    analysis = analyze_generated_code(sample_code)
    
    print("📊 代码分析结果:")
    print(f"✅ 使用Streamlit原生方法: {analysis['uses_streamlit_native']}")
    print(f"❌ 使用matplotlib: {analysis['uses_matplotlib']}")
    print(f"ℹ️ 使用plotly: {analysis['uses_plotly']}")
    print(f"✅ 语法正确: {not analysis['has_syntax_error']}")
    
    # 检查缺少的导入
    missing_imports = []
    if 'st.' in sample_code and 'import streamlit as st' not in sample_code:
        missing_imports.append('import streamlit as st')
    if 'df.' in sample_code and 'import pandas as pd' not in sample_code:
        missing_imports.append('import pandas as pd')
    
    if missing_imports:
        print(f"⚠️ 缺少导入语句: {', '.join(missing_imports)}")
    else:
        print("✅ 导入语句完整")
    
    print("\n💡 总结：这是一个完美的Streamlit原生图表代码示例！")

if __name__ == "__main__":
    test_prompt_consistency()
    test_specific_code_sample()
    
    print("\n" + "=" * 60)
    print("🎯 修复效果总结")
    print("=" * 60)
    print("问题：提示词与示例代码不一致，AI生成matplotlib代码")
    print()
    print("修复措施：")
    print("1. ✅ 移除了提示词中的matplotlib示例代码")
    print("2. ✅ 添加了强制性的禁止条款")
    print("3. ✅ 提供了清晰的Streamlit原生示例")
    print("4. ✅ 在两个LLM类中都进行了修复")
    print()
    print("预期效果：")
    print("- AI现在会优先生成Streamlit原生图表代码")
    print("- 图表显示问题应该得到根本解决")
    print("- 代码执行更可靠，渲染更快速")
