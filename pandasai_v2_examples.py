#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PandasAI V2 使用示例和配置
"""

import os
import pandas as pd
from pandasai import SmartDataframe
from pandasai.llm import OpenAI

# ===== 基本使用示例 =====

def basic_example():
    """基本使用示例"""
    # 创建示例数据
    data = {
        'Name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'Age': [25, 30, 35, 28],
        'Salary': [50000, 60000, 70000, 55000],
        'Department': ['IT', 'Finance', 'IT', 'HR']
    }
    
    df = pd.DataFrame(data)
    
    # 配置LLM (这里使用OpenAI作为示例)
    llm = OpenAI(
        api_token="your-openai-api-key",  # 替换为您的API密钥
        model="gpt-3.5-turbo",
        temperature=0.1
    )
    
    # 创建SmartDataframe
    smart_df = SmartDataframe(df, config={"llm": llm})
    
    # 自然语言查询
    result = smart_df.chat("What is the average salary?")
    print(result)

# ===== OpenAI配置示例 =====

def openai_config_example():
    """OpenAI配置示例"""
    # 方法1: 使用环境变量
    os.environ['OPENAI_API_KEY'] = 'your-api-key-here'
    
    llm = OpenAI(
        model="gpt-3.5-turbo",
        temperature=0.1,
        max_tokens=1000
    )
    
    # 方法2: 直接指定API密钥
    llm = OpenAI(
        api_token="your-api-key-here",
        model="gpt-4",
        temperature=0.2,
        max_tokens=1500
    )
    
    return llm

# ===== 通义千问配置示例 =====

def tongyi_qianwen_config_example():
    """通义千问配置示例 (使用OpenAI兼容接口)"""
    from dotenv import load_dotenv

    # 加载.env文件
    load_dotenv()

    # 方法1: 使用环境变量 (推荐)
    llm = OpenAI(
        api_token=os.getenv('DASHSCOPE_API_KEY'),
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        model="qwen-plus",
        temperature=0.1,
        max_tokens=2000
    )

    # 方法2: 直接指定API密钥
    llm = OpenAI(
        api_token="your-dashscope-api-key",
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        model="qwen-max",  # 或 qwen-plus, qwen-turbo
        temperature=0.1,
        max_tokens=2000
    )

    print("✅ 通义千问LLM配置成功")
    print(f"   模型: {llm.model}")
    print(f"   API端点: {llm.base_url}")

    return llm

# ===== 其他LLM配置示例 =====

def other_llm_examples():
    """其他LLM配置示例"""
    
    # Azure OpenAI
    from pandasai.llm import AzureOpenAI
    
    azure_llm = AzureOpenAI(
        api_token="your-azure-api-key",
        azure_endpoint="https://your-resource.openai.azure.com/",
        api_version="2023-05-15",
        deployment_name="your-deployment-name"
    )
    
    # Google PaLM
    from pandasai.llm import GooglePalm
    
    palm_llm = GooglePalm(
        api_key="your-palm-api-key"
    )
    
    # Hugging Face
    from pandasai.llm import HuggingFaceTextGen
    
    hf_llm = HuggingFaceTextGen(
        api_token="your-hf-token",
        model_url="your-model-endpoint"
    )
    
    return azure_llm, palm_llm, hf_llm

# ===== 完整使用示例 =====

def complete_example():
    """完整使用示例"""
    
    # 1. 准备数据
    data = {
        'Product': ['Laptop', 'Mouse', 'Keyboard', 'Monitor', 'Headphones'],
        'Price': [1200, 25, 75, 300, 150],
        'Category': ['Electronics', 'Accessories', 'Accessories', 'Electronics', 'Accessories'],
        'Stock': [50, 200, 100, 30, 80],
        'Rating': [4.5, 4.2, 4.0, 4.8, 4.3]
    }
    
    df = pd.DataFrame(data)
    print("原始数据:")
    print(df)
    print()
    
    # 2. 配置LLM
    llm = OpenAI(
        api_token=os.getenv('OPENAI_API_KEY', 'your-api-key'),
        model="gpt-3.5-turbo",
        temperature=0.1,
        max_tokens=1000
    )
    
    # 3. 创建SmartDataframe
    smart_df = SmartDataframe(
        df, 
        config={
            "llm": llm,
            "verbose": True,  # 显示详细信息
            "conversational": False,  # 非对话模式
            "save_charts": True,  # 保存图表
            "save_charts_path": "./charts/"  # 图表保存路径
        }
    )
    
    # 4. 执行各种查询
    queries = [
        "What is the most expensive product?",
        "Show me the average price by category",
        "Which products have rating above 4.5?",
        "Create a bar chart showing price by product",
        "What is the total value of stock (price * stock)?"
    ]
    
    for query in queries:
        print(f"查询: {query}")
        try:
            result = smart_df.chat(query)
            print(f"结果: {result}")
        except Exception as e:
            print(f"错误: {e}")
        print("-" * 50)

# ===== 配置选项说明 =====

def config_options_explanation():
    """配置选项说明"""
    
    config_options = {
        # LLM配置
        "llm": None,  # LLM实例
        
        # 输出配置
        "verbose": True,  # 显示详细信息
        "conversational": False,  # 对话模式
        
        # 图表配置
        "save_charts": True,  # 保存图表
        "save_charts_path": "./charts/",  # 图表保存路径
        
        # 缓存配置
        "enable_cache": True,  # 启用缓存
        
        # 安全配置
        "enforce_privacy": False,  # 强制隐私保护
        
        # 自定义配置
        "custom_whitelisted_dependencies": [],  # 自定义白名单依赖
        
        # 其他配置
        "max_retries": 3,  # 最大重试次数
        "use_error_correction_framework": True,  # 使用错误纠正框架
    }
    
    return config_options

# ===== V2与V3的主要差异 =====

def v2_vs_v3_differences():
    """V2与V3的主要差异说明"""
    
    differences = {
        "导入方式": {
            "V2": "from pandasai import SmartDataframe",
            "V3": "import pandasai as pai"
        },
        
        "LLM配置": {
            "V2": "SmartDataframe(df, config={'llm': llm})",
            "V3": "pai.config.set({'llm': llm})"
        },
        
        "查询方式": {
            "V2": "smart_df.chat('query')",
            "V3": "df.chat('query')  # pai.DataFrame"
        },
        
        "扩展包": {
            "V2": "内置支持多种LLM",
            "V3": "需要安装扩展包 (pandasai-openai, pandasai-litellm等)"
        },
        
        "架构": {
            "V2": "SmartDataframe为核心",
            "V3": "语义层 + 自然语言层架构"
        }
    }
    
    return differences

def tongyi_qianwen_working_example():
    """通义千问完整工作示例"""
    from dotenv import load_dotenv

    print("🚀 通义千问完整工作示例")
    print("=" * 40)

    try:
        # 加载环境变量
        load_dotenv()

        # 检查API密钥
        api_key = os.getenv('DASHSCOPE_API_KEY')
        if not api_key:
            print("❌ 未找到DASHSCOPE_API_KEY，请检查.env文件")
            return

        print(f"✅ API密钥已加载: {api_key[:10]}...{api_key[-4:]}")

        # 配置通义千问LLM
        llm = OpenAI(
            api_token=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            model="qwen-plus",
            temperature=0.1,
            max_tokens=2000
        )

        print("✅ 通义千问LLM配置成功")

        # 创建中文示例数据
        data = {
            '产品': ['iPhone', 'iPad', 'MacBook', 'AirPods', 'Apple Watch'],
            '价格': [6999, 3999, 9999, 1299, 2999],
            '销量': [1000, 800, 500, 1500, 1200],
            '类别': ['手机', '平板', '电脑', '配件', '配件'],
            '评分': [4.8, 4.6, 4.9, 4.7, 4.5]
        }

        df = pd.DataFrame(data)
        print("✅ 示例数据创建成功")
        print(df)

        # 创建SmartDataframe
        smart_df = SmartDataframe(df, config={
            "llm": llm,
            "verbose": True,
            "conversational": False
        })

        print("\n✅ SmartDataframe创建成功")

        # 中文查询示例
        queries = [
            "哪个产品的价格最高？",
            "计算总销售额（价格×销量）",
            "评分最高的产品是什么？",
            "配件类产品的平均价格是多少？"
        ]

        for i, query in enumerate(queries, 1):
            print(f"\n{i}. 查询: {query}")
            try:
                result = smart_df.chat(query)
                print(f"   结果: {result}")
            except Exception as e:
                print(f"   错误: {e}")

        print("\n🎉 通义千问集成演示完成!")

    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("PandasAI V2 配置和使用示例")
    print("=" * 50)

    # 显示版本差异
    differences = v2_vs_v3_differences()
    print("V2与V3主要差异:")
    for key, value in differences.items():
        print(f"\n{key}:")
        if isinstance(value, dict):
            for k, v in value.items():
                print(f"  {k}: {v}")
        else:
            print(f"  {value}")

    print("\n" + "=" * 50)
    print("运行通义千问工作示例:")
    tongyi_qianwen_working_example()
