#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度图表修复测试
基于Streamlit技术文档的根本性修复验证
"""

import pandas as pd
import numpy as np
from perfect_tongyi_integration import analyze_data

def test_deep_chart_fix():
    """测试深度图表修复"""
    print("🔬 深度图表修复测试")
    print("=" * 60)
    
    # 创建包含潜在问题的测试数据
    problematic_data = {
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500.0, 20200.0, 15000.0, 9700.0, 8000.0],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    }
    
    # 添加一些可能导致问题的数据
    problematic_data['特殊字符列'] = ['测试@#$', '数据%^&', '内容*()', '符号[]{}', '特殊<>']
    
    df = pd.DataFrame(problematic_data)
    
    print("测试数据（包含潜在问题）:")
    print(df)
    print(f"数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    print()
    
    # 测试查询列表
    test_queries = [
        "请分析各产品销售额，用柱状图展示",
        "生成销售额的折线图",
        "分析产品销量分布"
    ]
    
    results = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"🧪 测试 {i}: {query}")
        print("-" * 40)
        
        try:
            result = analyze_data(df, query, f"test_data_{i}", use_metadata=True)
            
            if result.get('success'):
                print("✅ 分析成功")
                
                # 分析生成的代码
                code = result.get('code', '')
                analysis = analyze_deep_fix_code(code)
                
                print("📊 深度修复分析:")
                for feature, status in analysis.items():
                    icon = "✅" if status else "❌"
                    print(f"  {icon} {feature}")
                
                # 检查是否解决了关键问题
                key_fixes = [
                    'has_vega_lite_fix',
                    'has_data_cleaning',
                    'has_index_sanitization',
                    'uses_streamlit_native'
                ]
                
                fixed_count = sum(1 for fix in key_fixes if analysis.get(fix))
                fix_rate = (fixed_count / len(key_fixes)) * 100
                
                print(f"关键修复率: {fixed_count}/{len(key_fixes)} ({fix_rate:.1f}%)")
                
                results.append({
                    'query': query,
                    'success': True,
                    'analysis': analysis,
                    'fix_rate': fix_rate
                })
                
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
                results.append({
                    'query': query,
                    'success': False,
                    'analysis': None,
                    'fix_rate': 0
                })
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append({
                'query': query,
                'success': False,
                'analysis': None,
                'fix_rate': 0
            })
        
        print()
    
    return results

def analyze_deep_fix_code(code):
    """分析深度修复代码特征"""
    analysis = {
        'has_vega_lite_fix': False,
        'has_data_cleaning': False,
        'has_index_sanitization': False,
        'has_duplicate_handling': False,
        'has_type_conversion': False,
        'has_inf_nan_handling': False,
        'uses_streamlit_native': False,
        'has_regex_cleaning': False,
        'has_safe_rendering': False,
        'has_error_handling': False
    }
    
    # 检查Vega-Lite修复
    vega_lite_indicators = [
        'use_container_width=True',
        'chart_data.index.duplicated',
        'clean_name = re.sub'
    ]
    analysis['has_vega_lite_fix'] = any(indicator in code for indicator in vega_lite_indicators)
    
    # 检查数据清理
    cleaning_indicators = [
        'replace([np.inf, -np.inf]',
        '.fillna(',
        'pd.to_numeric(',
        '.dropna('
    ]
    analysis['has_data_cleaning'] = any(indicator in code for indicator in cleaning_indicators)
    
    # 检查索引清理
    index_indicators = [
        'chart_data.index.name',
        're.sub(r\'[^\\w\\u4e00-\\u9fff]\'',
        'clean_name'
    ]
    analysis['has_index_sanitization'] = any(indicator in code for indicator in index_indicators)
    
    # 检查重复处理
    analysis['has_duplicate_handling'] = 'duplicated().any()' in code
    
    # 检查类型转换
    analysis['has_type_conversion'] = 'pd.to_numeric' in code or '.astype(' in code
    
    # 检查无穷大和NaN处理
    analysis['has_inf_nan_handling'] = 'np.inf' in code and '.fillna(' in code
    
    # 检查Streamlit原生使用
    streamlit_methods = ['st.bar_chart', 'st.line_chart', 'st.scatter_chart', 'st.area_chart']
    analysis['uses_streamlit_native'] = any(method in code for method in streamlit_methods)
    
    # 检查正则表达式清理
    analysis['has_regex_cleaning'] = 'import re' in code and 're.sub(' in code
    
    # 检查安全渲染
    safe_rendering_indicators = [
        'if not chart_data.empty',
        'chart_data.sum() != 0',
        'use_container_width=True'
    ]
    analysis['has_safe_rendering'] = any(indicator in code for indicator in safe_rendering_indicators)
    
    # 检查错误处理
    error_handling_indicators = [
        'try:',
        'except',
        'st.warning(',
        'errors=\'coerce\''
    ]
    analysis['has_error_handling'] = any(indicator in code for indicator in error_handling_indicators)
    
    return analysis

def test_specific_vega_lite_issues():
    """测试特定的Vega-Lite问题"""
    print("🔍 测试特定Vega-Lite问题")
    print("=" * 40)
    
    # 创建会导致Vega-Lite问题的数据
    problematic_df = pd.DataFrame({
        '产品名称@#$': ['A', 'B', 'C'],  # 特殊字符
        '销售额_start': [1000, 2000, 3000],  # 冲突字段名
        '销售额_end': [1500, 2500, 3500],  # 冲突字段名
        '销售额': [np.inf, -np.inf, np.nan]  # 异常值
    })
    
    print("问题数据:")
    print(problematic_df)
    print()
    
    query = "分析销售额数据，生成图表"
    
    try:
        result = analyze_data(problematic_df, query, "problematic_test", use_metadata=True)
        
        if result.get('success'):
            code = result.get('code', '')
            
            # 检查是否处理了问题
            issues_handled = {
                '特殊字符清理': 're.sub(' in code,
                '无穷大值处理': 'np.inf' in code,
                'NaN值处理': '.fillna(' in code,
                '字段名冲突避免': 'clean_name' in code,
                '重复索引处理': 'duplicated()' in code
            }
            
            print("问题处理情况:")
            for issue, handled in issues_handled.items():
                status = "✅ 已处理" if handled else "❌ 未处理"
                print(f"  {issue}: {status}")
            
            handled_count = sum(issues_handled.values())
            total_issues = len(issues_handled)
            
            print(f"\n问题处理率: {handled_count}/{total_issues} ({handled_count/total_issues*100:.1f}%)")
            
            return handled_count == total_issues
        else:
            print(f"❌ 分析失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def generate_final_recommendations(results):
    """生成最终建议"""
    print("\n🎯 最终建议和总结")
    print("=" * 60)
    
    # 统计结果
    successful_tests = [r for r in results if r['success']]
    success_rate = len(successful_tests) / len(results) * 100
    
    if successful_tests:
        avg_fix_rate = sum(r['fix_rate'] for r in successful_tests) / len(successful_tests)
    else:
        avg_fix_rate = 0
    
    print(f"测试成功率: {len(successful_tests)}/{len(results)} ({success_rate:.1f}%)")
    print(f"平均修复率: {avg_fix_rate:.1f}%")
    
    if success_rate >= 80 and avg_fix_rate >= 75:
        print("\n🎉 深度修复效果优秀！")
        print("建议:")
        print("1. 立即重启Streamlit服务")
        print("2. 清理浏览器缓存")
        print("3. 测试图表查询")
        print("4. 观察控制台是否还有警告")
        
        print("\n预期效果:")
        print("✅ 图表不再闪退")
        print("✅ 控制台警告显著减少")
        print("✅ Vega-Lite渲染稳定")
        print("✅ 数据处理更安全")
        
    elif success_rate >= 60:
        print("\n⚠️ 深度修复部分生效")
        print("建议:")
        print("1. 重启Streamlit服务测试")
        print("2. 使用更明确的查询语句")
        print("3. 如果问题持续，提供具体错误信息")
        
    else:
        print("\n❌ 深度修复效果不佳")
        print("建议:")
        print("1. 检查修复代码是否正确保存")
        print("2. 验证模块是否正确重新加载")
        print("3. 考虑手动应用修复")
    
    print(f"\n📋 重启步骤:")
    print("1. 停止Streamlit: Ctrl+C")
    print("2. 清理缓存: find . -name '__pycache__' -exec rm -rf {} +")
    print("3. 重启服务: streamlit run streamlit_app.py")
    print("4. 清理浏览器缓存: Ctrl+Shift+R")
    print("5. 测试查询: '请分析各产品销售额，用柱状图展示'")

if __name__ == "__main__":
    print("🚀 深度图表修复验证工具")
    print("基于Streamlit技术文档的根本性修复测试")
    print("=" * 60)
    
    # 1. 主要测试
    results = test_deep_chart_fix()
    
    # 2. 特定问题测试
    vega_lite_fixed = test_specific_vega_lite_issues()
    
    # 3. 生成建议
    generate_final_recommendations(results)
    
    # 4. 最终状态
    print(f"\n🔬 深度修复验证完成")
    print("=" * 30)
    
    if vega_lite_fixed and results:
        avg_success = sum(1 for r in results if r['success']) / len(results)
        if avg_success >= 0.8:
            print("✅ 深度修复验证通过，建议立即重启测试")
        else:
            print("⚠️ 部分修复生效，建议重启后观察效果")
    else:
        print("❌ 修复验证未完全通过，可能需要进一步调试")
    
    print("\n💡 关键提醒:")
    print("这次修复针对Streamlit的Vega-Lite渲染引擎进行了深度优化，")
    print("解决了数据格式、字段名冲突、索引处理等根本性问题。")
    print("重启后应该能显著改善图表显示稳定性。")
