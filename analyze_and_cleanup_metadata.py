#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析和清理元数据中的未上传表格
找出测试表格和临时表格，只保留用户真正上传的表格
"""

import pandas as pd
import sys
from pathlib import Path
import json
import os

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

try:
    from metadata_manager import metadata_manager
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def analyze_existing_tables():
    """分析现有的表格"""
    print("🔍 分析现有表格")
    print("=" * 50)
    
    tables = metadata_manager.get_all_tables()
    print(f"📊 当前总表格数: {len(tables)}")
    
    # 分类表格
    test_tables = []
    temp_tables = []
    user_tables = []
    
    for table_name in tables:
        table_metadata = metadata_manager.get_table_metadata(table_name)
        
        # 根据表格名称和特征判断类型
        if any(keyword in table_name.lower() for keyword in [
            'test', 'demo', 'sample', 'example', 'temp', 'tmp',
            'sales_data', 'finance_data', 'inventory_data',
            'metadata_test', 'ui_test', 'save_test', 'batch_test',
            'customer_sales', 'product_inventory', 'meaningful_table',
            'cryptic_table', 'customer_info_single_table'
        ]):
            test_tables.append(table_name)
        elif table_name.endswith('.csv') or table_name.endswith('.xlsx'):
            # 可能是用户上传的文件
            user_tables.append(table_name)
        else:
            # 需要进一步判断
            temp_tables.append(table_name)
    
    print(f"\n📋 表格分类:")
    print(f"🧪 测试表格 ({len(test_tables)}个):")
    for table in test_tables:
        print(f"  - {table}")
    
    print(f"\n👤 疑似用户表格 ({len(user_tables)}个):")
    for table in user_tables:
        print(f"  - {table}")
    
    print(f"\n❓ 待确认表格 ({len(temp_tables)}个):")
    for table in temp_tables:
        table_metadata = metadata_manager.get_table_metadata(table)
        if table_metadata:
            print(f"  - {table} (业务领域: {table_metadata.business_domain}, 列数: {len(table_metadata.columns)})")
    
    return test_tables, user_tables, temp_tables

def check_table_sources():
    """检查表格来源"""
    print("\n🔍 检查表格来源")
    print("=" * 50)
    
    # 检查是否有上传文件的记录
    upload_dir = Path("uploads")
    if upload_dir.exists():
        uploaded_files = list(upload_dir.glob("*"))
        print(f"📁 上传目录中的文件 ({len(uploaded_files)}个):")
        for file in uploaded_files:
            print(f"  - {file.name}")
    else:
        print("📁 未找到uploads目录")
    
    # 检查当前目录中的CSV文件
    csv_files = list(Path(".").glob("*.csv"))
    print(f"\n📄 当前目录中的CSV文件 ({len(csv_files)}个):")
    for file in csv_files:
        print(f"  - {file.name}")
    
    return upload_dir.exists(), csv_files

def identify_test_patterns():
    """识别测试模式创建的表格"""
    print("\n🧪 识别测试模式创建的表格")
    print("=" * 50)
    
    tables = metadata_manager.get_all_tables()
    test_patterns = []
    
    for table_name in tables:
        table_metadata = metadata_manager.get_table_metadata(table_name)
        if table_metadata:
            # 检查是否是测试数据的特征
            is_test = False
            reasons = []
            
            # 1. 表格名称包含测试关键词
            test_keywords = ['test', 'demo', 'sample', 'example', 'temp', 'tmp']
            if any(keyword in table_name.lower() for keyword in test_keywords):
                is_test = True
                reasons.append("表格名称包含测试关键词")
            
            # 2. 数据量很小（可能是测试数据）
            if len(table_metadata.columns) <= 3:
                reasons.append("列数较少（可能是测试数据）")
            
            # 3. 业务领域是通用或测试相关
            if table_metadata.business_domain in ["通用", "测试", "示例"]:
                reasons.append("业务领域为通用/测试")
            
            # 4. 表格描述包含测试信息
            if any(keyword in table_metadata.description.lower() for keyword in test_keywords):
                is_test = True
                reasons.append("表格描述包含测试信息")
            
            # 5. 列名是典型的测试数据
            column_names = list(table_metadata.columns.keys())
            test_column_patterns = ['col_a', 'col_b', 'col_c', 'test_', 'demo_']
            if any(any(pattern in col.lower() for pattern in test_column_patterns) for col in column_names):
                is_test = True
                reasons.append("包含测试列名模式")
            
            if is_test or len(reasons) >= 2:
                test_patterns.append({
                    'name': table_name,
                    'is_test': is_test,
                    'reasons': reasons,
                    'columns': len(table_metadata.columns),
                    'domain': table_metadata.business_domain
                })
    
    print(f"🔍 识别出的测试表格 ({len(test_patterns)}个):")
    for pattern in test_patterns:
        print(f"  - {pattern['name']}")
        print(f"    原因: {', '.join(pattern['reasons'])}")
        print(f"    列数: {pattern['columns']}, 业务领域: {pattern['domain']}")
        print()
    
    return test_patterns

def create_cleanup_plan():
    """创建清理计划"""
    print("\n📋 创建清理计划")
    print("=" * 50)
    
    # 获取分析结果
    test_tables, user_tables, temp_tables = analyze_existing_tables()
    upload_exists, csv_files = check_table_sources()
    test_patterns = identify_test_patterns()
    
    # 创建清理计划
    to_delete = set()
    to_keep = set()
    to_review = set()
    
    # 明确的测试表格
    for table in test_tables:
        to_delete.add(table)
    
    # 测试模式识别的表格
    for pattern in test_patterns:
        if pattern['is_test']:
            to_delete.add(pattern['name'])
    
    # 用户上传的表格
    for table in user_tables:
        to_keep.add(table)
    
    # 待审查的表格
    for table in temp_tables:
        if table not in to_delete and table not in to_keep:
            to_review.add(table)
    
    print(f"🗑️ 建议删除的表格 ({len(to_delete)}个):")
    for table in sorted(to_delete):
        print(f"  - {table}")
    
    print(f"\n✅ 建议保留的表格 ({len(to_keep)}个):")
    for table in sorted(to_keep):
        print(f"  - {table}")
    
    print(f"\n❓ 需要审查的表格 ({len(to_review)}个):")
    for table in sorted(to_review):
        table_metadata = metadata_manager.get_table_metadata(table)
        print(f"  - {table} (业务领域: {table_metadata.business_domain})")
    
    return to_delete, to_keep, to_review

def execute_cleanup(to_delete, confirm=False):
    """执行清理操作"""
    print(f"\n🧹 执行清理操作")
    print("=" * 50)
    
    if not confirm:
        print("⚠️ 这是预览模式，不会实际删除数据")
        print("如需实际执行，请设置 confirm=True")
    
    deleted_count = 0
    failed_count = 0
    
    for table_name in to_delete:
        try:
            if confirm:
                # 实际删除表格元数据
                if hasattr(metadata_manager, 'tables_metadata'):
                    if table_name in metadata_manager.tables_metadata:
                        del metadata_manager.tables_metadata[table_name]
                        deleted_count += 1
                        print(f"✅ 已删除: {table_name}")
                    else:
                        print(f"⚠️ 表格不存在: {table_name}")
                else:
                    print(f"❌ 无法访问元数据存储")
            else:
                print(f"🔍 预览删除: {table_name}")
                deleted_count += 1
        except Exception as e:
            print(f"❌ 删除失败 {table_name}: {e}")
            failed_count += 1
    
    print(f"\n📊 清理结果:")
    print(f"  成功处理: {deleted_count} 个表格")
    print(f"  失败: {failed_count} 个表格")
    
    if confirm and deleted_count > 0:
        # 保存清理后的元数据
        try:
            metadata_manager.save_metadata()
            print(f"✅ 元数据已保存")
        except Exception as e:
            print(f"❌ 保存元数据失败: {e}")
    
    return deleted_count, failed_count

def backup_metadata():
    """备份当前元数据"""
    print(f"\n💾 备份当前元数据")
    print("=" * 50)
    
    try:
        backup_file = f"metadata_backup_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json"
        success = metadata_manager.export_metadata(backup_file, format="json")
        
        if success:
            print(f"✅ 元数据已备份到: {backup_file}")
            return backup_file
        else:
            print(f"❌ 备份失败")
            return None
    except Exception as e:
        print(f"❌ 备份过程出错: {e}")
        return None

def main():
    """主函数"""
    print("🚀 开始分析和清理元数据")
    print("=" * 60)
    
    try:
        # 1. 备份当前元数据
        backup_file = backup_metadata()
        
        # 2. 分析现有表格
        analyze_existing_tables()
        
        # 3. 检查表格来源
        check_table_sources()
        
        # 4. 识别测试模式
        identify_test_patterns()
        
        # 5. 创建清理计划
        to_delete, to_keep, to_review = create_cleanup_plan()
        
        # 6. 预览清理操作
        execute_cleanup(to_delete, confirm=False)
        
        print("\n" + "=" * 60)
        print("🎉 元数据分析完成！")
        
        print(f"\n📊 分析结果:")
        print(f"  - 建议删除: {len(to_delete)} 个表格")
        print(f"  - 建议保留: {len(to_keep)} 个表格")
        print(f"  - 需要审查: {len(to_review)} 个表格")
        
        if backup_file:
            print(f"  - 备份文件: {backup_file}")
        
        print(f"\n💡 下一步操作:")
        print(f"  1. 审查需要确认的表格")
        print(f"  2. 如确认清理计划，可执行实际清理")
        print(f"  3. 清理命令: execute_cleanup(to_delete, confirm=True)")
        
        # 返回清理计划供后续使用
        return to_delete, to_keep, to_review
        
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

if __name__ == "__main__":
    to_delete, to_keep, to_review = main()
    
    # 如果用户确认要执行清理，可以取消下面的注释
    # if to_delete:
    #     print(f"\n⚠️ 即将执行实际清理操作...")
    #     input("按回车键继续，或Ctrl+C取消...")
    #     execute_cleanup(to_delete, confirm=True)
