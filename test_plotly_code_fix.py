#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Plotly代码保存修复效果
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_plotly_code_fix():
    """测试Plotly代码保存修复"""
    print("🧪 测试Plotly代码保存修复效果")
    print("=" * 60)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500, 20200, 15000, 9700, 8000],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    # 测试查询（这个查询通常会生成Plotly代码）
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 40)
    
    try:
        # 调用分析函数
        result = analyze_data(df, query, "sales_data", use_metadata=True)
        
        if result.get('success'):
            print("✅ 分析成功")
            
            # 检查结果
            original_code = result.get('code', '')
            plotly_code = result.get('plotly_code', '')
            uses_plotly = result.get('uses_plotly_native', False)
            has_chart = result.get('has_chart', False)
            
            print(f"📊 使用Plotly: {uses_plotly}")
            print(f"📊 需要前端显示: {has_chart}")
            
            if uses_plotly and has_chart:
                print("\n🔍 代码对比分析:")
                print("=" * 30)
                
                print("原始代码:")
                print(original_code)
                print()
                
                print("保存给前端的代码:")
                print(plotly_code)
                print()
                
                # 分析代码差异
                analysis = analyze_code_differences(original_code, plotly_code)
                
                print("📊 代码分析结果:")
                for key, value in analysis.items():
                    status = "✅" if value else "❌"
                    print(f"  {status} {key}")
                
                # 测试前端代码执行
                print("\n🧪 测试前端代码执行:")
                test_frontend_execution(plotly_code, df)
                
            else:
                print("ℹ️ 此查询没有生成需要前端重新执行的Plotly代码")
                
        else:
            print(f"❌ 分析失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        import traceback
        traceback.print_exc()

def analyze_code_differences(original_code, plotly_code):
    """分析代码差异"""
    analysis = {
        'original_has_import_st': 'import streamlit as st' in original_code,
        'plotly_has_import_st': 'import streamlit as st' in plotly_code and not '# import streamlit as st' in plotly_code,
        'plotly_has_commented_import': '# import streamlit as st' in plotly_code,
        'codes_are_different': original_code != plotly_code,
        'plotly_code_cleaned': '# 已在执行环境中提供' in plotly_code
    }

    return analysis

def test_frontend_execution(code, df):
    """测试前端代码执行"""
    print("模拟前端执行环境...")
    
    # 创建模拟的前端执行环境
    try:
        import streamlit as st
        frontend_available = True
    except ImportError:
        # 创建模拟的streamlit对象
        class MockStreamlit:
            def __getattr__(self, name):
                def mock_method(*args, **kwargs):
                    print(f"[前端模拟] st.{name}() 被调用")
                    return None
                return mock_method
        st = MockStreamlit()
        frontend_available = False
    
    try:
        import plotly.express as px
        import plotly.graph_objects as go
        plotly_available = True
    except ImportError:
        px = None
        go = None
        plotly_available = False
    
    # 创建执行环境
    exec_globals = {
        'df': df,
        'pd': pd,
        'st': st,
        'px': px,
        'go': go,
        'print': print
    }
    
    try:
        print("执行代码:")
        print(code)
        print()
        
        exec(code, exec_globals)
        print("✅ 前端代码执行成功")
        
    except Exception as e:
        print(f"❌ 前端代码执行失败: {e}")
        if 'cannot access local variable' in str(e):
            print("🚨 检测到作用域冲突！这正是我们要修复的问题。")
        return False
    
    return True

def test_multiple_scenarios():
    """测试多种场景"""
    print("\n🧪 测试多种场景")
    print("=" * 40)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro'],
        '销售额': [25500, 20200, 15000]
    })
    
    test_cases = [
        {
            'name': '柱状图查询',
            'query': '请分析各产品销售额，用柱状图展示'
        },
        {
            'name': '饼图查询',
            'query': '请生成各产品销售额的饼图'
        },
        {
            'name': '折线图查询',
            'query': '生成销售额的折线图'
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n📋 {test_case['name']}: {test_case['query']}")
        print("-" * 20)
        
        try:
            result = analyze_data(df, test_case['query'], "test_data", use_metadata=True)
            
            if result.get('success'):
                uses_plotly = result.get('uses_plotly_native', False)
                has_chart = result.get('has_chart', False)
                
                if uses_plotly and has_chart:
                    plotly_code = result.get('plotly_code', '')
                    # 检查是否有未注释的import streamlit as st
                    lines = plotly_code.split('\n')
                    has_import_conflict = any(
                        line.strip() == 'import streamlit as st'
                        for line in lines
                    )

                    print(f"📊 Plotly代码: {'有导入冲突' if has_import_conflict else '已清理'}")
                    
                    results.append({
                        'name': test_case['name'],
                        'success': True,
                        'uses_plotly': True,
                        'has_conflict': has_import_conflict
                    })
                else:
                    print("📊 使用Streamlit原生图表")
                    results.append({
                        'name': test_case['name'],
                        'success': True,
                        'uses_plotly': False,
                        'has_conflict': False
                    })
            else:
                print(f"❌ 失败: {result.get('error', '未知错误')}")
                results.append({
                    'name': test_case['name'],
                    'success': False,
                    'uses_plotly': False,
                    'has_conflict': False
                })
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            results.append({
                'name': test_case['name'],
                'success': False,
                'uses_plotly': False,
                'has_conflict': False
            })
    
    # 总结结果
    print("\n" + "=" * 40)
    print("🎯 测试结果总结")
    print("=" * 40)
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['success'])
    plotly_tests = sum(1 for r in results if r['uses_plotly'])
    conflict_tests = sum(1 for r in results if r['has_conflict'])
    
    print(f"总测试数: {total_tests}")
    print(f"成功测试: {successful_tests}")
    print(f"使用Plotly: {plotly_tests}")
    print(f"有导入冲突: {conflict_tests}")
    
    if conflict_tests == 0:
        print("\n✅ 修复完全成功！所有Plotly代码都已清理导入冲突")
    elif conflict_tests < plotly_tests:
        print(f"\n⚠️ 部分修复成功，还有{conflict_tests}个测试存在冲突")
    else:
        print("\n❌ 修复失败，仍存在导入冲突")

if __name__ == "__main__":
    test_plotly_code_fix()
    test_multiple_scenarios()
    
    print("\n" + "=" * 60)
    print("🎯 Plotly代码保存修复总结")
    print("=" * 60)
    print("问题：前端重新执行Plotly代码时出现st变量作用域冲突")
    print()
    print("根本原因：")
    print("1. 保存给前端的是原始代码（包含import语句）")
    print("2. 前端执行环境中已经提供了st变量")
    print("3. 导入语句与环境变量产生冲突")
    print()
    print("解决方案：")
    print("1. ✅ 修改代码保存逻辑")
    print("2. ✅ 保存清理后的代码而不是原始代码")
    print("3. ✅ 确保前端执行的代码没有导入冲突")
    print()
    print("预期效果：")
    print("- 前端重新执行Plotly代码时不再出错")
    print("- 图表正常显示，不再闪退")
    print("- 用户体验显著改善")
