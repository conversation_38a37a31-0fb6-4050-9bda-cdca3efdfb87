#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Plotly显示逻辑修复
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_plotly_display_logic():
    """测试Plotly显示逻辑"""
    print("🔧 测试Plotly显示逻辑修复")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据: {df.shape}")
    
    # 测试Plotly饼图
    query = "请为我分析2024年各产品销售额，并用饼图展示"
    print(f"🔍 查询: {query}")
    
    try:
        result = analyze_data(df, query, table_name="sales_data")
        
        if result and result.get('success'):
            print("✅ 查询成功")
            
            code = result.get('code', '')
            output = result.get('output', '')
            has_chart = result.get('has_chart', False)
            uses_plotly_native = result.get('uses_plotly_native', False)
            chart_figure = result.get('chart_figure')
            chart_path = result.get('chart_path')
            
            print(f"\n📝 生成的代码:")
            print(code)
            
            print(f"\n🔍 显示逻辑分析:")
            print(f"  📊 使用Plotly原生: {'✅' if uses_plotly_native else '❌'}")
            print(f"  📈 has_chart标志: {has_chart}")
            print(f"  🖼️ chart_figure存在: {chart_figure is not None}")
            print(f"  📁 chart_path存在: {chart_path is not None}")
            
            print(f"\n📊 执行输出:")
            print(output if output.strip() else "(无输出)")
            
            # 分析显示逻辑
            print(f"\n🎨 预期显示行为:")
            if uses_plotly_native:
                print("  1. ✅ Plotly图表通过st.plotly_chart()直接显示")
                print("  2. ✅ 不会显示额外的'AI生成的数据可视化图表'")
                print("  3. ✅ 避免重复显示")
            elif has_chart:
                print("  1. ✅ 会显示'AI生成的数据可视化图表'")
                print("  2. ✅ 使用matplotlib图表")
            else:
                print("  1. ❌ 没有图表显示")
            
            # 验证修复效果
            if uses_plotly_native and not has_chart:
                print(f"\n🎉 修复成功！")
                print("  ✅ Plotly原生图表正确识别")
                print("  ✅ 避免了重复显示")
                print("  ✅ 用户只会看到一个饼图")
            elif not uses_plotly_native and has_chart:
                print(f"\n✅ Matplotlib方案正常")
                print("  ✅ 会正确显示额外图表")
            else:
                print(f"\n⚠️ 可能仍有问题")
                
        else:
            print("❌ 查询失败")
            if result:
                print(f"错误: {result.get('error', '未知错误')}")
                
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        import traceback
        traceback.print_exc()

def test_matplotlib_fallback_logic():
    """测试Matplotlib回退逻辑"""
    print(f"\n🔧 测试Matplotlib回退逻辑")
    print("=" * 50)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 测试柱状图（应该使用matplotlib）
    query = "生成销售金额的柱状图"
    print(f"🔍 查询: {query}")
    
    try:
        result = analyze_data(df, query, table_name="sales_data")
        
        if result and result.get('success'):
            code = result.get('code', '')
            has_chart = result.get('has_chart', False)
            uses_plotly_native = result.get('uses_plotly_native', False)
            
            print(f"📝 生成的代码类型:")
            if 'plt.bar' in code:
                print("  📊 使用matplotlib柱状图")
            elif 'px.bar' in code:
                print("  📊 使用Plotly柱状图")
            
            print(f"🔍 显示逻辑:")
            print(f"  uses_plotly_native: {uses_plotly_native}")
            print(f"  has_chart: {has_chart}")
            
            if not uses_plotly_native and has_chart:
                print("  ✅ 会正确显示matplotlib图表")
            elif uses_plotly_native and not has_chart:
                print("  ✅ 会使用Plotly原生显示")
            else:
                print("  ⚠️ 显示逻辑可能有问题")
                
        else:
            print("❌ 查询失败")
            
    except Exception as e:
        print(f"❌ 查询异常: {e}")

def summarize_fix():
    """总结修复效果"""
    print(f"\n🎯 修复效果总结")
    print("=" * 50)
    
    print("🔧 修复的问题:")
    print("  ❌ 原问题: Plotly饼图显示后立即被'AI生成图表'覆盖")
    print("  ✅ 修复后: Plotly饼图独立显示，无重复")
    
    print("\n🎨 新的显示逻辑:")
    print("  1. 检测代码中是否包含'st.plotly_chart'")
    print("  2. 如果是Plotly原生:")
    print("     - uses_plotly_native = True")
    print("     - has_chart = False (避免额外显示)")
    print("  3. 如果是matplotlib:")
    print("     - uses_plotly_native = False") 
    print("     - has_chart = True (需要额外显示)")
    
    print("\n🎉 用户体验:")
    print("  ✅ 请求饼图 → 只显示一个Plotly原生饼图")
    print("  ✅ 请求柱状图 → 显示matplotlib图表")
    print("  ✅ 无重复显示，界面清洁")

if __name__ == "__main__":
    test_plotly_display_logic()
    test_matplotlib_fallback_logic()
    summarize_fix()
