#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 st 变量错误修复效果
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_st_error_fix():
    """测试 st 变量错误修复"""
    print("🧪 测试 st 变量错误修复")
    print("=" * 50)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500, 20200, 15000, 9700, 8000],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    # 测试多个查询
    test_queries = [
        "请分析2024年各产品销售额，用柱状图展示",
        "生成销售额的折线图",
        "创建产品销量的条形图",
        "用柱状图显示各产品的销售情况"
    ]
    
    success_count = 0
    total_count = len(test_queries)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 测试 {i}: {query}")
        print("-" * 40)
        
        try:
            # 调用分析函数
            result = analyze_data(df, query, "sales_data", use_metadata=True)
            
            if result.get('success'):
                print("✅ 分析成功")
                success_count += 1
                
                # 检查生成的代码
                code = result.get('code', '')
                if 'st.' in code:
                    print("📊 包含Streamlit图表代码")
                    
                    # 检查是否有导入冲突
                    if 'import streamlit as st' in code:
                        print("⚠️ 代码包含 import streamlit as st（可能导致冲突）")
                    else:
                        print("✅ 代码已清理，无导入冲突")
                
                # 显示输出摘要
                output = result.get('output', '')
                if output:
                    lines = output.split('\n')
                    print(f"📄 输出行数: {len(lines)}")
                    if len(lines) > 0:
                        print(f"📄 首行输出: {lines[0][:50]}...")
                
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 执行异常: {e}")
            print(f"错误类型: {type(e).__name__}")
    
    # 总结结果
    print("\n" + "=" * 50)
    print("🎯 测试总结")
    print("=" * 50)
    
    success_rate = (success_count / total_count * 100) if total_count > 0 else 0
    print(f"成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("✅ 所有测试通过！st 变量错误已修复")
    elif success_rate >= 75:
        print("✅ 大部分测试通过，修复效果良好")
    elif success_rate >= 50:
        print("⚠️ 部分测试通过，仍需进一步优化")
    else:
        print("❌ 修复效果不佳，需要重新检查")

def test_code_cleaning():
    """测试代码清理功能"""
    print("\n🧪 测试代码清理功能")
    print("=" * 40)
    
    # 模拟有问题的代码
    problematic_code = """import streamlit as st
import pandas as pd
import numpy as np

# 使用Streamlit原生柱状图
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
print("各产品销售额:")
print(product_sales)

st.subheader("📊 各产品销售额对比")
st.bar_chart(product_sales)"""

    print("原始代码:")
    print(problematic_code)
    print()
    
    # 模拟清理过程
    cleaned_code = problematic_code
    import_lines_to_remove = [
        'import streamlit as st',
        'import pandas as pd',
        'import numpy as np',
        'import matplotlib.pyplot as plt'
    ]
    
    for import_line in import_lines_to_remove:
        if import_line in cleaned_code:
            cleaned_code = cleaned_code.replace(import_line, f'# {import_line} # 已在执行环境中提供')
    
    print("清理后的代码:")
    print(cleaned_code)
    print()
    
    # 测试执行
    try:
        # 创建测试数据和执行环境
        df = pd.DataFrame({
            '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro'],
            '销售额': [25500, 20200, 15000]
        })
        
        # 模拟Streamlit对象
        class MockStreamlit:
            def __getattr__(self, name):
                def mock_method(*args, **kwargs):
                    print(f"[模拟] st.{name}() 被调用")
                    return None
                return mock_method
        
        exec_globals = {
            'df': df,
            'pd': pd,
            'st': MockStreamlit(),
            'print': print
        }
        
        exec(cleaned_code, exec_globals)
        print("✅ 清理后的代码执行成功")
        
    except Exception as e:
        print(f"❌ 清理后的代码执行失败: {e}")

if __name__ == "__main__":
    test_code_cleaning()
    test_st_error_fix()
    
    print("\n" + "=" * 60)
    print("🎯 修复效果总结")
    print("=" * 60)
    print("问题：前端报错 'cannot access local variable 'st' where it is not associated with a value'")
    print()
    print("根本原因：")
    print("1. 生成的代码包含 'import streamlit as st'")
    print("2. 执行环境中已经提供了 st 变量")
    print("3. 导入语句与环境变量产生冲突")
    print()
    print("解决方案：")
    print("1. ✅ 在代码执行前清理重复的导入语句")
    print("2. ✅ 保留功能性代码，只移除导入冲突")
    print("3. ✅ 在两个LLM类中都实施了修复")
    print("4. ✅ 添加了详细的调试信息")
    print()
    print("预期效果：")
    print("- 消除 st 变量冲突错误")
    print("- 保持Streamlit图表功能正常")
    print("- 提供更清晰的错误诊断")
