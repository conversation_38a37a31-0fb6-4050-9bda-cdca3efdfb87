#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据清理修复效果
"""

import pandas as pd
import numpy as np
from perfect_tongyi_integration import analyze_data

def test_data_cleaning_fix():
    """测试数据清理修复"""
    print("🧪 测试数据清理修复效果")
    print("=" * 60)
    
    # 创建包含潜在问题的测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500.0, 20200.0, 15000.0, 9700.0, 8000.0],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    print("原始测试数据:")
    print(df)
    print(f"销售额数据类型: {df['销售额'].dtype}")
    print(f"销售额数据范围: {df['销售额'].min()} - {df['销售额'].max()}")
    print()
    
    # 测试查询
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 40)
    
    try:
        result = analyze_data(df, query, "sales_data", use_metadata=True)
        
        if result.get('success'):
            print("✅ 分析成功")
            
            # 检查生成的代码
            code = result.get('code', '')
            output = result.get('output', '')
            
            print("生成的代码:")
            print(code)
            print()
            
            print("执行输出:")
            print(output)
            print()
            
            # 分析代码内容
            analysis = analyze_generated_code(code)
            
            print("📊 代码分析结果:")
            for key, value in analysis.items():
                status = "✅" if value else "❌"
                print(f"  {status} {key}")
            
            # 检查是否解决了异常字段问题
            if not analysis.get('has_problematic_fields'):
                print("\n✅ 成功！未检测到异常字段（销售额_start/销售额_end）")
            else:
                print("\n❌ 仍然存在异常字段问题")
            
            return analysis
            
        else:
            print(f"❌ 分析失败: {result.get('error', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_generated_code(code):
    """分析生成的代码"""
    analysis = {
        'has_data_cleaning': False,
        'has_numpy_import': False,
        'has_type_conversion': False,
        'has_nan_handling': False,
        'has_inf_handling': False,
        'has_data_validation': False,
        'has_problematic_fields': False,
        'uses_streamlit_native': False
    }
    
    # 检查数据清理机制
    analysis['has_data_cleaning'] = 'replace([np.inf, -np.inf]' in code
    analysis['has_numpy_import'] = 'import numpy as np' in code
    analysis['has_type_conversion'] = '.astype(float)' in code
    analysis['has_nan_handling'] = '.fillna(' in code
    analysis['has_inf_handling'] = 'np.inf' in code
    analysis['has_data_validation'] = '数据类型:' in code or '数据范围:' in code
    
    # 检查异常字段
    problematic_fields = ['销售额_start', '销售额_end']
    analysis['has_problematic_fields'] = any(field in code for field in problematic_fields)
    
    # 检查Streamlit原生方法
    streamlit_methods = ['st.bar_chart', 'st.line_chart', 'st.scatter_chart', 'st.area_chart']
    analysis['uses_streamlit_native'] = any(method in code for method in streamlit_methods)
    
    return analysis

def test_problematic_data():
    """测试包含问题数据的情况"""
    print("\n🧪 测试包含问题数据的情况")
    print("=" * 40)
    
    # 创建包含异常值的数据
    problematic_df = pd.DataFrame({
        '产品名称': ['A', 'B', 'C', 'D', 'E'],
        '销售额': [1000.0, np.inf, 2000.0, -np.inf, np.nan],
        '销量': [10, 20, 30, 40, 50]
    })
    
    print("包含异常值的测试数据:")
    print(problematic_df)
    print(f"销售额包含无穷大: {np.isinf(problematic_df['销售额']).any()}")
    print(f"销售额包含NaN: {problematic_df['销售额'].isnull().any()}")
    print()
    
    query = "请分析各产品销售额，用柱状图展示"
    
    try:
        result = analyze_data(problematic_df, query, "problematic_data", use_metadata=True)
        
        if result.get('success'):
            print("✅ 异常数据处理成功")
            
            code = result.get('code', '')
            output = result.get('output', '')
            
            # 检查是否包含数据清理
            if 'replace([np.inf, -np.inf]' in code:
                print("✅ 包含无穷大值处理")
            else:
                print("❌ 缺少无穷大值处理")
            
            if '.fillna(' in code:
                print("✅ 包含NaN值处理")
            else:
                print("❌ 缺少NaN值处理")
            
            return True
        else:
            print(f"❌ 异常数据处理失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 异常数据测试失败: {e}")
        return False

def simulate_streamlit_execution():
    """模拟Streamlit执行环境"""
    print("\n🧪 模拟Streamlit执行环境")
    print("=" * 40)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro'],
        '销售额': [25500.0, 20200.0, 15000.0]
    })
    
    # 模拟生成的代码（带数据清理）
    test_code = """import numpy as np
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)

# 数据清理：处理异常值和类型转换
product_sales = product_sales.replace([np.inf, -np.inf], np.nan)
product_sales = product_sales.fillna(0)
product_sales = product_sales.astype(float)

# 验证数据质量
print(f"数据类型: {product_sales.dtype}")
print(f"数据范围: {product_sales.min()} - {product_sales.max()}")
print(f"是否包含NaN: {product_sales.isnull().any()}")

print("各产品销售额:")
print(product_sales)"""
    
    print("模拟执行的代码:")
    print(test_code)
    print()
    
    try:
        # 创建执行环境
        exec_globals = {
            'df': df,
            'pd': pd,
            'np': np,
            'print': print
        }
        
        # 执行代码
        exec(test_code, exec_globals)
        
        # 检查结果
        product_sales = exec_globals.get('product_sales')
        if product_sales is not None:
            print(f"\n📊 执行结果分析:")
            print(f"  数据类型: {product_sales.dtype}")
            print(f"  数据形状: {product_sales.shape}")
            print(f"  包含无穷大: {np.isinf(product_sales).any()}")
            print(f"  包含NaN: {product_sales.isnull().any()}")
            print(f"  数据范围: {product_sales.min()} - {product_sales.max()}")
            
            # 检查是否会产生异常字段
            if hasattr(product_sales, 'index'):
                print(f"  索引: {list(product_sales.index)}")
                
                # 检查索引中是否有异常字段
                problematic_fields = ['销售额_start', '销售额_end']
                has_problematic = any(field in str(product_sales.index) for field in problematic_fields)
                
                if not has_problematic:
                    print("✅ 未检测到异常字段")
                else:
                    print("❌ 检测到异常字段")
            
            return True
        else:
            print("❌ 未找到处理结果")
            return False
            
    except Exception as e:
        print(f"❌ 模拟执行失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 数据清理修复测试工具")
    print("=" * 60)
    
    # 1. 测试数据清理修复
    analysis = test_data_cleaning_fix()
    
    # 2. 测试异常数据处理
    problematic_success = test_problematic_data()
    
    # 3. 模拟Streamlit执行
    simulation_success = simulate_streamlit_execution()
    
    # 4. 总结结果
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    
    if analysis:
        cleaning_features = [
            'has_data_cleaning',
            'has_numpy_import', 
            'has_type_conversion',
            'has_nan_handling',
            'has_inf_handling'
        ]
        
        implemented_features = sum(1 for feature in cleaning_features if analysis.get(feature))
        total_features = len(cleaning_features)
        
        print(f"数据清理功能实现率: {implemented_features}/{total_features} ({implemented_features/total_features*100:.1f}%)")
        print(f"异常字段问题: {'已解决' if not analysis.get('has_problematic_fields') else '仍存在'}")
        print(f"使用Streamlit原生: {'是' if analysis.get('uses_streamlit_native') else '否'}")
    
    print(f"异常数据处理: {'✅ 成功' if problematic_success else '❌ 失败'}")
    print(f"执行环境模拟: {'✅ 成功' if simulation_success else '❌ 失败'}")
    
    # 5. 提供建议
    print(f"\n💡 建议:")
    if analysis and not analysis.get('has_problematic_fields'):
        print("✅ 数据清理修复已生效，建议重启Streamlit服务测试")
        print("重启步骤:")
        print("1. 停止当前Streamlit服务 (Ctrl+C)")
        print("2. 清理缓存: find . -name '__pycache__' -exec rm -rf {} +")
        print("3. 重启服务: streamlit run streamlit_app.py")
        print("4. 清理浏览器缓存并测试图表查询")
    else:
        print("❌ 修复可能未完全生效，建议检查:")
        print("1. 文件是否正确保存")
        print("2. 模块是否正确重新加载")
        print("3. 是否需要进一步调试")
