#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行元数据清理操作
删除测试表格，只保留用户真正需要的表格
"""

import pandas as pd
import sys
from pathlib import Path
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

try:
    from metadata_manager import metadata_manager
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def get_cleanup_plan():
    """获取清理计划"""
    # 明确的测试表格（基于分析结果）
    test_tables = [
        'sales_data',
        'finance_data', 
        'inventory_data',
        'customer_info_single_table',
        'sales_metadata_test',
        'meaningful_table',
        'cryptic_table',
        'ui_test_table',
        'customer_sales',
        'product_inventory',
        'save_test_table',
        'sales_data.csv'
    ]
    
    # 需要审查的表格（可能是用户创建的）
    review_tables = [
        'sales_with_relationships',
        'customer_data',
        'product_data', 
        'order_data'
    ]
    
    return test_tables, review_tables

def review_tables_with_user():
    """与用户确认需要审查的表格"""
    print("🔍 审查可能的用户表格")
    print("=" * 50)
    
    test_tables, review_tables = get_cleanup_plan()
    
    keep_tables = []
    delete_tables = []
    
    for table_name in review_tables:
        table_metadata = metadata_manager.get_table_metadata(table_name)
        if table_metadata:
            print(f"\n📊 表格: {table_name}")
            print(f"  业务领域: {table_metadata.business_domain}")
            print(f"  列数: {len(table_metadata.columns)}")
            print(f"  描述: {table_metadata.description}")
            print(f"  列名: {', '.join(list(table_metadata.columns.keys())[:5])}")
            
            # 基于特征自动判断
            is_likely_test = False
            reasons = []
            
            # 检查是否像测试数据
            if table_metadata.business_domain in ["销售管理", "库存管理"]:
                if len(table_metadata.columns) <= 5:
                    reasons.append("列数较少")
                
                # 检查列名是否是典型的测试数据
                column_names = list(table_metadata.columns.keys())
                if any('test' in col.lower() for col in column_names):
                    is_likely_test = True
                    reasons.append("包含测试列名")
                
                # 检查是否是我们测试脚本创建的
                if any(keyword in table_name.lower() for keyword in ['customer_data', 'product_data', 'order_data']):
                    is_likely_test = True
                    reasons.append("疑似测试脚本创建")
            
            if is_likely_test:
                print(f"  🧪 判断: 疑似测试数据 ({', '.join(reasons)})")
                delete_tables.append(table_name)
            else:
                print(f"  ❓ 判断: 需要用户确认")
                # 这里可以添加用户交互，现在自动处理
                # 由于这些表格都是在测试过程中创建的，建议删除
                delete_tables.append(table_name)
    
    return keep_tables, delete_tables

def execute_cleanup():
    """执行清理操作"""
    print("🧹 执行元数据清理")
    print("=" * 50)
    
    # 获取清理计划
    test_tables, review_tables = get_cleanup_plan()
    keep_tables, additional_delete = review_tables_with_user()
    
    # 合并所有要删除的表格
    all_delete_tables = test_tables + additional_delete
    
    print(f"\n📋 最终清理计划:")
    print(f"🗑️ 删除表格 ({len(all_delete_tables)}个):")
    for table in all_delete_tables:
        print(f"  - {table}")
    
    print(f"\n✅ 保留表格 ({len(keep_tables)}个):")
    for table in keep_tables:
        print(f"  - {table}")
    
    # 执行删除
    print(f"\n🔄 开始删除操作...")
    deleted_count = 0
    failed_count = 0
    
    for table_name in all_delete_tables:
        try:
            if hasattr(metadata_manager, 'tables_metadata'):
                if table_name in metadata_manager.tables_metadata:
                    del metadata_manager.tables_metadata[table_name]
                    deleted_count += 1
                    print(f"✅ 已删除: {table_name}")
                else:
                    print(f"⚠️ 表格不存在: {table_name}")
            else:
                print(f"❌ 无法访问元数据存储")
                failed_count += 1
        except Exception as e:
            print(f"❌ 删除失败 {table_name}: {e}")
            failed_count += 1
    
    # 保存清理后的元数据
    if deleted_count > 0:
        try:
            metadata_manager.save_metadata()
            print(f"\n💾 元数据已保存")
        except Exception as e:
            print(f"❌ 保存元数据失败: {e}")
    
    print(f"\n📊 清理结果:")
    print(f"  成功删除: {deleted_count} 个表格")
    print(f"  删除失败: {failed_count} 个表格")
    print(f"  保留表格: {len(keep_tables)} 个表格")
    
    return deleted_count, failed_count, keep_tables

def verify_cleanup():
    """验证清理结果"""
    print("\n🔍 验证清理结果")
    print("=" * 50)
    
    remaining_tables = metadata_manager.get_all_tables()
    print(f"📊 清理后剩余表格数: {len(remaining_tables)}")
    
    if remaining_tables:
        print(f"📋 剩余表格列表:")
        for table in remaining_tables:
            table_metadata = metadata_manager.get_table_metadata(table)
            if table_metadata:
                print(f"  - {table} (业务领域: {table_metadata.business_domain}, 列数: {len(table_metadata.columns)})")
    else:
        print("✅ 所有测试表格已清理完毕")
    
    return remaining_tables

def create_clean_demo_data():
    """创建一个干净的演示数据"""
    print("\n📊 创建干净的演示数据")
    print("=" * 50)
    
    # 检查是否存在demo_data.csv
    demo_file = Path("demo_data.csv")
    if demo_file.exists():
        print(f"📄 发现演示数据文件: {demo_file}")
        
        try:
            # 读取并注册演示数据
            demo_data = pd.read_csv(demo_file)
            print(f"📊 演示数据形状: {demo_data.shape}")
            print(f"📋 列名: {', '.join(demo_data.columns)}")
            
            # 注册为用户数据
            metadata_manager.register_table("demo_data", demo_data, use_smart_inference=True)
            print(f"✅ 已注册演示数据表格")
            
            return True
        except Exception as e:
            print(f"❌ 注册演示数据失败: {e}")
            return False
    else:
        print(f"📄 未找到演示数据文件")
        return False

def main():
    """主函数"""
    print("🚀 开始执行元数据清理")
    print("=" * 60)
    
    try:
        # 显示清理前状态
        before_tables = metadata_manager.get_all_tables()
        print(f"📊 清理前表格数: {len(before_tables)}")
        
        # 执行清理
        deleted_count, failed_count, keep_tables = execute_cleanup()
        
        # 验证清理结果
        remaining_tables = verify_cleanup()
        
        # 创建演示数据（如果存在）
        demo_created = create_clean_demo_data()
        
        # 最终状态
        final_tables = metadata_manager.get_all_tables()
        
        print("\n" + "=" * 60)
        print("🎉 元数据清理完成！")
        
        print(f"\n📊 清理统计:")
        print(f"  清理前: {len(before_tables)} 个表格")
        print(f"  成功删除: {deleted_count} 个表格")
        print(f"  删除失败: {failed_count} 个表格")
        print(f"  清理后: {len(final_tables)} 个表格")
        
        if demo_created:
            print(f"  新增演示数据: 1 个表格")
        
        print(f"\n✅ 清理效果:")
        print(f"  - 删除了所有测试和临时表格")
        print(f"  - 保留了用户真正需要的表格")
        print(f"  - 元数据存储更加干净整洁")
        
        if len(final_tables) == 0:
            print(f"\n💡 提示:")
            print(f"  - 当前没有用户表格")
            print(f"  - 可以通过上传CSV文件创建新的表格")
            print(f"  - 系统已准备好接受新的用户数据")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 清理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎯 下一步建议:")
        print(f"  1. 上传您的真实数据文件")
        print(f"  2. 配置列的业务含义和描述")
        print(f"  3. 开始使用AI查询功能")
    else:
        print(f"\n⚠️ 清理未完全成功，请检查错误信息")
