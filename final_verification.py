#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PandasAI V2 最终验证
"""

print("=== PandasAI V2 安装验证 ===")

# 1. 检查基本导入
try:
    import pandas as pd
    print("✅ Pandas导入成功")
    print(f"   版本: {pd.__version__}")
except Exception as e:
    print(f"❌ Pandas导入失败: {e}")
    exit(1)

try:
    import pandasai
    print("✅ PandasAI导入成功")
    
    try:
        from pandasai.__version__ import __version__
        print(f"   版本: {__version__}")
    except:
        print("   版本: 无法获取")
        
except Exception as e:
    print(f"❌ PandasAI导入失败: {e}")
    exit(1)

try:
    from pandasai import SmartDataframe
    print("✅ SmartDataframe导入成功")
except Exception as e:
    print(f"❌ SmartDataframe导入失败: {e}")
    exit(1)

try:
    from pandasai.llm import OpenAI
    print("✅ OpenAI LLM导入成功")
except Exception as e:
    print(f"❌ OpenAI LLM导入失败: {e}")
    exit(1)

# 2. 创建测试数据
print("\n=== 创建测试数据 ===")
try:
    df = pd.DataFrame({
        'name': ['Alice', 'Bob', 'Charlie'],
        'age': [25, 30, 35],
        'salary': [50000, 60000, 70000]
    })
    print("✅ 测试数据创建成功")
    print(df)
except Exception as e:
    print(f"❌ 测试数据创建失败: {e}")
    exit(1)

# 3. 验证安装完成
print("\n=== 安装验证结果 ===")
print("🎉 PandasAI V2 安装成功!")
print("\n已安装组件:")
print("- PandasAI 2.3.2")
print("- SmartDataframe 类")
print("- OpenAI LLM 支持")
print("- Pandas 1.5.3")

print("\n=== 使用说明 ===")
print("""
要开始使用PandasAI V2，您需要:

1. 获取LLM API密钥:
   - OpenAI: https://platform.openai.com/api-keys
   - 通义千问: https://dashscope.console.aliyun.com/

2. 基本使用代码:
```python
import pandas as pd
from pandasai import SmartDataframe
from pandasai.llm import OpenAI

# 配置LLM
llm = OpenAI(api_token="your-api-key")

# 创建SmartDataframe
df = pd.DataFrame(your_data)
smart_df = SmartDataframe(df, config={"llm": llm})

# 自然语言查询
result = smart_df.chat("What is the average salary?")
```

3. 通义千问配置:
```python
llm = OpenAI(
    api_token="your-dashscope-key",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    model="qwen-plus"
)
```
""")

print("\n=== V2 vs V3 差异总结 ===")
print("""
您现在使用的是PandasAI V2 (稳定版):
✅ 使用 SmartDataframe 类
✅ 通过 config 参数配置LLM
✅ 调用方式: smart_df.chat("query")
✅ 内置支持多种LLM
✅ 稳定可靠，适合生产环境

V3 (Beta版) 的主要变化:
- 使用 pai.DataFrame 类
- 全局配置: pai.config.set()
- 需要安装扩展包
- 更复杂的架构

推荐继续使用V2，因为它更稳定且功能完整。
""")

print("\n=== 下一步 ===")
print("1. 查看 pandasai_v2_examples.py 了解详细用法")
print("2. 配置您的LLM API密钥")
print("3. 开始使用自然语言分析数据!")

print("\n" + "=" * 50)
print("安装验证完成! 🎉")
