#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据激活和元数据注册同步修复效果
验证文件上传激活后能正确注册到元数据管理系统
"""

import pandas as pd
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

try:
    from metadata_manager import metadata_manager
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def create_test_data():
    """创建测试数据文件"""
    print("📊 创建测试数据文件")
    print("=" * 50)
    
    # 创建销售数据
    sales_data = pd.DataFrame({
        '订单ID': ['ORD001', 'ORD002', 'ORD003', 'ORD004', 'ORD005'],
        '客户名称': ['张三公司', '李四企业', '王五集团', '赵六商贸', '钱七实业'],
        '产品名称': ['笔记本电脑', '台式电脑', '平板电脑', '手机', '耳机'],
        '销售金额': [8500, 6200, 3200, 2800, 450],
        '销售数量': [2, 3, 5, 4, 10],
        '销售日期': ['2024-01-15', '2024-01-16', '2024-01-17', '2024-01-18', '2024-01-19'],
        '销售区域': ['北京', '上海', '广州', '深圳', '杭州']
    })
    
    # 保存到文件
    upload_dir = Path("uploaded_files")
    upload_dir.mkdir(exist_ok=True)
    
    test_file = upload_dir / "sales_data.csv"
    sales_data.to_csv(test_file, index=False, encoding='utf-8')
    
    print(f"✅ 测试数据已保存到: {test_file}")
    print(f"📊 数据形状: {sales_data.shape}")
    print(f"📋 列名: {', '.join(sales_data.columns)}")
    
    return test_file, sales_data

def simulate_file_loading(file_path, data):
    """模拟文件加载和自动注册过程"""
    print(f"\n🔄 模拟文件加载过程")
    print("=" * 50)
    
    file_name = file_path.name
    print(f"📄 文件名: {file_name}")
    
    # 模拟streamlit_app.py中的逻辑
    # 1. 数据加载到session_state（模拟）
    print(f"1. 数据加载到session_state")
    current_data = data
    current_file = file_name
    
    # 2. 自动注册到元数据管理系统
    print(f"2. 自动注册到元数据管理系统")
    try:
        # 使用文件名（不含扩展名）作为表格名
        table_name = file_name
        if '.' in table_name:
            table_name = table_name.rsplit('.', 1)[0]
        
        print(f"   表格名: {table_name}")
        
        # 检查是否已经注册
        existing_metadata = metadata_manager.get_table_metadata(table_name)
        if not existing_metadata:
            # 自动注册表格到元数据系统
            metadata_manager.register_table(table_name, data, use_smart_inference=True)
            print(f"   ✅ 数据加载成功，已自动注册到元数据系统")
        else:
            print(f"   ✅ 数据加载成功，元数据已存在")
            
        return table_name, True
    except Exception as e:
        print(f"   ❌ 元数据注册失败: {e}")
        return table_name, False

def verify_metadata_registration(table_name):
    """验证元数据注册结果"""
    print(f"\n🔍 验证元数据注册结果")
    print("=" * 50)
    
    # 检查表格是否在元数据管理系统中
    all_tables = metadata_manager.get_all_tables()
    print(f"📋 所有已注册表格: {all_tables}")
    
    if table_name in all_tables:
        print(f"✅ 表格 '{table_name}' 已成功注册")
        
        # 获取详细元数据
        table_metadata = metadata_manager.get_table_metadata(table_name)
        if table_metadata:
            print(f"📊 元数据详情:")
            print(f"   表格名: {table_metadata.table_name}")
            print(f"   业务领域: {table_metadata.business_domain}")
            print(f"   描述: {table_metadata.description}")
            print(f"   列数: {len(table_metadata.columns)}")
            print(f"   版本: {table_metadata.version}")
            
            print(f"\n📋 列信息:")
            for col_name, col_metadata in table_metadata.columns.items():
                print(f"   - {col_name}: {col_metadata.description}")
            
            return True
        else:
            print(f"❌ 无法获取表格元数据")
            return False
    else:
        print(f"❌ 表格 '{table_name}' 未注册")
        return False

def test_metadata_ui_access(table_name):
    """测试元数据UI访问"""
    print(f"\n🎯 测试元数据UI访问")
    print("=" * 50)
    
    # 模拟元数据UI中的表格获取
    try:
        tables = metadata_manager.get_all_tables()
        print(f"📋 元数据UI可见表格: {tables}")
        
        if table_name in tables:
            print(f"✅ 表格 '{table_name}' 在元数据UI中可见")
            print(f"   用户可以在'📋 列管理'标签页中配置此表格")
            return True
        else:
            print(f"❌ 表格 '{table_name}' 在元数据UI中不可见")
            return False
    except Exception as e:
        print(f"❌ 元数据UI访问失败: {e}")
        return False

def test_analyze_data_integration(table_name, data):
    """测试analyze_data集成"""
    print(f"\n🤖 测试analyze_data集成")
    print("=" * 50)
    
    try:
        # 模拟analyze_data调用中的表格名处理
        current_file = f"{table_name}.csv"  # 模拟带扩展名的文件名
        
        # 使用文件名（不含扩展名）作为表格名
        processed_table_name = current_file
        if '.' in processed_table_name:
            processed_table_name = processed_table_name.rsplit('.', 1)[0]
        
        print(f"📄 原始文件名: {current_file}")
        print(f"📊 处理后表格名: {processed_table_name}")
        
        # 检查元数据是否可以正确获取
        table_metadata = metadata_manager.get_table_metadata(processed_table_name)
        if table_metadata:
            print(f"✅ analyze_data可以正确获取元数据")
            print(f"   表格名匹配: {processed_table_name}")
            return True
        else:
            print(f"❌ analyze_data无法获取元数据")
            return False
    except Exception as e:
        print(f"❌ analyze_data集成测试失败: {e}")
        return False

def test_complete_workflow():
    """测试完整工作流程"""
    print(f"\n🚀 测试完整工作流程")
    print("=" * 50)
    
    workflow_steps = [
        "1. 用户上传文件 → 文件保存到uploaded_files目录",
        "2. 用户点击文件按钮 → 数据加载到session_state",
        "3. 自动注册逻辑 → 表格注册到元数据系统",
        "4. 元数据UI → 表格在列管理中可见",
        "5. AI查询 → 可以使用元数据进行分析"
    ]
    
    print("📋 完整工作流程:")
    for step in workflow_steps:
        print(f"   {step}")
    
    print(f"\n✅ 修复效果:")
    print(f"   - 消除了数据激活和元数据注册之间的断层")
    print(f"   - 用户无需手动点击'自动生成'按钮")
    print(f"   - 激活的表格立即在元数据管理中可见")
    print(f"   - AI查询可以立即使用元数据")

def main():
    """主测试函数"""
    print("🚀 开始测试数据激活和元数据注册同步修复")
    print("=" * 60)
    
    try:
        # 1. 创建测试数据
        test_file, test_data = create_test_data()
        
        # 2. 模拟文件加载和自动注册
        table_name, registration_success = simulate_file_loading(test_file, test_data)
        
        # 3. 验证元数据注册
        metadata_success = verify_metadata_registration(table_name)
        
        # 4. 测试元数据UI访问
        ui_access_success = test_metadata_ui_access(table_name)
        
        # 5. 测试analyze_data集成
        integration_success = test_analyze_data_integration(table_name, test_data)
        
        # 6. 测试完整工作流程
        test_complete_workflow()
        
        print("\n" + "=" * 60)
        print("🎉 数据同步修复测试完成！")
        
        print(f"\n📊 测试结果:")
        print(f"   文件加载和注册: {'✅ 成功' if registration_success else '❌ 失败'}")
        print(f"   元数据验证: {'✅ 成功' if metadata_success else '❌ 失败'}")
        print(f"   UI访问测试: {'✅ 成功' if ui_access_success else '❌ 失败'}")
        print(f"   集成测试: {'✅ 成功' if integration_success else '❌ 失败'}")
        
        all_success = all([registration_success, metadata_success, ui_access_success, integration_success])
        
        if all_success:
            print(f"\n🎯 修复效果验证:")
            print(f"   ✅ 数据激活和元数据注册已完全同步")
            print(f"   ✅ 用户上传文件后立即可在元数据管理中配置")
            print(f"   ✅ AI查询可以立即使用元数据功能")
            print(f"   ✅ 消除了用户操作中的断层")
        else:
            print(f"\n⚠️ 部分功能需要进一步检查")
        
        return all_success
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎉 修复验证成功！")
        print(f"现在用户上传文件并激活后，可以立即在元数据管理中配置。")
    else:
        print(f"\n⚠️ 修复验证中发现问题，请检查具体错误。")
