#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的图表修复测试
测试所有修复：调试输出过滤、图表持久化、数据清理
"""

import pandas as pd
import numpy as np
import streamlit as st
from io import StringIO
import sys

def create_problematic_test_data():
    """创建包含所有问题的测试数据"""
    print("🧪 创建包含问题的测试数据")
    print("=" * 50)
    
    # 创建包含各种问题的数据
    data = {
        '产品名称@#$': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro'],  # 特殊字符
        '销售额_start': [6999000, np.inf, 14999000, 1899000],                    # 无穷大值
        '销售额_end': [7999000, 5599000, -np.inf, 2899000],                     # 负无穷大值
        '销售额': [7499000, np.nan, 15499000, 2399000],                         # NaN值
        '销量': [1200, 800, 400, 1500],
        '价格': [6999, 4599, 14999, 1899]
    }
    
    df = pd.DataFrame(data)
    
    print("原始数据（包含所有问题）:")
    print(df)
    print(f"列名: {list(df.columns)}")
    print(f"无穷大值数量: {np.isinf(df.select_dtypes(include=[np.number])).sum().sum()}")
    print(f"NaN值数量: {df.isnull().sum().sum()}")
    print()
    
    return df

def test_debug_output_filtering():
    """测试调试输出过滤功能"""
    print("🔍 测试调试输出过滤功能")
    print("=" * 50)
    
    # 模拟包含调试输出的分析结果
    mock_output = """图表数据:
产品名称___
iPhone     7499000
iPad       5599000
MacBook    15499000
AirPods    2399000
数据类型: int64
数据范围: 330 - 15499000
是否包含NaN: False
是否包含无穷大: False
使用列: 产品列=产品名称___, 销售额列=销售额
执行成功"""
    
    print("原始输出（包含调试信息）:")
    print(mock_output)
    print()
    
    # 测试过滤逻辑
    from result_formatter import EnhancedResultFormatter
    
    # 模拟结果对象
    mock_result = {
        'success': True,
        'output': mock_output,
        'has_chart': False,
        'uses_plotly_native': False,
        'uses_streamlit_native': False
    }
    
    print("🔧 应用调试输出过滤...")
    
    # 检查过滤逻辑
    lines = mock_output.strip().split('\n')
    debug_patterns = [
        '数据范围:', '数据类型:', '数据形状:', '是否包含', 
        'print(', 'dtype:', 'Name:', '图表数据:', '使用列:',
        '数据验证:', '清理后数据:', '原始数据:', '测试数据:',
        '列名映射:', '修复检查:', '执行成功', '生成的代码'
    ]
    
    filtered_lines = []
    for line in lines:
        line = line.strip()
        if not any(pattern in line for pattern in debug_patterns):
            filtered_lines.append(line)
    
    filtered_output = '\n'.join(filtered_lines)
    
    print("过滤后输出:")
    print(filtered_output)
    print()
    
    # 检查是否成功过滤掉调试信息
    problematic_lines = ['数据范围: 330 -', '数据类型:', '是否包含', '使用列:', '执行成功']
    filtered_successfully = all(line not in filtered_output for line in problematic_lines)
    
    if filtered_successfully:
        print("✅ 调试输出过滤成功！")
        print("- ✅ '数据范围: 330 -' 已被过滤")
        print("- ✅ 其他调试信息已被过滤")
        return True
    else:
        print("❌ 调试输出过滤失败")
        return False

def test_chart_persistence_fix():
    """测试图表持久化修复"""
    print("\n🔒 测试图表持久化修复")
    print("=" * 50)
    
    # 创建测试数据
    df = create_problematic_test_data()
    
    try:
        from perfect_tongyi_integration import analyze_data
        
        # 测试查询
        query = "分析各产品销售额，生成柱状图"
        
        print(f"📋 测试查询: {query}")
        print("-" * 30)
        
        # 执行分析
        result = analyze_data(df, query, "chart_persistence_test")
        
        if result and result.get('success'):
            print("✅ 分析执行成功")
            
            # 检查生成的代码
            code = result.get('code', '')
            
            # 检查持久化特征
            persistence_features = {
                '容器包装': 'with st.container():' in code,
                '错误处理': 'try:' in code and 'except' in code,
                '数据清理': 'replace([np.inf, -np.inf], np.nan)' in code,
                '智能列检测': '产品列=' in code or '销售额列=' in code,
                '备用方案': 'st.dataframe' in code or 'st.error' in code
            }
            
            print("\n🔧 持久化特征检查:")
            all_features = True
            for feature, present in persistence_features.items():
                status = "✅" if present else "❌"
                print(f"{status} {feature}")
                if not present:
                    all_features = False
            
            if all_features:
                print("\n🎉 图表持久化修复完整！")
                return True
            else:
                print("\n⚠️ 部分持久化特征缺失")
                return False
        else:
            print(f"❌ 分析失败: {result.get('error') if result else '未知错误'}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_data_cleaning_effectiveness():
    """测试数据清理效果"""
    print("\n🧹 测试数据清理效果")
    print("=" * 50)
    
    # 创建包含异常值的数据
    test_data = pd.DataFrame({
        '产品@#$': ['A', 'B', 'C', 'D'],
        '销售额_start': [1000, np.inf, 2000, 3000],
        '销售额_end': [1500, 2500, -np.inf, 3500],
        '销售额': [1200, np.nan, 2200, 3200]
    })
    
    print("原始数据:")
    print(test_data)
    print(f"无穷大值: {np.isinf(test_data.select_dtypes(include=[np.number])).sum().sum()}")
    print(f"NaN值: {test_data.isnull().sum().sum()}")
    
    # 应用清理逻辑（模拟增强版的清理过程）
    import re
    
    # 1. 处理数值列中的无穷大值和NaN
    numeric_cols = test_data.select_dtypes(include=[np.number]).columns
    for col in numeric_cols:
        test_data[col] = test_data[col].replace([np.inf, -np.inf], np.nan)
        test_data[col] = test_data[col].fillna(0)
        test_data[col] = pd.to_numeric(test_data[col], errors='coerce').fillna(0)
    
    # 2. 清理列名中的特殊字符
    test_data.columns = [re.sub(r'[^\w\u4e00-\u9fff]', '_', str(col)) for col in test_data.columns]
    
    # 3. 处理重复索引
    if test_data.index.duplicated().any():
        test_data = test_data.reset_index(drop=True)
    
    print("\n清理后数据:")
    print(test_data)
    print(f"无穷大值: {np.isinf(test_data.select_dtypes(include=[np.number])).sum().sum()}")
    print(f"NaN值: {test_data.isnull().sum().sum()}")
    print(f"列名: {list(test_data.columns)}")
    
    # 验证清理效果
    is_clean = (
        np.isinf(test_data.select_dtypes(include=[np.number])).sum().sum() == 0 and
        test_data.isnull().sum().sum() == 0 and
        all('_' in col or col.isalnum() or any(c in col for c in '中文字符') for col in test_data.columns)
    )
    
    if is_clean:
        print("\n✅ 数据清理完全成功！")
        print("- ✅ 无穷大值已处理")
        print("- ✅ NaN值已填充")
        print("- ✅ 列名特殊字符已清理")
        return True
    else:
        print("\n❌ 数据清理不完整")
        return False

def main():
    """主测试函数"""
    print("🎯 完整图表修复测试")
    print("=" * 60)
    
    # 执行所有测试
    test_results = []
    
    # 测试1：调试输出过滤
    result1 = test_debug_output_filtering()
    test_results.append(("调试输出过滤", result1))
    
    # 测试2：数据清理效果
    result2 = test_data_cleaning_effectiveness()
    test_results.append(("数据清理效果", result2))
    
    # 测试3：图表持久化修复
    result3 = test_chart_persistence_fix()
    test_results.append(("图表持久化修复", result3))
    
    # 生成测试总结
    print(f"\n📊 测试总结")
    print("=" * 30)
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 所有修复测试通过！")
        print("\n💡 现在应该解决以下问题:")
        print("1. ✅ 调试输出不再显示为产品名称")
        print("2. ✅ 图表不会消失（持久化显示）")
        print("3. ✅ Vega-Lite警告已解决（数据清理）")
        print("4. ✅ 无穷大值和NaN值已处理")
        print("5. ✅ 特殊字符列名已清理")
        
        print(f"\n🚀 建议:")
        print("1. 重启Streamlit应用以加载所有修复")
        print("2. 测试包含特殊字符和异常值的真实数据")
        print("3. 验证控制台不再有Vega-Lite警告")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
