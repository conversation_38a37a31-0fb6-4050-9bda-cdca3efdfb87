#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试缩进修复功能
"""

from perfect_tongyi_integration import Tongyi<PERSON>ianwenLLM

def test_indentation_fix():
    """测试缩进修复功能"""
    
    # 创建LLM实例
    llm = TongyiQianwenLLM()
    
    # 测试有问题的代码（类似于报错的代码）
    problematic_code = """import matplotlib.pyplot as plt
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
print(product_sales)
plt.figure(figsize=(12, 8))
ax = product_sales.plot(kind='bar', color='skyblue')
plt.title('各产品总销售额对比', fontsize=16, fontweight='bold')
plt.xlabel('产品名称', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.grid(True, alpha=0.3)
for i, v in enumerate(product_sales):
ax.text(i, v + 0.02*v, f'{v}', ha='center', va='bottom', fontsize=10)
plt.tight_layout()
max_product = product_sales.idxmax()
max_sales = product_sales.max()
min_product = product_sales.idxmin()
min_sales = product_sales.min()"""

    print("原始代码:")
    print(problematic_code)
    print("\n" + "="*50 + "\n")
    
    # 测试清理和修复
    cleaned_code = llm.clean_code(problematic_code)
    print("清理后的代码:")
    print(cleaned_code)
    print("\n" + "="*50 + "\n")
    
    # 测试语法
    try:
        compile(cleaned_code, '<string>', 'exec')
        print("✅ 代码语法正确！")
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"错误位置: 行 {e.lineno}")
        
        # 显示有问题的行
        lines = cleaned_code.split('\n')
        if e.lineno and e.lineno <= len(lines):
            print(f"问题行: {lines[e.lineno-1]}")

if __name__ == "__main__":
    test_indentation_fix()
