#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整版本集成测试脚本
验证所有依赖和功能是否正常工作
"""

import sys
import os
from pathlib import Path

def test_imports():
    """测试所有必要的导入"""
    print("🧪 测试模块导入...")
    
    try:
        import streamlit
        print("✅ Streamlit导入成功")
    except ImportError as e:
        print(f"❌ Streamlit导入失败: {e}")
        return False
    
    try:
        import pandasai
        print("✅ PandasAI导入成功")
    except ImportError as e:
        print(f"❌ PandasAI导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        import numpy as np
        print("✅ Pandas和Numpy导入成功")
    except ImportError as e:
        print(f"❌ Pandas/Numpy导入失败: {e}")
        return False
    
    try:
        import yaml
        print("✅ PyYAML导入成功")
    except ImportError as e:
        print(f"❌ PyYAML导入失败: {e}")
        return False
    
    try:
        from perfect_tongyi_integration import TongyiQianwenLLM, analyze_data
        print("✅ 通义千问集成模块导入成功")
    except ImportError as e:
        print(f"❌ 通义千问集成模块导入失败: {e}")
        return False
    
    try:
        from working_tongyi_integration import create_smart_dataframe_with_tongyi
        print("✅ 工作集成模块导入成功")
    except ImportError as e:
        print(f"❌ 工作集成模块导入失败: {e}")
        return False
    
    return True

def test_environment():
    """测试环境配置"""
    print("\n🔧 测试环境配置...")
    
    # 检查.env文件
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️ .env文件不存在")
        return False
    
    # 检查API密钥
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("❌ DASHSCOPE_API_KEY未配置")
        return False
    elif api_key == 'your-dashscope-api-key-here':
        print("⚠️ DASHSCOPE_API_KEY使用默认值，请配置真实API密钥")
        return False
    else:
        print("✅ API密钥配置正确")
    
    return True

def test_data_processing():
    """测试数据处理功能"""
    print("\n📊 测试数据处理功能...")
    
    try:
        import pandas as pd
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'name': ['产品A', '产品B', '产品C'],
            'price': [100, 200, 150],
            'quantity': [10, 5, 8]
        })
        
        print("✅ 测试数据创建成功")
        
        # 测试基本操作
        print(f"✅ 数据形状: {test_data.shape}")
        print(f"✅ 数据列: {list(test_data.columns)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据处理测试失败: {e}")
        return False

def test_directories():
    """测试目录结构"""
    print("\n📁 测试目录结构...")
    
    directories = [
        "uploaded_files",
        "chat_history", 
        "charts",
        "temp"
    ]
    
    for directory in directories:
        dir_path = Path(directory)
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"✅ 目录存在: {directory}")
    
    return True

def test_streamlit_app():
    """测试Streamlit应用文件"""
    print("\n🎯 测试Streamlit应用文件...")
    
    app_file = Path("streamlit_app.py")
    if not app_file.exists():
        print("❌ streamlit_app.py文件不存在")
        return False
    
    print("✅ streamlit_app.py文件存在")
    
    # 检查文件内容
    try:
        with open(app_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        if 'TongyiQianwenLLM' in content:
            print("✅ 包含通义千问集成")
        else:
            print("⚠️ 未找到通义千问集成")
            
        if 'analyze_data' in content:
            print("✅ 包含数据分析功能")
        else:
            print("⚠️ 未找到数据分析功能")
            
        return True
        
    except Exception as e:
        print(f"❌ 读取应用文件失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🤖 智能数据分析助手 - 完整版本集成测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("环境配置", test_environment),
        ("数据处理", test_data_processing),
        ("目录结构", test_directories),
        ("应用文件", test_streamlit_app)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 执行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！完整版本可以正常使用")
        print("🚀 可以运行: streamlit run streamlit_app.py")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
