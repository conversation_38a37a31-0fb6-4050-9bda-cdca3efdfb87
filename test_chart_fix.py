#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图表修复效果
"""

import streamlit as st
import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

try:
    from perfect_tongyi_integration import analyze_data
    from result_formatter import EnhancedResultFormatter
    
    st.title("🧪 图表修复测试")
    
    # 创建测试数据
    data = {
        '地区': ['北京', '上海', '广州', '深圳'],
        '销售额': [23400, 13980, 15300, 20700]
    }
    df = pd.DataFrame(data)
    
    st.subheader("📊 测试数据")
    st.dataframe(df)
    
    if st.button("🚀 测试饼图生成"):
        with st.spinner("正在生成图表..."):
            result = analyze_data(df, '请分析2024年各地区的销售总额', 'sales_data', use_metadata=True)
            
            if result and result.get('success'):
                st.success("✅ 分析完成！")
                
                # 显示生成的代码
                if result.get('code'):
                    with st.expander("📝 生成的代码", expanded=True):
                        st.code(result['code'], language='python')
                
                # 显示结果状态
                st.subheader("📊 结果状态")
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("成功", "✅" if result.get('success') else "❌")
                
                with col2:
                    st.metric("Plotly原生", "✅" if result.get('uses_plotly_native') else "❌")
                
                with col3:
                    st.metric("有额外图表", "✅" if result.get('has_chart') else "❌")
                
                # 使用格式化器显示结果
                if result.get('output'):
                    st.subheader("📋 分析结果")
                    EnhancedResultFormatter.format_and_display_result(result)
                
                # 验证修复效果
                code = result.get('code', '')
                has_st_plotly_chart = 'st.plotly_chart' in code
                has_plotly_fig = 'fig = px.' in code or 'fig=px.' in code
                
                if has_plotly_fig and has_st_plotly_chart:
                    st.success("🎉 修复成功！Plotly图表应该正确显示了")
                elif has_plotly_fig and not has_st_plotly_chart:
                    st.error("❌ 仍有问题：创建了fig但没有调用st.plotly_chart")
                else:
                    st.info("ℹ️ 没有使用Plotly图表")
            
            else:
                st.error("❌ 分析失败")
                if result:
                    st.error(f"错误: {result.get('error', '未知错误')}")
    
    st.markdown("---")
    st.markdown("### 📖 说明")
    st.markdown("""
    这个测试验证了图表生成修复的效果：
    
    1. **修复前的问题**：AI生成的代码以 `fig` 结尾，导致图表不显示
    2. **修复后的效果**：自动检测并修复，确保使用 `st.plotly_chart(fig, use_container_width=True)`
    3. **预期结果**：应该看到一个正确显示的饼图
    """)

except ImportError as e:
    st.error(f"❌ 导入失败: {e}")
    st.error("请确保所有依赖都已正确安装")
