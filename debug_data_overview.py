#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据概览问题
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data
from result_formatter import EnhancedResultFormatter

def test_data_overview():
    """测试数据概览功能"""
    print("🔍 测试数据概览功能")
    print("=" * 50)
    
    # 加载测试数据
    try:
        df = pd.read_csv('demo_data.csv')
        print(f"✅ 数据加载成功: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print()
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 测试查询
    query = "显示数据的基本统计信息和概览"
    print(f"🔍 查询: {query}")
    print("-" * 30)
    
    # 调用分析函数
    result = analyze_data(df, query)
    
    if result:
        print(f"✅ 分析成功: {result.get('success')}")
        print(f"📝 生成的代码:")
        print(result.get('code', '无代码'))
        print()
        
        print(f"📊 原始输出:")
        output = result.get('output', '')
        print(repr(output))  # 使用repr显示原始字符串
        print()
        
        print(f"📋 输出内容:")
        print(output)
        print()
        
        # 测试格式化器检测
        if output:
            output_type = EnhancedResultFormatter._detect_output_type(output)
            print(f"🎯 检测到的类型: {output_type}")
            
            # 模拟格式化显示
            print("\n🎨 格式化显示效果:")
            print("-" * 30)
            try:
                # 这里我们不能直接调用Streamlit函数，但可以检查逻辑
                if output_type == 'dataframe_info':
                    print("✅ 将显示为DataFrame信息格式")
                    # 解析基本信息
                    lines = output.split('\n')
                    for line in lines:
                        if 'entries' in line:
                            print(f"  📏 数据行数: 从 '{line}' 中提取")
                        elif 'Data columns' in line:
                            print(f"  📋 数据列数: 从 '{line}' 中提取")
                else:
                    print(f"⚠️ 将显示为 {output_type} 格式")
            except Exception as e:
                print(f"❌ 格式化测试失败: {e}")
        else:
            print("❌ 没有输出内容")
    else:
        print("❌ 分析失败")

def test_manual_info():
    """手动测试df.info()输出"""
    print("\n🧪 手动测试df.info()输出")
    print("=" * 50)
    
    try:
        df = pd.read_csv('demo_data.csv')
        
        # 捕获df.info()输出
        import io
        import sys
        
        buffer = io.StringIO()
        df.info(buf=buffer)
        info_output = buffer.getvalue()
        
        print("📊 df.info()输出:")
        print(info_output)
        print()
        
        # 测试检测
        output_type = EnhancedResultFormatter._detect_output_type(info_output)
        print(f"🎯 检测类型: {output_type}")
        
        if output_type == 'dataframe_info':
            print("✅ 检测正确！")
        else:
            print("❌ 检测失败，需要改进检测逻辑")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_data_overview()
    test_manual_info()
