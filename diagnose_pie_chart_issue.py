#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断饼图生成问题
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import warnings
from perfect_tongyi_integration import analyze_data

def test_pie_chart_code():
    """测试饼图代码执行"""
    print("🔍 诊断饼图生成问题")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据加载成功: {df.shape}")
    print(f"📋 数据预览:")
    print(df.head())
    print()
    
    # 测试您提供的饼图代码
    print("🧪 测试原始饼图代码")
    print("-" * 30)
    
    original_code = """
import matplotlib.pyplot as plt
plt.figure(figsize=(12, 8))
sales_by_product = df.groupby('产品名称')['销售金额'].sum()
sales_by_product.plot(kind='pie', autopct='%1.1f%%')
plt.title('销售金额分布饼图', fontsize=16, fontweight='bold')
plt.ylabel('销售金额', fontsize=12)
plt.tight_layout()
"""
    
    print("📝 原始代码:")
    print(original_code)
    
    # 手动执行代码测试
    try:
        # 设置matplotlib后端
        matplotlib.use('Agg')
        warnings.filterwarnings('ignore')
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        
        print("🚀 手动执行代码...")
        
        # 执行代码
        plt.figure(figsize=(12, 8))
        sales_by_product = df.groupby('产品名称')['销售金额'].sum()
        print(f"📊 销售数据:")
        print(sales_by_product)
        
        sales_by_product.plot(kind='pie', autopct='%1.1f%%')
        plt.title('销售金额分布饼图', fontsize=16, fontweight='bold')
        plt.ylabel('销售金额', fontsize=12)
        plt.tight_layout()
        
        # 保存图表
        plt.savefig('test_pie_chart.png', dpi=300, bbox_inches='tight')
        print("✅ 饼图生成成功: test_pie_chart.png")
        plt.close()
        
    except Exception as e:
        print(f"❌ 手动执行失败: {e}")
        import traceback
        traceback.print_exc()

def test_corrected_pie_chart_code():
    """测试修正后的饼图代码"""
    print("\n🔧 测试修正后的饼图代码")
    print("-" * 30)
    
    # 修正后的代码（添加save_chart调用）
    corrected_code = """
import matplotlib.pyplot as plt
plt.figure(figsize=(12, 8))
sales_by_product = df.groupby('产品名称')['销售金额'].sum()
print("销售金额分布:")
print(sales_by_product)
sales_by_product.plot(kind='pie', autopct='%1.1f%%')
plt.title('销售金额分布饼图', fontsize=16, fontweight='bold')
plt.ylabel('')  # 饼图不需要y轴标签
plt.tight_layout()
save_chart()
"""
    
    print("📝 修正后的代码:")
    print(corrected_code)
    
    # 使用analyze_data函数测试
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    print("🚀 使用analyze_data函数测试...")
    try:
        result = analyze_data(df, "请为我生成销售金额分布的饼图", table_name="sales_data")
        
        if result and result.get('success'):
            print("✅ 修正后的查询成功")
            print(f"📝 生成的代码:")
            print(result.get('code', ''))
            print(f"📊 输出:")
            print(result.get('output', ''))
            if result.get('has_chart'):
                print("📊 图表已生成")
        else:
            print("❌ 修正后的查询失败")
            if result:
                print(f"错误: {result.get('error', '未知错误')}")
                
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_consecutive_queries():
    """测试连续查询"""
    print("\n🔄 测试完整的连续查询流程")
    print("=" * 50)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 第一次查询
    print("1️⃣ 第一次查询: 分析2024年各产品销售量")
    result1 = analyze_data(df, "分析2024年各产品销售量", table_name="sales_data")
    
    if result1 and result1.get('success'):
        print("✅ 第一次查询成功")
        print(f"📊 输出长度: {len(result1.get('output', ''))}")
        print(f"📊 有图表: {result1.get('has_chart', False)}")
    else:
        print("❌ 第一次查询失败")
    
    print("\n" + "-" * 30)
    
    # 第二次查询
    print("2️⃣ 第二次查询: 请为我生成饼图")
    result2 = analyze_data(df, "请为我生成销售金额分布的饼图", table_name="sales_data")
    
    if result2 and result2.get('success'):
        print("✅ 第二次查询成功")
        print(f"📝 生成的代码:")
        print(result2.get('code', ''))
        print(f"📊 输出:")
        print(result2.get('output', ''))
        print(f"📊 有图表: {result2.get('has_chart', False)}")
    else:
        print("❌ 第二次查询失败")
        if result2:
            print(f"错误: {result2.get('error', '未知错误')}")

if __name__ == "__main__":
    test_pie_chart_code()
    test_corrected_pie_chart_code()
    test_consecutive_queries()
