#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复效果
"""

import pandas as pd
import warnings
from perfect_tongyi_integration import analyze_data
from result_formatter import EnhancedResultFormatter

def test_font_warning_suppression():
    """测试字体警告抑制"""
    print("🔍 测试字体警告抑制")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 执行AI分析
    query = "分析2024年各产品总销售额，并生成柱状图"
    print(f"🔍 查询: {query}")
    
    # 捕获stderr来检查警告
    import io
    import sys
    from contextlib import redirect_stderr
    
    error_buffer = io.StringIO()
    
    with redirect_stderr(error_buffer):
        result = analyze_data(df, query)
    
    error_output = error_buffer.getvalue()
    
    if error_output:
        print("❌ 仍有警告输出:")
        print(error_output[:500] + "..." if len(error_output) > 500 else error_output)
    else:
        print("✅ 警告已完全抑制")
    
    return result

def test_output_type_detection():
    """测试输出类型检测"""
    print("\n🔍 测试输出类型检测")
    print("=" * 50)
    
    # 测试实际的AI输出格式
    actual_output = """    产品名称    销售额
0   台式电脑  20200
1   平板电脑   6800
2     手机   9700
3    显示器   4700
4   智能手术   3800
5  笔记本电脑  25500
6     耳机   1700
7     键盘    650
8     鼠标    330"""

    print("📊 测试输出:")
    print(actual_output)
    
    # 检测类型
    output_type = EnhancedResultFormatter._detect_output_type(actual_output)
    print(f"\n🎯 检测结果: {output_type}")
    
    if output_type == 'series_data':
        print("✅ 正确识别为序列数据")
    else:
        print(f"❌ 识别错误，应该是 series_data，实际是 {output_type}")
    
    return output_type

def test_complete_workflow():
    """测试完整工作流程"""
    print("\n🔍 测试完整工作流程")
    print("=" * 50)
    
    # 1. 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"✅ 数据加载: {df.shape}")
    
    # 2. AI分析
    query = "分析2024年各产品总销售额"
    result = analyze_data(df, query)
    
    if result and result.get('success'):
        print("✅ AI分析成功")
        
        # 3. 检查输出
        output = result.get('output', '')
        if output:
            print("✅ 有分析输出")
            
            # 4. 检查输出类型
            output_type = EnhancedResultFormatter._detect_output_type(output)
            print(f"🎯 输出类型: {output_type}")
            
            # 5. 检查数据格式
            if '产品名称' in output and '销售额' in output:
                print("✅ 数据格式正确")
            else:
                print("❌ 数据格式有问题")
                
            # 6. 检查图表
            if result.get('has_chart'):
                print("✅ 图表已生成")
            else:
                print("❌ 未生成图表")
                
        else:
            print("❌ 无分析输出")
    else:
        print("❌ AI分析失败")
        if result:
            print(f"错误: {result.get('error', '未知错误')}")

def main():
    """主测试函数"""
    print("🎉 验证修复效果")
    print("=" * 60)
    
    # 1. 测试字体警告抑制
    result = test_font_warning_suppression()
    
    # 2. 测试输出类型检测
    output_type = test_output_type_detection()
    
    # 3. 测试完整工作流程
    test_complete_workflow()
    
    print("\n" + "=" * 60)
    print("🎯 修复验证总结:")
    
    fixes = [
        ("字体警告抑制", "检查stderr输出"),
        ("输出类型检测", f"期望series_data，实际{output_type}"),
        ("数据格式显示", "检查产品名称和销售额"),
        ("图表生成", "检查has_chart标志"),
    ]
    
    for fix, status in fixes:
        print(f"  {fix}: {status}")
    
    print("\n💡 如果仍有问题，请检查:")
    print("1. Streamlit应用是否使用了最新的代码")
    print("2. 是否需要重启Streamlit应用")
    print("3. 浏览器缓存是否需要清除")

if __name__ == "__main__":
    main()
