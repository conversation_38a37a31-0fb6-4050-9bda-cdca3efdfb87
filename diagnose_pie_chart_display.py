#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断饼图显示问题
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import warnings
import os

def test_pie_chart_generation():
    """测试饼图生成"""
    print("🔍 诊断饼图显示问题")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据加载成功: {df.shape}")
    
    # 设置matplotlib
    matplotlib.use('Agg')
    warnings.filterwarnings('ignore')
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    print("\n🧪 测试您的饼图代码")
    print("-" * 30)
    
    # 执行您的代码
    try:
        product_sales = df.groupby('产品名称')['销售金额'].sum()
        print("📊 销售数据:")
        print(product_sales)
        
        plt.figure(figsize=(12, 8))
        plt.pie(product_sales.values, labels=product_sales.index, autopct='%1.1f%%', startangle=90)
        plt.title('2024年各产品销售额占比', fontsize=16, fontweight='bold')
        plt.axis('equal')
        plt.tight_layout()
        
        # 保存图表
        plt.savefig('test_pie_chart_direct.png', dpi=300, bbox_inches='tight')
        print("✅ 饼图生成成功: test_pie_chart_direct.png")
        plt.close()
        
    except Exception as e:
        print(f"❌ 饼图生成失败: {e}")
        import traceback
        traceback.print_exc()

def test_different_pie_chart_styles():
    """测试不同的饼图样式"""
    print("\n🎨 测试不同的饼图样式")
    print("-" * 30)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    product_sales = df.groupby('产品名称')['销售金额'].sum()
    
    # 样式1：基础饼图
    try:
        plt.figure(figsize=(10, 8))
        plt.pie(product_sales.values, labels=product_sales.index, autopct='%1.1f%%')
        plt.title('基础饼图', fontsize=16)
        plt.axis('equal')
        plt.savefig('pie_style1_basic.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 基础饼图生成成功")
    except Exception as e:
        print(f"❌ 基础饼图失败: {e}")
    
    # 样式2：带颜色的饼图
    try:
        plt.figure(figsize=(10, 8))
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc']
        plt.pie(product_sales.values, labels=product_sales.index, autopct='%1.1f%%', 
                colors=colors, startangle=90)
        plt.title('彩色饼图', fontsize=16)
        plt.axis('equal')
        plt.savefig('pie_style2_colored.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 彩色饼图生成成功")
    except Exception as e:
        print(f"❌ 彩色饼图失败: {e}")
    
    # 样式3：分离饼图
    try:
        plt.figure(figsize=(10, 8))
        explode = (0.1, 0, 0, 0, 0)  # 突出显示第一个扇形
        plt.pie(product_sales.values, labels=product_sales.index, autopct='%1.1f%%',
                explode=explode, startangle=90, shadow=True)
        plt.title('分离饼图', fontsize=16)
        plt.axis('equal')
        plt.savefig('pie_style3_exploded.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✅ 分离饼图生成成功")
    except Exception as e:
        print(f"❌ 分离饼图失败: {e}")

def check_matplotlib_backend():
    """检查matplotlib后端设置"""
    print("\n🔧 检查matplotlib配置")
    print("-" * 30)
    
    print(f"当前后端: {matplotlib.get_backend()}")
    print(f"matplotlib版本: {matplotlib.__version__}")
    print(f"可用后端: {matplotlib.backend_bases.Backend._backend_names if hasattr(matplotlib.backend_bases.Backend, '_backend_names') else '无法获取'}")
    
    # 检查是否有GUI后端干扰
    gui_backends = ['TkAgg', 'Qt5Agg', 'Qt4Agg', 'GTKAgg']
    current_backend = matplotlib.get_backend()
    
    if current_backend in gui_backends:
        print(f"⚠️ 当前使用GUI后端: {current_backend}")
        print("建议切换到Agg后端以避免显示问题")
    else:
        print(f"✅ 当前使用非GUI后端: {current_backend}")

def test_with_analyze_data():
    """使用analyze_data函数测试"""
    print("\n🧪 使用analyze_data函数测试饼图")
    print("-" * 30)
    
    from perfect_tongyi_integration import analyze_data
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 明确要求饼图
    queries = [
        "生成销售金额的饼图，必须是饼图不是柱状图",
        "用pie chart显示产品销售金额分布",
        "创建一个圆形饼图显示各产品销售占比"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n{i}️⃣ 测试查询: {query}")
        try:
            result = analyze_data(df, query, table_name="sales_data")
            
            if result and result.get('success'):
                code = result.get('code', '')
                print(f"📝 生成的代码:")
                print(code)
                
                # 检查代码类型
                if 'plt.pie(' in code:
                    print("✅ 代码确实是饼图")
                elif 'plt.bar(' in code or '.plot(kind=' in code:
                    print("❌ 代码是柱状图，不是饼图")
                else:
                    print("❓ 代码类型不明确")
                
                has_chart = result.get('has_chart', False)
                print(f"📈 图表生成: {'✅' if has_chart else '❌'}")
                
            else:
                print("❌ 查询失败")
                
        except Exception as e:
            print(f"❌ 查询异常: {e}")

def check_chart_files():
    """检查生成的图表文件"""
    print("\n📁 检查生成的图表文件")
    print("-" * 30)
    
    chart_files = [
        'test_pie_chart_direct.png',
        'pie_style1_basic.png', 
        'pie_style2_colored.png',
        'pie_style3_exploded.png'
    ]
    
    for file in chart_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {file} - {size} bytes")
        else:
            print(f"❌ {file} - 不存在")
    
    # 检查charts目录
    if os.path.exists('charts'):
        print(f"\n📁 charts目录内容:")
        for file in os.listdir('charts'):
            if file.endswith('.png'):
                size = os.path.getsize(os.path.join('charts', file))
                print(f"  📊 {file} - {size} bytes")

if __name__ == "__main__":
    test_pie_chart_generation()
    test_different_pie_chart_styles()
    check_matplotlib_backend()
    test_with_analyze_data()
    check_chart_files()
