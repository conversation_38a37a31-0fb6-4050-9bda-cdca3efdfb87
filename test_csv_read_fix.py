#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试CSV读取问题修复
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_no_csv_read_in_code():
    """测试生成的代码不包含CSV读取"""
    print("🧪 测试生成的代码不包含CSV读取")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据加载成功: {df.shape}")
    
    # 测试各种查询
    queries = [
        "请为我分析2024年各产品销售额，并用饼图展示",
        "分析各产品的销售数据",
        "生成销售金额分布的饼图",
        "计算各产品的总销售额",
        "显示销售数量最多的产品"
    ]
    
    for i, query in enumerate(queries, 1):
        print(f"\n{i}️⃣ 测试查询: {query}")
        print("-" * 40)
        
        try:
            result = analyze_data(df, query, table_name="sales_data")
            
            if result and result.get('success'):
                print("✅ 查询成功")
                
                # 检查生成的代码
                code = result.get('code', '')
                print(f"📝 生成的代码:")
                print(code)
                
                # 检查是否包含不应该有的内容
                has_read_csv = 'read_csv' in code
                has_read_excel = 'read_excel' in code
                has_file_read = any(pattern in code for pattern in [
                    'pd.read_', 'pandas.read_', '.csv', '.xlsx', '.xls'
                ])
                
                print(f"\n🔍 代码检查:")
                print(f"  ❌ 包含read_csv: {'是' if has_read_csv else '否'}")
                print(f"  ❌ 包含read_excel: {'是' if has_read_excel else '否'}")
                print(f"  ❌ 包含文件读取: {'是' if has_file_read else '否'}")
                
                # 检查输出
                output = result.get('output', '')
                print(f"\n📊 执行输出:")
                if output.strip():
                    print(output)
                else:
                    print("  (无输出)")
                
                # 检查图表
                has_chart = result.get('has_chart', False)
                print(f"\n📈 图表生成: {'✅' if has_chart else '❌'}")
                
                if not has_file_read and output.strip():
                    print("🎉 测试通过：代码正确且有输出")
                elif not has_file_read:
                    print("⚠️ 代码正确但无输出")
                else:
                    print("❌ 代码包含不应该有的文件读取操作")
                    
            else:
                print("❌ 查询失败")
                if result:
                    print(f"错误: {result.get('error', '未知错误')}")
                    
        except Exception as e:
            print(f"❌ 查询异常: {e}")
            import traceback
            traceback.print_exc()

def test_specific_problematic_query():
    """测试具体的问题查询"""
    print("\n🎯 测试具体的问题查询")
    print("=" * 50)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 测试您提到的具体查询
    query = "请为我分析2024年各产品销售额，并用饼图展示"
    print(f"🔍 查询: {query}")
    
    try:
        result = analyze_data(df, query, table_name="sales_data")
        
        if result and result.get('success'):
            print("✅ 查询成功")
            
            code = result.get('code', '')
            output = result.get('output', '')
            has_chart = result.get('has_chart', False)
            
            print(f"\n📝 生成的代码:")
            print(code)
            
            # 详细检查代码
            lines = code.split('\n')
            problematic_lines = []
            for i, line in enumerate(lines, 1):
                if any(pattern in line for pattern in ['read_csv', 'read_excel', '.csv', '.xlsx']):
                    problematic_lines.append(f"第{i}行: {line.strip()}")
            
            if problematic_lines:
                print(f"\n❌ 发现问题代码行:")
                for line in problematic_lines:
                    print(f"  {line}")
            else:
                print(f"\n✅ 代码检查通过：无文件读取操作")
            
            print(f"\n📊 执行输出:")
            print(output if output.strip() else "(无输出)")
            
            print(f"\n📈 图表生成: {'✅' if has_chart else '❌'}")
            
            if not problematic_lines and output.strip() and has_chart:
                print("\n🎉 完美！所有功能都正常工作")
            elif not problematic_lines:
                print("\n✅ 代码正确，但可能缺少输出或图表")
            else:
                print("\n❌ 代码仍有问题，需要进一步修复")
                
        else:
            print("❌ 查询失败")
            if result:
                print(f"错误: {result.get('error', '未知错误')}")
                
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_no_csv_read_in_code()
    test_specific_problematic_query()
