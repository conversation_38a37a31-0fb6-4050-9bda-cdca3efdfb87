#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速重启修复脚本
"""

import os
import subprocess
import time

def quick_restart_fix():
    """快速重启修复"""
    print("🚀 快速重启修复脚本")
    print("=" * 50)
    
    print("1️⃣ 检查当前状态...")
    
    # 检查是否有Streamlit进程运行
    try:
        result = subprocess.run(['pgrep', '-f', 'streamlit'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 检测到运行中的Streamlit进程")
            pids = result.stdout.strip().split('\n')
            print(f"进程ID: {', '.join(pids)}")
        else:
            print("ℹ️ 未检测到运行中的Streamlit进程")
    except FileNotFoundError:
        print("ℹ️ 无法检查进程状态（Windows系统）")
    
    print("\n2️⃣ 清理Python缓存...")
    
    # 清理缓存
    cache_cleaned = 0
    for root, dirs, files in os.walk('.'):
        # 删除__pycache__目录
        if '__pycache__' in dirs:
            cache_dir = os.path.join(root, '__pycache__')
            try:
                import shutil
                shutil.rmtree(cache_dir)
                cache_cleaned += 1
                print(f"  删除缓存目录: {cache_dir}")
            except Exception as e:
                print(f"  删除缓存失败: {cache_dir} - {e}")
        
        # 删除.pyc文件
        for file in files:
            if file.endswith('.pyc'):
                pyc_file = os.path.join(root, file)
                try:
                    os.remove(pyc_file)
                    print(f"  删除缓存文件: {pyc_file}")
                except Exception as e:
                    print(f"  删除文件失败: {pyc_file} - {e}")
    
    print(f"✅ 清理了 {cache_cleaned} 个缓存目录")
    
    print("\n3️⃣ 验证修复状态...")
    
    # 验证关键文件
    key_files = [
        'enhanced_tongyi_integration.py',
        'perfect_tongyi_integration.py',
        'streamlit_app.py'
    ]
    
    for file_path in key_files:
        if os.path.exists(file_path):
            mtime = os.path.getmtime(file_path)
            mtime_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime))
            print(f"✅ {file_path}: {mtime_str}")
        else:
            print(f"❌ 缺少文件: {file_path}")
    
    print("\n4️⃣ 生成重启命令...")
    
    restart_commands = {
        'Linux/Mac': [
            "# 停止Streamlit服务（如果正在运行）",
            "pkill -f streamlit 2>/dev/null || echo '没有运行中的Streamlit进程'",
            "",
            "# 等待进程完全停止",
            "sleep 2",
            "",
            "# 重启Streamlit服务",
            "streamlit run streamlit_app.py --server.port 8501 --server.address 0.0.0.0"
        ],
        'Windows': [
            "REM 停止Streamlit服务（如果正在运行）",
            "taskkill /f /im python.exe 2>nul || echo 没有运行中的Python进程",
            "",
            "REM 等待进程完全停止",
            "timeout /t 2 /nobreak >nul",
            "",
            "REM 重启Streamlit服务",
            "streamlit run streamlit_app.py --server.port 8501 --server.address 0.0.0.0"
        ]
    }
    
    # 保存重启脚本
    for platform, commands in restart_commands.items():
        if platform == 'Linux/Mac':
            script_name = 'restart_streamlit.sh'
            with open(script_name, 'w') as f:
                f.write('#!/bin/bash\n')
                f.write('echo "🔄 重启Streamlit服务..."\n\n')
                for cmd in commands:
                    f.write(cmd + '\n')
                f.write('\necho "✅ Streamlit服务已重启"\n')
            
            # 设置执行权限
            try:
                os.chmod(script_name, 0o755)
                print(f"✅ 生成重启脚本: {script_name}")
            except:
                print(f"⚠️ 生成脚本但无法设置权限: {script_name}")
        
        elif platform == 'Windows':
            script_name = 'restart_streamlit.bat'
            with open(script_name, 'w') as f:
                f.write('@echo off\n')
                f.write('echo 🔄 重启Streamlit服务...\n\n')
                for cmd in commands:
                    f.write(cmd + '\n')
                f.write('\necho ✅ Streamlit服务已重启\n')
            
            print(f"✅ 生成重启脚本: {script_name}")
    
    print("\n5️⃣ 提供测试建议...")
    
    test_queries = [
        "请分析各产品销售额，用柱状图展示",
        "用Streamlit原生柱状图分析产品销售数据",
        "使用st.bar_chart展示销售额对比"
    ]
    
    print("建议的测试查询（按优先级排序）:")
    for i, query in enumerate(test_queries, 1):
        print(f"  {i}. {query}")
    
    print("\n🎯 重启后的预期结果:")
    print("  ✅ 图表稳定显示，不再闪退")
    print("  ✅ 控制台无 'Infinite extent' 警告")
    print("  ✅ 控制台无 'Scale bindings' 警告")
    print("  ✅ 无异常字段 (销售额_start/销售额_end)")
    
    print("\n📋 重启步骤:")
    print("1. 在运行Streamlit的终端按 Ctrl+C 停止服务")
    print("2. 运行重启脚本: ./restart_streamlit.sh (Linux/Mac) 或 restart_streamlit.bat (Windows)")
    print("3. 或手动执行: streamlit run streamlit_app.py")
    print("4. 在浏览器中按 Ctrl+Shift+R 强制刷新")
    print("5. 测试图表查询并观察控制台")
    
    print("\n💡 如果问题持续:")
    print("1. 检查生成的代码是否使用了Streamlit原生方法")
    print("2. 尝试使用更明确的查询（如'用st.bar_chart展示数据'）")
    print("3. 提供重启后的具体错误信息进行进一步诊断")

if __name__ == "__main__":
    quick_restart_fix()
    
    print("\n" + "=" * 50)
    print("🎉 快速修复准备完成！")
    print("现在请重启Streamlit服务并测试图表功能。")
    print("根据我们的测试，异常字段问题已经解决，")
    print("重启后应该能显著改善图表显示效果。")
