#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试显示问题
"""

from result_formatter import EnhancedResultFormatter

def test_index_parsing():
    """测试索引解析问题"""
    print("🔍 测试DataFrame索引解析问题")
    print("=" * 50)
    
    # 模拟实际的AI输出（包含DataFrame索引）
    actual_output = """    产品名称    销售额
0   台式电脑  20200
1   平板电脑   6800
2     手机   9700
3    显示器   4700
4   智能手表   3800
5  笔记本电脑  25500
6     耳机   1700
7     键盘    650
8     鼠标    330"""

    print("📊 实际AI输出:")
    print(actual_output)
    print()
    
    # 分析解析过程
    lines = actual_output.strip().split('\n')
    data_dict = {}
    
    print("🔍 逐行解析过程:")
    for i, line in enumerate(lines):
        line = line.strip()
        print(f"行{i+1}: '{line}'")
        
        if line and not line.startswith('Name:') and not line.startswith('dtype:'):
            # 当前的解析逻辑
            parts = line.rsplit(None, 1)  # 从右边分割
            print(f"  分割结果: {parts}")
            
            if len(parts) == 2:
                try:
                    key = parts[0]
                    value = float(parts[1])
                    data_dict[key] = value
                    print(f"  ✅ 解析成功: {key} = {value}")
                except ValueError as e:
                    print(f"  ❌ 解析失败: {e}")
                    continue
            else:
                print(f"  ⚠️ 分割结果不是2部分，跳过")
        else:
            print(f"  ⚠️ 跳过此行（Name/dtype或空行）")
        print()
    
    print("📋 最终解析结果:")
    for key, value in data_dict.items():
        print(f"  {key}: {value}")
    
    print(f"\n🎯 问题分析:")
    print("当前解析逻辑将 '0   台式电脑' 作为key，'20200' 作为value")
    print("这导致产品名称前面带有索引号")

def test_correct_parsing():
    """测试正确的解析方法"""
    print("\n🔧 测试正确的解析方法")
    print("=" * 50)
    
    actual_output = """    产品名称    销售额
0   台式电脑  20200
1   平板电脑   6800
2     手机   9700
3    显示器   4700
4   智能手表   3800
5  笔记本电脑  25500
6     耳机   1700
7     键盘    650
8     鼠标    330"""

    lines = actual_output.strip().split('\n')
    data_dict = {}
    
    print("🔍 改进的解析过程:")
    for i, line in enumerate(lines):
        line = line.strip()
        print(f"行{i+1}: '{line}'")
        
        if line and not line.startswith('Name:') and not line.startswith('dtype:'):
            # 改进的解析逻辑：跳过表头，处理DataFrame格式
            if i == 0:  # 跳过表头
                print("  ⚠️ 跳过表头")
                continue
                
            # 对于DataFrame格式：索引 产品名称 销售额
            parts = line.split()
            print(f"  分割结果: {parts}")
            
            if len(parts) >= 3:
                # 跳过索引，取产品名称和销售额
                try:
                    index = parts[0]  # 索引（跳过）
                    product_name = parts[1]  # 产品名称
                    sales_amount = float(parts[2])  # 销售额
                    
                    data_dict[product_name] = sales_amount
                    print(f"  ✅ 解析成功: {product_name} = {sales_amount} (跳过索引 {index})")
                except ValueError as e:
                    print(f"  ❌ 解析失败: {e}")
                    continue
            else:
                print(f"  ⚠️ 分割结果少于3部分，跳过")
        print()
    
    print("📋 改进后的解析结果:")
    for key, value in data_dict.items():
        print(f"  {key}: {value}")
    
    print(f"\n✅ 改进效果:")
    print("现在产品名称不再包含索引号，显示为纯净的产品名称")

if __name__ == "__main__":
    test_index_parsing()
    test_correct_parsing()
