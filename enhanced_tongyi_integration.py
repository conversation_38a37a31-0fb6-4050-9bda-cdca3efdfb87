#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版通义千问集成模块
集成元数据管理功能，提供更准确的数据分析
"""

import os
import pandas as pd
from dotenv import load_dotenv
from pandasai.llm.base import LLM
import requests
import re
from metadata_manager import metadata_manager
from typing import Optional, Dict, Any
import logging

load_dotenv()
logger = logging.getLogger(__name__)

class EnhancedTongyiQianwenLLM(LLM):
    """增强版通义千问LLM，集成元数据功能"""
    
    def __init__(self, model="qwen-plus", enable_metadata=True):
        """
        初始化增强版LLM
        
        Args:
            model: 模型名称
            enable_metadata: 是否启用元数据功能
        """
        self.api_key = os.getenv('DASHSCOPE_API_KEY')
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        self.enable_metadata = enable_metadata
        self.current_table_name = None
        self.current_dataframe = None
        
    def set_current_data(self, table_name: str, df: pd.DataFrame):
        """
        设置当前分析的数据
        
        Args:
            table_name: 表格名称
            df: DataFrame对象
        """
        self.current_table_name = table_name
        self.current_dataframe = df
        
        # 如果启用元数据且表格未注册，自动注册
        if self.enable_metadata:
            if not metadata_manager.get_table_metadata(table_name):
                logger.info(f"自动注册表格元数据: {table_name}")
                metadata_manager.register_table(table_name, df)
    
    def _should_generate_chart(self, instruction):
        """判断用户指令是否需要生成图表"""
        # 明确的图表关键词
        chart_keywords = [
            '图表', '饼图', '柱状图', '折线图', '散点图', '可视化', '显示图',
            '画图', '绘制图', '生成图', '图形', 'chart', '可视', '展示图'
        ]

        # 检查是否包含图表关键词
        instruction_lower = instruction.lower()
        for keyword in chart_keywords:
            if keyword in instruction_lower:
                return True

        # 如果只是分析、计算、统计类查询，不生成图表
        print(f"🎯 检测到纯分析查询，不生成图表: {instruction}")
        return False

    def call(self, instruction, value):
        """
        调用LLM生成代码，集成元数据上下文

        Args:
            instruction: 用户指令
            value: 数据信息

        Returns:
            str: 生成的Python代码
        """
        # 判断是否需要生成图表
        should_chart = self._should_generate_chart(instruction)

        # 构建增强的提示词
        enhanced_prompt = self._build_enhanced_prompt(instruction, value, should_chart)
        
        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        data = {
            "model": self.model, 
            "messages": [{"role": "user", "content": enhanced_prompt}], 
            "temperature": 0.1, 
            "max_tokens": 800  # 增加token限制以支持更复杂的分析
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
            if response.status_code == 200:
                code = response.json()['choices'][0]['message']['content']
                
                # 清理代码
                code = self.clean_code(code)

                # 强制使用Streamlit原生图表
                code = self.enforce_streamlit_native_charts(code, instruction)

                return code
            else:
                logger.error(f"API调用失败: {response.status_code}")
                return "print('API调用失败')"
        except Exception as e:
            logger.error(f"API异常: {e}")
            return f"print('API异常: {e}')"
    
    def _build_enhanced_prompt(self, instruction: str, value: str, should_chart: bool = True) -> str:
        """
        构建增强的提示词，包含元数据信息

        Args:
            instruction: 用户指令
            value: 数据信息
            should_chart: 是否应该生成图表

        Returns:
            str: 增强的提示词
        """
        base_prompt = f"""你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
{value}

用户指令: {instruction}
"""
        
        # 添加元数据上下文
        if self.enable_metadata and self.current_table_name and self.current_dataframe is not None:
            metadata_context = self._get_metadata_context()
            if metadata_context:
                base_prompt += f"""

数据元数据信息:
{metadata_context}

请根据以上元数据信息更准确地理解列名含义和业务逻辑。
"""
        
        # 添加代码生成要求
        base_prompt += """
要求:
1. 只返回可执行的Python代码
2. 使用变量名df表示DataFrame，df已经存在，直接使用即可
3. 绝对不要包含创建DataFrame的代码，如pd.read_csv()、pd.read_excel()等
4. 绝对不要包含任何文件读取操作，数据已经在df变量中
5. 不要包含任何中文注释或解释
6. 🚨 重要：如果需要生成饼图，必须严格使用Plotly模板，绝对不要使用matplotlib的plt.pie()方法
7. 使用print()输出最终结果，特别注意：
   - 对于"哪个/哪种...最高/最低"类型查询，必须输出：
     a) 所有项目的统计数据：print(grouped_data)
     b) 明确的答案：print(f"答案: 具体项目名称, 具体指标: 具体数值")
   - 对于比较分析，必须输出完整数据和结论
   - 不能只计算结果而不输出，每个分析都必须有print语句
6. 根据元数据信息理解列的业务含义，生成更准确的分析代码
7. 如果涉及计算，请考虑列之间的业务关系
8. 图表生成规则："""

        # 根据是否需要图表添加不同的指令
        if should_chart:
            base_prompt += """
   🚨 用户明确要求了图表，请生成相应的可视化代码！

   图表生成规则（严格按优先级执行）：

   🥧 对于饼图（最高优先级，必须使用）：
     ```python
     import plotly.express as px
     data = df.groupby('分组列')['数值列'].sum().reset_index()
     print("数据分布:")
     print(data)
     fig = px.pie(data, values='数值列', names='分组列', title='饼图标题')
     fig.update_traces(textposition='inside', textinfo='percent+label')
     fig.update_layout(showlegend=True, legend=dict(orientation="v", yanchor="middle", y=0.5, xanchor="left", x=1.01))
     st.plotly_chart(fig, use_container_width=True)
     ```

   🚨 **绝对禁止使用matplotlib！** 🚨

   📊 对于其他图表类型（柱状图、折线图等），强制使用Streamlit原生方法：

   柱状图示例：
   ```python
   data = df.groupby('分组列')['数值列'].sum().sort_values(ascending=False)
   print("数据分布:")
   print(data)
   st.subheader("📊 数据分析结果")
   st.bar_chart(data)
   ```

   折线图示例：
   ```python
   data = df.groupby('时间列')['数值列'].sum()
   print("趋势数据:")
   print(data)
   st.subheader("📈 趋势分析")
   st.line_chart(data)
   ```

   - 禁止导入matplotlib：import matplotlib.pyplot as plt
   - 禁止使用任何plt.方法
   - 只使用st.bar_chart(), st.line_chart(), st.scatter_chart()等Streamlit原生方法

代码:"""
        else:
            base_prompt += """
   🚨 重要：用户只要求数据分析，请不要生成任何图表代码！
   - 只进行数据处理和计算
   - 只输出print()语句显示结果
   - 不要导入plotly、matplotlib等可视化库
   - 不要生成任何图表相关代码
   - 专注于数据统计、计算和分析

代码:"""

        return base_prompt
    
    def _get_metadata_context(self) -> Optional[str]:
        """
        获取当前表格的元数据上下文
        
        Returns:
            str: 元数据上下文字符串
        """
        try:
            if not self.current_table_name or self.current_dataframe is None:
                return None
            
            # 生成元数据上下文
            context = metadata_manager.generate_llm_context(
                self.current_table_name, 
                self.current_dataframe
            )
            
            return context
            
        except Exception as e:
            logger.error(f"获取元数据上下文失败: {e}")
            return None
    
    def clean_code(self, code):
        """清理生成的代码"""
        # 移除markdown标记
        code = re.sub(r'```python\n?', '', code)
        code = re.sub(r'```\n?', '', code)
        
        # 按行处理
        lines = code.split('\n')
        clean_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 跳过中文解释行
            if self.contains_chinese_explanation(line):
                continue
                
            # 跳过注释行（但保留代码中的英文注释）
            if line.startswith('#') and any(ord(c) > 127 for c in line):
                continue
                
            clean_lines.append(line)
        
        return '\n'.join(clean_lines).strip()
    
    def contains_chinese_explanation(self, line):
        """检查是否包含中文解释"""
        # 检查是否包含中文标点符号
        chinese_punctuation = '。，：；！？""''（）【】'
        if any(p in line for p in chinese_punctuation):
            return True
            
        # 检查是否以中文开头且不是代码
        if line and ord(line[0]) > 127 and not any(op in line for op in ['=', '(', '[', 'df', 'print']):
            return True
            
        return False
    
    def enforce_streamlit_native_charts(self, code, instruction):
        """强制使用Streamlit原生图表方法（增强版 - 解决图表消失问题）"""
        instruction_lower = instruction.lower()

        # 检查是否需要图表
        chart_keywords = ['柱状图', '条形图', '折线图', '散点图', '面积图', 'bar', 'line', 'scatter', 'area', '图表', '可视化']
        needs_chart = any(keyword in instruction_lower for keyword in chart_keywords)

        print(f"🔍 图表检测: 需要图表={needs_chart}, 指令={instruction}")

        # 如果需要图表，强制应用深度修复
        if needs_chart:
            print("🔧 强制应用深度图表修复...")
            return self.enforce_streamlit_native_charts_deep_fix(code, instruction)

        # 检查代码中是否包含图表相关内容
        chart_indicators = ['st.bar_chart', 'st.line_chart', 'st.plotly_chart', 'px.bar', 'px.line', 'matplotlib']
        has_chart_code = any(indicator in code for indicator in chart_indicators)

        if has_chart_code:
            print("🔧 检测到图表代码，应用深度修复...")
            return self.enforce_streamlit_native_charts_deep_fix(code, instruction)

        return code

    def _generate_streamlit_bar_chart(self, original_code):
        """生成Streamlit原生柱状图代码（深度修复版 - 解决图表消失问题）"""
        return """# 使用安全的Streamlit图表渲染（深度修复版 - 解决图表消失问题）
import numpy as np
import pandas as pd
import re

# 首先进行数据清理（解决Vega-Lite渲染问题）
# 1. 处理数值列中的无穷大值和NaN
numeric_cols = df.select_dtypes(include=[np.number]).columns
for col in numeric_cols:
    # 替换无穷大值
    df[col] = df[col].replace([np.inf, -np.inf], np.nan)
    # 填充NaN值
    df[col] = df[col].fillna(0)
    # 确保数据类型正确
    df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

    # 处理过大或过小的值（避免Vega-Lite渲染问题）
    if df[col].max() > 1e15:
        df[col] = df[col] / 1e6  # 转换为百万单位
    if abs(df[col].min()) > 1e15:
        df[col] = df[col].clip(lower=-1e12)

# 2. 清理列名中的特殊字符（避免字段冲突）
df.columns = [re.sub(r'[^\\w\\u4e00-\\u9fff]', '_', str(col)) for col in df.columns]

# 3. 处理重复索引
if df.index.duplicated().any():
    df = df.reset_index(drop=True)

# 智能检测产品名称列和销售额列
product_col = None
sales_col = None

# 查找产品名称列（优先级：产品名称 > 产品 > 名称）
for col in df.columns:
    if '产品名称' in str(col) or '产品' in str(col):
        product_col = col
        break

# 查找销售额列（优先级：销售额 > 销售 > 金额）
for col in df.columns:
    if '销售额' in str(col) or '销售' in str(col) or '金额' in str(col):
        sales_col = col
        break

# 如果没找到，使用第一列作为产品列，第二列作为数值列
if product_col is None:
    product_col = df.columns[0]
if sales_col is None:
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    sales_col = numeric_cols[0] if len(numeric_cols) > 0 else df.columns[1]

print(f"使用列: 产品列={product_col}, 销售额列={sales_col}")

# 数据处理和清理
chart_data = df.groupby(product_col)[sales_col].sum()

# 深度数据清理（解决Vega-Lite渲染问题）
chart_data = chart_data.replace([np.inf, -np.inf], np.nan)
chart_data = chart_data.fillna(0)
chart_data = pd.to_numeric(chart_data, errors='coerce').fillna(0)

# 处理重复索引（避免Vega-Lite字段冲突）
if chart_data.index.duplicated().any():
    chart_data = chart_data.groupby(chart_data.index).sum()

# 清理索引名称（避免特殊字符导致的渲染问题）
if hasattr(chart_data.index, 'name') and chart_data.index.name:
    clean_name = re.sub(r'[^\\w\\u4e00-\\u9fff]', '_', str(chart_data.index.name))
    chart_data.index.name = clean_name

# 处理过大或过小的值（避免Vega-Lite scale binding问题）
if chart_data.max() > 1e15:
    chart_data = chart_data / 1e6  # 转换为百万单位
    value_unit = "（百万）"
else:
    value_unit = ""

# 排序数据
chart_data = chart_data.sort_values(ascending=False)

# 验证数据质量
print("图表数据:")
print(chart_data)
print(f"数据类型: {chart_data.dtype}")
print(f"数据形状: {chart_data.shape}")
print(f"数据范围: {chart_data.min()} - {chart_data.max()}")
print(f"是否包含NaN: {chart_data.isnull().any()}")
print(f"是否包含无穷大: {np.isinf(chart_data).any()}")

# 使用容器和错误处理确保图表持久化显示
with st.container():
    # 安全渲染图表
    if not chart_data.empty and chart_data.sum() != 0:
        st.subheader(f"📊 各产品销售额对比{value_unit}")

        try:
            # 使用Streamlit原生方法，避免Vega-Lite渲染冲突
            st.bar_chart(chart_data, use_container_width=True)

            # 显示数据摘要
            st.write("**数据摘要:**")
            st.write(f"• 总销售额: ¥{chart_data.sum():,.0f}{value_unit}")
            st.write(f"• 平均销售额: ¥{chart_data.mean():,.0f}{value_unit}")
            st.write(f"• 最高销售额: ¥{chart_data.max():,.0f}{value_unit}")

            # 显示详细数据（可展开）
            with st.expander("📋 详细数据"):
                for product_name, sales_amount in chart_data.items():
                    percentage = (sales_amount / chart_data.sum()) * 100
                    st.write(f"• {product_name}: ¥{sales_amount:,.0f}{value_unit} ({percentage:.1f}%)")

        except Exception as render_error:
            st.error(f"图表渲染失败: {render_error}")
            # 备用显示方案
            st.write("**数据表格（备用显示）:**")
            st.dataframe(chart_data.to_frame(f'销售额{value_unit}'))
    else:
        st.warning("📊 数据无法生成图表")
        # 显示原始数据用于调试
        if not chart_data.empty:
            st.write("**原始数据（调试用）:**")
            st.dataframe(chart_data.to_frame('销售额'))
"""

    def _generate_streamlit_line_chart(self, original_code):
        """生成Streamlit原生折线图代码（深度修复版）"""
        return """# 使用安全的Streamlit折线图渲染
import numpy as np
import pandas as pd
import re

# 深度数据清理和处理
if '日期' in df.columns:
    # 处理日期数据
    df_clean = df.copy()
    df_clean['日期'] = pd.to_datetime(df_clean['日期'], errors='coerce')
    df_clean = df_clean.dropna(subset=['日期'])

    chart_data = df_clean.groupby('日期')['销售额'].sum()
    chart_title = "📈 销售额趋势"
else:
    # 备用方案：按产品显示
    chart_data = df.groupby('产品名称')['销售额'].sum()
    chart_title = "📈 产品销售额趋势"

# 深度数据清理（解决Vega-Lite渲染问题）
chart_data = chart_data.replace([np.inf, -np.inf], np.nan)
chart_data = chart_data.fillna(method='ffill').fillna(0)
chart_data = pd.to_numeric(chart_data, errors='coerce').fillna(0)

# 处理重复索引
if chart_data.index.duplicated().any():
    chart_data = chart_data.groupby(chart_data.index).sum()

# 清理索引名称（避免Vega-Lite字段冲突）
if hasattr(chart_data.index, 'name') and chart_data.index.name:
    clean_name = re.sub(r'[^\w\u4e00-\u9fff]', '_', str(chart_data.index.name))
    chart_data.index.name = clean_name

print("趋势数据:")
print(chart_data)
print(f"数据类型: {chart_data.dtype}")
print(f"数据范围: {chart_data.min()} - {chart_data.max()}")

# 安全渲染折线图
if not chart_data.empty and chart_data.sum() != 0:
    st.subheader(chart_title)
    st.line_chart(chart_data, use_container_width=True)

    # 趋势分析
    if len(chart_data) >= 2:
        trend_change = chart_data.iloc[-1] - chart_data.iloc[0]
        if trend_change > 0:
            st.success(f"📈 呈上升趋势，增长 {trend_change:,.2f}")
        elif trend_change < 0:
            st.warning(f"📉 呈下降趋势，下降 {abs(trend_change):,.2f}")
        else:
            st.info("📊 趋势保持稳定")
else:
    st.warning("📊 数据无法生成趋势图")
"""

    def _generate_streamlit_scatter_chart(self, original_code):
        """生成Streamlit原生散点图代码"""
        return """# 使用Streamlit原生散点图
if '价格' in df.columns and '销量' in df.columns:
    scatter_data = df[['价格', '销量']].copy()
    print("价格与销量关系:")
    print(scatter_data.head())

    st.subheader("📊 价格与销量关系")
    st.scatter_chart(scatter_data.set_index('价格')['销量'])
else:
    # 备用方案：销售额与销量
    if '销售额' in df.columns and '销量' in df.columns:
        scatter_data = df[['销售额', '销量']].copy()
        print("销售额与销量关系:")
        print(scatter_data.head())

        st.subheader("📊 销售额与销量关系")
        st.scatter_chart(scatter_data.set_index('销售额')['销量'])
"""

    def fix_syntax_errors(self, code):
        """修复常见的语法错误"""
        lines = code.split('\n')
        fixed_lines = []

        for i, line in enumerate(lines):
            fixed_line = line

            # 修复常见的Plotly语法错误
            if 'fig = px.bar(' in line and not line.strip().endswith(')'):
                # 检查下一行是否是fig.update_layout
                if i + 1 < len(lines) and 'fig.update_layout(' in lines[i + 1]:
                    # 合并这两行并修复语法
                    next_line = lines[i + 1]
                    if line.strip().endswith(','):
                        # 移除末尾的逗号，添加闭合括号
                        fixed_line = line.rstrip().rstrip(',') + ')'
                        # 保持下一行不变
                        fixed_lines.append(fixed_line)
                        continue
                    else:
                        # 添加闭合括号
                        fixed_line = line + ')'

            # 修复其他常见的括号问题
            if ('fig = px.' in line and
                line.count('(') > line.count(')') and
                not line.strip().endswith(',')):
                # 添加缺失的闭合括号
                missing_parens = line.count('(') - line.count(')')
                fixed_line = line + ')' * missing_parens

            fixed_lines.append(fixed_line)

        return '\n'.join(fixed_lines)

    def validate_syntax(self, code):
        """验证代码语法"""
        try:
            compile(code, '<string>', 'exec')
            return True
        except SyntaxError as e:
            print(f"⚠️ 语法错误: {e}")
            return False

    def add_data_cleaning(self, code):
        """添加数据清理机制"""
        # 检查是否包含Streamlit图表方法
        streamlit_chart_methods = ['st.bar_chart', 'st.line_chart', 'st.scatter_chart', 'st.area_chart']

        if any(method in code for method in streamlit_chart_methods):
            print("🧹 添加数据清理机制")

            # 在代码开头添加必要的导入
            if 'import numpy as np' not in code:
                code = 'import numpy as np\n' + code

            lines = code.split('\n')
            cleaned_lines = []

            for line in lines:
                cleaned_lines.append(line)

                # 在数据处理后添加清理代码
                if ('= df.groupby(' in line and '.sum()' in line) or \
                   ('= df.set_index(' in line) or \
                   ('product_sales =' in line and 'groupby' in line):

                    # 添加数据清理代码
                    var_name = line.split('=')[0].strip()
                    cleaning_code = f"""
# 数据清理：处理异常值和类型转换
{var_name} = {var_name}.replace([np.inf, -np.inf], np.nan)
{var_name} = {var_name}.fillna(0)
{var_name} = {var_name}.astype(float)

# 验证数据质量
print(f"数据类型: {{{var_name}.dtype}}")
print(f"数据范围: {{{var_name}.min()}} - {{{var_name}.max()}}")
print(f"是否包含NaN: {{{var_name}.isnull().any()}}")"""

                    cleaned_lines.extend(cleaning_code.strip().split('\n'))

            return '\n'.join(cleaned_lines)

        return code

    def enforce_streamlit_native_charts_deep_fix(self, code, instruction):
        """强制转换为深度修复的Streamlit原生图表"""
        print("🔧 应用深度图表修复...")

        # 检测图表类型
        chart_keywords = {
            'bar': ['柱状图', '条形图', 'bar', '销售额', '对比'],
            'line': ['折线图', '趋势', 'line', '时间', '日期'],
            'pie': ['饼图', '占比', 'pie', '比例'],
            'scatter': ['散点图', 'scatter', '相关性']
        }

        detected_chart_type = None
        instruction_lower = instruction.lower()

        for chart_type, keywords in chart_keywords.items():
            if any(keyword in instruction_lower or keyword in instruction for keyword in keywords):
                detected_chart_type = chart_type
                break

        # 如果检测到图表需求，强制使用深度修复版本
        if detected_chart_type:
            print(f"🎯 检测到图表类型: {detected_chart_type}")

            if detected_chart_type == 'bar':
                return self._generate_streamlit_bar_chart(code)
            elif detected_chart_type == 'line':
                return self._generate_streamlit_line_chart(code)
            elif detected_chart_type == 'pie':
                return self._generate_streamlit_pie_chart_alternative(code)
            elif detected_chart_type == 'scatter':
                return self._generate_streamlit_scatter_chart(code)

        # 如果代码中包含图表方法，也进行深度修复
        if any(method in code for method in ['st.bar_chart', 'st.line_chart', 'st.plotly_chart']):
            print("🔧 检测到图表代码，应用深度修复")

            if 'st.bar_chart' in code:
                return self._generate_streamlit_bar_chart(code)
            elif 'st.line_chart' in code:
                return self._generate_streamlit_line_chart(code)
            else:
                # 默认使用柱状图深度修复
                return self._generate_streamlit_bar_chart(code)

        return code

    def attempt_syntax_repair(self, code):
        """尝试修复语法错误"""
        lines = code.split('\n')
        repaired_lines = []

        for line in lines:
            # 跳过明显有问题的行
            if ('fig = px.' in line and
                line.count('(') != line.count(')') and
                not line.strip().endswith(',')):
                # 尝试修复括号匹配
                open_parens = line.count('(')
                close_parens = line.count(')')
                if open_parens > close_parens:
                    line = line + ')' * (open_parens - close_parens)

            repaired_lines.append(line)

        repaired_code = '\n'.join(repaired_lines)

        # 再次验证
        if self.validate_syntax(repaired_code):
            print("✅ 语法修复成功")
            return repaired_code
        else:
            print("❌ 语法修复失败，返回原代码")
            return code

    @property
    def type(self):
        return "enhanced_tongyi_qianwen"

def analyze_data_with_metadata(df: pd.DataFrame, query: str, table_name: str = "data_table"):
    """
    使用增强版LLM分析数据，包含元数据支持
    
    Args:
        df: DataFrame对象
        query: 查询语句
        table_name: 表格名称
        
    Returns:
        dict: 分析结果
    """
    import io
    import sys
    from contextlib import redirect_stdout, redirect_stderr

    # 创建结果字典
    result = {
        'query': query,
        'table_name': table_name,
        'data_shape': df.shape,
        'code': '',
        'output': '',
        'error': None,
        'success': False,
        'chart_path': None,
        'chart_figure': None,
        'has_chart': False,
        'metadata_used': False
    }

    print(f"🔍 查询: {query}")
    print(f"📊 数据形状: {df.shape}")
    print(f"📋 表格名称: {table_name}")

    try:
        # 创建增强版LLM
        llm = EnhancedTongyiQianwenLLM()
        
        # 设置当前数据
        llm.set_current_data(table_name, df)
        result['metadata_used'] = llm.enable_metadata
        
        # 生成代码
        code = llm.call(query, df.to_string())
        result['code'] = code

        print(f"📝 生成的代码:")
        print(code)
        print(f"🚀 执行结果:")

        # 执行代码的逻辑与原版本相同
        # [这里可以复用原来的执行逻辑]
        
        # 简化版执行逻辑
        output_buffer = io.StringIO()
        error_buffer = io.StringIO()
        
        # 创建DataFrame副本，避免修改原始数据
        df_copy = df.copy()

        # 导入Streamlit和Plotly（如果可用）
        try:
            import streamlit as st
        except ImportError:
            # 创建一个模拟的streamlit对象，避免执行错误
            class MockStreamlit:
                def __getattr__(self, name):
                    def mock_method(*args, **kwargs):
                        print(f"[模拟] st.{name}() 被调用，但Streamlit不可用")
                        return None
                    return mock_method
            st = MockStreamlit()

        try:
            import plotly.express as px
            import plotly.graph_objects as go
        except ImportError:
            px = None
            go = None

        exec_globals = {
            'df': df_copy,  # 使用副本而不是原始DataFrame
            'pd': pd,
            'st': st,  # Streamlit支持
            'px': px,  # Plotly Express支持
            'go': go,  # Plotly Graph Objects支持
            'print': lambda *args, **kwargs: print(*args, **kwargs, file=output_buffer)
        }
        
        # 清理和修复代码
        cleaned_code = code

        # 1. 移除可能导致冲突的导入语句
        import_lines_to_remove = [
            'import streamlit as st',
            'import pandas as pd',
            'import numpy as np',
            'import matplotlib.pyplot as plt',
            'import plotly.express as px',
            'import plotly.graph_objects as go'
        ]

        for import_line in import_lines_to_remove:
            if import_line in cleaned_code:
                cleaned_code = cleaned_code.replace(import_line, f'# {import_line} # 已在执行环境中提供')

        # 2. 修复常见的语法错误
        cleaned_code = self.fix_syntax_errors(cleaned_code)

        # 3. 添加数据清理机制
        cleaned_code = self.add_data_cleaning(cleaned_code)

        # 4. 强制转换为Streamlit原生图表（深度修复版）
        cleaned_code = self.enforce_streamlit_native_charts_deep_fix(cleaned_code, instruction)

        # 5. 验证代码语法
        syntax_valid = self.validate_syntax(cleaned_code)
        if not syntax_valid:
            print("⚠️ 代码语法验证失败，尝试修复")
            cleaned_code = self.attempt_syntax_repair(cleaned_code)

        print(f"🔧 清理后的代码:")
        print(cleaned_code)

        with redirect_stdout(output_buffer), redirect_stderr(error_buffer):
            exec(cleaned_code, exec_globals)
        
        output = output_buffer.getvalue()
        error_output = error_buffer.getvalue()
        
        if error_output:
            result['error'] = error_output
            print(f"❌ 执行警告: {error_output}")
        
        if output:
            result['output'] = output
            print(output)
        
        # 检查图表类型和显示方式
        uses_plotly_native = 'st.plotly_chart' in code
        uses_streamlit_native = any(method in code for method in ['st.bar_chart', 'st.line_chart', 'st.scatter_chart', 'st.area_chart'])

        if uses_streamlit_native:
            # Streamlit原生图表，直接显示
            result['uses_streamlit_native'] = True
            result['has_chart'] = False  # 不需要额外显示
            print(f"📊 Streamlit原生图表已显示")
        elif uses_plotly_native:
            # Plotly原生图表，直接通过st.plotly_chart显示
            result['uses_plotly_native'] = True
            result['has_chart'] = False  # 不需要额外显示
            print(f"📊 Plotly原生图表已显示")
        else:
            result['has_chart'] = False
            result['uses_plotly_native'] = False
            result['uses_streamlit_native'] = False

        result['success'] = True
        print("✅ 执行成功")

        # 显示元数据使用情况
        if result['metadata_used']:
            print("🎯 已使用元数据增强分析准确性")

    except Exception as e:
        result['error'] = str(e)
        result['success'] = False
        print(f"❌ 执行失败: {e}")

    print("-" * 50)
    return result
