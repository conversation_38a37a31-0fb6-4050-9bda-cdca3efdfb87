#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表生成问题的完整解决方案
"""

import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_fixed_llm_class():
    """创建修复后的LLM类"""
    
    class FixedTongyiQianwenLLM:
        def __init__(self, model="qwen-plus"):
            self.api_key = os.getenv('DASHSCOPE_API_KEY')
            self.model = model
            self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
            
        def call(self, instruction, value):
            """生成修复后的代码"""
            # 针对多维度分析的特殊处理
            if "各地区" in instruction and "产品" in instruction:
                return self.generate_multi_dimension_code()
            else:
                # 使用原有逻辑
                return self.call_api(instruction, value)
        
        def generate_multi_dimension_code(self):
            """生成多维度分析的标准代码"""
            return """import pandas as pd
import plotly.express as px

# 按地区和产品分组
grouped_data = df.groupby(['地区', '产品名称'])['销售额'].sum().reset_index()
print("分组数据:")
print(grouped_data)

# 为每个地区生成饼图
regions = grouped_data['地区'].unique()
for region in regions:
    region_data = grouped_data[grouped_data['地区'] == region]
    if not region_data.empty:
        fig = px.pie(region_data, values='销售额', names='产品名称', 
                    title=region + '地区各产品销售总额占比')
        fig.update_traces(textposition='inside', textinfo='percent+label')
        st.plotly_chart(fig, use_container_width=True)"""
        
        def call_api(self, instruction, value):
            """调用API生成代码"""
            import requests
            
            prompt = f"""你是Python数据分析专家。根据数据和指令生成Python代码。

数据信息:
{value}

用户指令: {instruction}

要求:
1. 只返回可执行的Python代码，确保所有代码块都有正确的缩进
2. 使用变量名df表示DataFrame，df已经存在，直接使用即可
3. 绝对不要包含创建DataFrame的代码
4. 使用print()输出最终结果
5. 对于循环和条件语句，确保正确缩进（4个空格）

代码:"""
            
            headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
            data = {"model": self.model, "messages": [{"role": "user", "content": prompt}], "temperature": 0.1, "max_tokens": 800}
            
            try:
                response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
                if response.status_code == 200:
                    code = response.json()['choices'][0]['message']['content']
                    return self.clean_code(code)
                else:
                    return "print('API调用失败')"
            except Exception as e:
                return f"print('API异常: {e}')"
        
        def clean_code(self, code):
            """清理代码"""
            import re
            
            # 移除markdown标记
            code = re.sub(r'```python\n?', '', code)
            code = re.sub(r'```\n?', '', code)
            
            # 简单清理
            lines = code.split('\n')
            clean_lines = []
            
            for line in lines:
                line = line.strip()
                if line and not self.contains_chinese_explanation(line):
                    clean_lines.append(line)
            
            return '\n'.join(clean_lines)
        
        def contains_chinese_explanation(self, line):
            """检查是否包含中文解释"""
            chinese_punctuation = '。，：；！？""''（）【】'
            if any(p in line for p in chinese_punctuation):
                return True
            return False
    
    return FixedTongyiQianwenLLM

def test_fixed_solution():
    """测试修复后的解决方案"""
    print("🎯 测试修复后的解决方案")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"✅ 数据加载成功: {df.shape}")
    
    # 创建修复后的LLM
    FixedLLM = create_fixed_llm_class()
    llm = FixedLLM()
    
    # 测试问题查询
    query = "请按照各地区各中各产品销售总额情况分析"
    print(f"\n🔍 查询: {query}")
    
    # 生成代码
    code = llm.call(query, df.to_string())
    print(f"\n📝 生成的代码:")
    print(code)
    
    # 验证语法
    try:
        compile(code, '<string>', 'exec')
        print("\n✅ 代码语法检查通过")
        
        # 尝试执行
        print("\n🚀 执行结果:")
        
        # 模拟Streamlit环境
        class MockStreamlit:
            @staticmethod
            def plotly_chart(fig, use_container_width=True):
                print(f"📊 Plotly图表已生成: {fig.layout.title.text}")
        
        # 执行代码
        exec_globals = {
            'df': df,
            'pd': pd,
            'px': None,  # 先设为None测试
            'st': MockStreamlit(),
            'print': print
        }
        
        try:
            import plotly.express as px
            exec_globals['px'] = px
            exec(code, exec_globals)
            print("✅ 代码执行成功")
        except ImportError:
            print("⚠️ Plotly未安装，但代码语法正确")
        except Exception as e:
            print(f"❌ 执行错误: {e}")
            
    except SyntaxError as e:
        print(f"\n❌ 代码语法错误: {e}")
        print(f"错误位置: 第{e.lineno}行")

def create_streamlit_integration():
    """创建Streamlit集成方案"""
    print("\n🔧 Streamlit集成方案")
    print("=" * 50)
    
    integration_code = '''
# 在streamlit_app.py中添加以下修复代码

def fix_chart_generation_error(result):
    """修复图表生成错误"""
    if result.get('error') and 'expected an indented block' in result['error']:
        # 检测到缩进错误，使用备用方案
        st.warning("检测到代码生成问题，使用备用图表方案")
        
        # 备用方案：直接生成图表
        try:
            df = st.session_state.get('current_df')
            if df is not None:
                # 多维度分析备用方案
                grouped_data = df.groupby(['地区', '产品名称'])['销售额'].sum().reset_index()
                
                st.subheader("📊 各地区产品销售分析")
                st.dataframe(grouped_data)
                
                # 为每个地区生成图表
                regions = grouped_data['地区'].unique()
                for region in regions:
                    region_data = grouped_data[grouped_data['地区'] == region]
                    if not region_data.empty:
                        st.subheader(f"📈 {region}地区产品销售分布")
                        
                        # 使用Streamlit原生图表
                        chart_data = region_data.set_index('产品名称')['销售额']
                        st.bar_chart(chart_data)
                        
                return True
        except Exception as e:
            st.error(f"备用方案也失败了: {e}")
            return False
    
    return False

# 在主分析函数中使用
def enhanced_analyze_with_fallback(df, query):
    """增强的分析函数，包含错误恢复"""
    try:
        # 尝试正常分析
        result = analyze_data(df, query, "sales_data", use_metadata=True)
        
        # 检查是否有错误
        if fix_chart_generation_error(result):
            return {"success": True, "fallback_used": True}
        
        # 正常显示结果
        if result.get('output'):
            st.text(result['output'])
        
        return {"success": True, "fallback_used": False}
        
    except Exception as e:
        st.error(f"分析失败: {e}")
        return {"success": False, "error": str(e)}
'''
    
    print(integration_code)

if __name__ == "__main__":
    test_fixed_solution()
    create_streamlit_integration()
