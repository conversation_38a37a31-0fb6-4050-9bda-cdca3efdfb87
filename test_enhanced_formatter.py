#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强的结果格式化器
验证各种输出类型的显示效果
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data
from result_formatter import EnhancedResultFormatter
import streamlit as st

def create_test_data():
    """创建测试数据"""
    return pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro'],
        '类别': ['手机', '平板', '笔记本', '配件'],
        '价格': [6999, 4599, 14999, 1899],
        '销量': [1200, 800, 400, 1500],
        '评分': [4.8, 4.6, 4.9, 4.7]
    })

def test_different_output_types():
    """测试不同输出类型的格式化"""
    
    test_data = create_test_data()
    
    # 测试查询列表
    test_queries = [
        ("DataFrame信息", "显示数据的基本信息"),
        ("统计摘要", "计算所有数值列的统计摘要"),
        ("单一数值", "计算总销售额"),
        ("表格数据", "显示前3行数据"),
        ("序列数据", "计算各类别的平均价格"),
        ("相关性矩阵", "显示数值列之间的相关性矩阵")
    ]
    
    print("🧪 测试增强格式化器")
    print("=" * 50)
    
    for test_name, query in test_queries:
        print(f"\n📊 测试: {test_name}")
        print(f"🔍 查询: {query}")
        print("-" * 30)
        
        try:
            # 执行分析
            result = analyze_data(test_data, query)
            
            if result and result.get('success'):
                output = result.get('output', '')
                print(f"✅ 分析成功")
                print(f"📝 输出长度: {len(output)}")
                print(f"📊 有图表: {result.get('has_chart', False)}")
                
                # 检测输出类型
                output_type = EnhancedResultFormatter._detect_output_type(output)
                print(f"🎯 检测类型: {output_type}")
                
                # 显示输出预览
                preview = output[:100] + "..." if len(output) > 100 else output
                print(f"👀 输出预览: {preview}")
                
            else:
                print("❌ 分析失败")
                if result and result.get('error'):
                    print(f"   错误: {result['error']}")
                    
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        print("-" * 30)

def simulate_streamlit_display():
    """模拟Streamlit显示效果"""
    print("\n🎨 模拟Streamlit显示效果")
    print("=" * 50)
    
    # 创建模拟结果
    test_results = [
        {
            'success': True,
            'output': 'iPhone 15    6999\niPad Air     4599\nMacBook Pro  14999\nAirPods Pro  1899\nName: 价格, dtype: int64',
            'query': '显示各产品价格'
        },
        {
            'success': True,
            'output': '29674800',
            'query': '计算总销售额'
        },
        {
            'success': True,
            'output': '           价格        销量        评分\ncount   4.000000   4.000000   4.000000\nmean    7124.000000  975.000000   4.750000\nstd     5654.321234  456.789012   0.129099',
            'query': '计算统计摘要'
        }
    ]
    
    for i, result in enumerate(test_results, 1):
        print(f"\n📊 测试结果 {i}")
        print(f"🔍 查询: {result['query']}")
        
        output_type = EnhancedResultFormatter._detect_output_type(result['output'])
        print(f"🎯 检测类型: {output_type}")
        
        print("📋 原始输出:")
        print(result['output'])
        print()

def main():
    """主测试函数"""
    print("🔍 增强结果格式化器测试")
    print("=" * 60)
    
    # 测试不同输出类型
    test_different_output_types()
    
    # 模拟Streamlit显示
    simulate_streamlit_display()
    
    print("\n🎉 测试完成！")
    print("💡 建议在Streamlit应用中实际测试各种查询类型")

if __name__ == "__main__":
    main()
