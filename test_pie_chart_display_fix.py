#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试饼图显示修复
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data
from result_formatter import EnhancedResultFormatter

def test_pie_chart_display():
    """测试饼图显示修复"""
    print("🧪 测试饼图显示修复")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据加载成功: {df.shape}")
    
    # 测试饼图查询
    query = "请为我分析2024年各产品销售额，并用饼图展示"
    print(f"🔍 查询: {query}")
    
    try:
        result = analyze_data(df, query, table_name="sales_data")
        
        if result and result.get('success'):
            print("✅ 查询成功")
            
            code = result.get('code', '')
            output = result.get('output', '')
            has_chart = result.get('has_chart', False)
            chart_figure = result.get('chart_figure')
            chart_path = result.get('chart_path')
            
            print(f"\n📝 生成的代码:")
            print(code)
            
            print(f"\n📊 执行输出:")
            print(output if output.strip() else "(无输出)")
            
            print(f"\n📈 图表信息:")
            print(f"  has_chart: {has_chart}")
            print(f"  chart_figure: {chart_figure is not None}")
            print(f"  chart_path: {chart_path}")
            
            # 检查代码类型
            if 'plt.pie(' in code:
                print("✅ 代码确实是饼图代码")
            elif 'plt.bar(' in code:
                print("❌ 代码是柱状图代码")
            else:
                print("❓ 代码类型不明确")
            
            # 检测输出类型
            output_type = EnhancedResultFormatter._detect_output_type(output)
            print(f"\n🔍 输出类型检测: {output_type}")
            
            # 模拟Streamlit显示逻辑
            print(f"\n🎨 模拟Streamlit显示:")
            print("1. 首先显示数据分析结果")
            if output_type == 'series_data':
                print("   → 检测为序列数据，会显示表格")
                print(f"   → has_chart={has_chart}，{'不会' if has_chart else '会'}显示自动柱状图")
            
            print("2. 然后显示用户生成的图表")
            if has_chart:
                if chart_figure:
                    print("   → 会显示matplotlib图表对象（饼图）")
                elif chart_path:
                    print(f"   → 会显示图表文件: {chart_path}")
                else:
                    print("   → 会显示charts目录中的最新图表")
            else:
                print("   → 没有图表显示")
            
            # 总结
            print(f"\n🎯 预期结果:")
            if has_chart and 'plt.pie(' in code:
                print("✅ 应该显示：数据表格 + 饼图（不是柱状图）")
            else:
                print("❌ 可能有问题")
                
        else:
            print("❌ 查询失败")
            if result:
                print(f"错误: {result.get('error', '未知错误')}")
                
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        import traceback
        traceback.print_exc()

def test_different_chart_types():
    """测试不同图表类型"""
    print("\n🎨 测试不同图表类型")
    print("=" * 50)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    queries = [
        ("饼图", "生成销售金额分布的饼图"),
        ("柱状图", "生成销售金额的柱状图"),
        ("条形图", "用条形图显示产品销售额")
    ]
    
    for chart_type, query in queries:
        print(f"\n📊 测试{chart_type}: {query}")
        try:
            result = analyze_data(df, query, table_name="sales_data")
            
            if result and result.get('success'):
                code = result.get('code', '')
                has_chart = result.get('has_chart', False)
                
                # 检查代码类型
                if 'plt.pie(' in code:
                    actual_type = "饼图"
                elif 'plt.bar(' in code:
                    actual_type = "柱状图"
                elif '.plot(kind=' in code:
                    actual_type = "其他图表"
                else:
                    actual_type = "未知"
                
                print(f"  期望: {chart_type}")
                print(f"  实际: {actual_type}")
                print(f"  图表生成: {'✅' if has_chart else '❌'}")
                
                if chart_type == actual_type:
                    print("  ✅ 图表类型匹配")
                else:
                    print("  ❌ 图表类型不匹配")
                    
            else:
                print("  ❌ 查询失败")
                
        except Exception as e:
            print(f"  ❌ 查询异常: {e}")

if __name__ == "__main__":
    test_pie_chart_display()
    test_different_chart_types()
