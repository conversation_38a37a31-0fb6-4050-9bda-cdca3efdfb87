#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图表持久化修复
专门测试解决图表消失问题的修复方案
"""

import pandas as pd
import numpy as np
from perfect_tongyi_integration import analyze_data

def create_test_data_with_issues():
    """创建包含可能导致Vega-Lite问题的测试数据"""
    print("🧪 创建测试数据（包含潜在问题）")
    print("=" * 50)
    
    # 创建包含各种问题的数据
    data = {
        '产品名称@#$': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro'],  # 特殊字符
        '销售额_start': [6999000, 4599000, 14999000, 1899000],  # 冲突字段名
        '销售额_end': [7999000, 5599000, 15999000, 2899000],    # 冲突字段名
        '销售额': [np.inf, -np.inf, 25000000, np.nan],          # 异常值
        '销量': [1200, 800, 400, 1500],
        '价格': [6999, 4599, 14999, 1899]
    }
    
    df = pd.DataFrame(data)
    
    print("原始数据（包含问题）:")
    print(df)
    print(f"数据类型: {df.dtypes}")
    print(f"异常值检查:")
    print(f"- 无穷大值: {np.isinf(df.select_dtypes(include=[np.number])).sum().sum()}")
    print(f"- NaN值: {df.isnull().sum().sum()}")
    print()
    
    return df

def test_chart_persistence_fix():
    """测试图表持久化修复"""
    print("🔧 测试图表持久化修复")
    print("=" * 50)
    
    # 创建测试数据
    df = create_test_data_with_issues()
    
    # 测试查询列表
    test_queries = [
        "分析各产品销售额，生成柱状图",
        "显示销售额趋势，用折线图展示",
        "创建产品销售额对比图表",
        "用图表展示各产品的销售情况"
    ]
    
    results = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 测试 {i}: {query}")
        print("-" * 30)
        
        try:
            result = analyze_data(df, query, f"chart_test_{i}")
            
            # 分析结果
            analysis = {
                'query': query,
                'success': result.get('success', False),
                'has_chart': result.get('has_chart', False),
                'uses_streamlit_native': result.get('uses_streamlit_native', False),
                'uses_plotly_native': result.get('uses_plotly_native', False),
                'error': result.get('error'),
                'code_contains_container': 'with st.container():' in result.get('code', ''),
                'code_contains_data_cleaning': '数据清理' in result.get('code', ''),
                'code_contains_error_handling': 'try:' in result.get('code', ''),
                'code_length': len(result.get('code', ''))
            }
            
            results.append(analysis)
            
            # 显示分析结果
            print(f"✅ 执行成功: {analysis['success']}")
            print(f"📊 包含图表: {analysis['has_chart']}")
            print(f"🎯 使用Streamlit原生: {analysis['uses_streamlit_native']}")
            print(f"📈 使用Plotly原生: {analysis['uses_plotly_native']}")
            print(f"🔒 包含容器: {analysis['code_contains_container']}")
            print(f"🧹 包含数据清理: {analysis['code_contains_data_cleaning']}")
            print(f"🛡️ 包含错误处理: {analysis['code_contains_error_handling']}")
            
            if analysis['error']:
                print(f"⚠️ 错误信息: {analysis['error']}")
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append({
                'query': query,
                'success': False,
                'error': str(e)
            })
    
    return results

def test_vega_lite_error_fix():
    """测试Vega-Lite错误修复"""
    print("\n🔍 测试Vega-Lite错误修复")
    print("=" * 50)
    
    # 创建专门导致Vega-Lite问题的数据
    problematic_data = {
        '产品名称': ['产品A', '产品B', '产品C', '产品D'],
        '销售额_start': [np.inf, 1000000, 2000000, 3000000],    # 无穷大值
        '销售额_end': [1500000, -np.inf, 2500000, 3500000],     # 负无穷大值
        '销售额': [1200000, 1800000, np.nan, 2400000]           # NaN值
    }
    
    df = pd.DataFrame(problematic_data)
    
    print("问题数据:")
    print(df)
    print()
    
    # 测试修复效果
    query = "分析销售额数据，生成图表可视化"
    
    print(f"📋 测试查询: {query}")
    print("-" * 30)
    
    try:
        result = analyze_data(df, query, "vega_lite_test")
        
        if result.get('success'):
            print("✅ Vega-Lite错误修复成功！")
            
            # 检查生成的代码
            code = result.get('code', '')
            
            # 检查修复特征
            fixes_applied = {
                '无穷大值处理': 'replace([np.inf, -np.inf], np.nan)' in code,
                'NaN值填充': 'fillna(0)' in code,
                '数据类型转换': 'pd.to_numeric' in code,
                '重复索引处理': 'index.duplicated()' in code,
                '特殊字符清理': 're.sub' in code,
                '容器包装': 'with st.container():' in code,
                '错误处理': 'try:' in code and 'except' in code
            }
            
            print("\n🔧 应用的修复:")
            for fix_name, applied in fixes_applied.items():
                status = "✅" if applied else "❌"
                print(f"{status} {fix_name}")
                
            # 检查是否解决了控制台警告
            if all(fixes_applied.values()):
                print("\n🎉 所有修复已应用，应该解决以下问题:")
                print("- ✅ WARN Scale bindings (通过数据清理)")
                print("- ✅ WARN Infinite extent (通过无穷大值处理)")
                print("- ✅ 图表消失问题 (通过容器和错误处理)")
            else:
                print("\n⚠️ 部分修复未应用，可能仍有问题")
                
        else:
            print(f"❌ 修复失败: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def generate_fix_summary(results):
    """生成修复效果总结"""
    print("\n📊 修复效果总结")
    print("=" * 50)
    
    if not results:
        print("❌ 没有测试结果")
        return
    
    successful_tests = [r for r in results if r.get('success', False)]
    chart_tests = [r for r in results if r.get('has_chart', False)]
    container_tests = [r for r in results if r.get('code_contains_container', False)]
    cleaning_tests = [r for r in results if r.get('code_contains_data_cleaning', False)]
    
    total_tests = len(results)
    success_rate = len(successful_tests) / total_tests * 100
    chart_rate = len(chart_tests) / total_tests * 100
    container_rate = len(container_tests) / total_tests * 100
    cleaning_rate = len(cleaning_tests) / total_tests * 100
    
    print(f"📈 总体成功率: {len(successful_tests)}/{total_tests} ({success_rate:.1f}%)")
    print(f"📊 图表生成率: {len(chart_tests)}/{total_tests} ({chart_rate:.1f}%)")
    print(f"🔒 容器使用率: {len(container_tests)}/{total_tests} ({container_rate:.1f}%)")
    print(f"🧹 数据清理率: {len(cleaning_tests)}/{total_tests} ({cleaning_rate:.1f}%)")
    
    if success_rate >= 80:
        print("\n🎉 修复效果良好！")
    elif success_rate >= 60:
        print("\n⚠️ 修复效果一般，需要进一步优化")
    else:
        print("\n❌ 修复效果不佳，需要重新设计")
    
    print(f"\n💡 建议:")
    if container_rate < 100:
        print("- 确保所有图表代码都使用容器包装")
    if cleaning_rate < 100:
        print("- 强化数据清理机制")
    if chart_rate < success_rate:
        print("- 优化图表生成逻辑")

def main():
    """主测试函数"""
    print("🧪 图表持久化修复测试")
    print("=" * 60)
    
    # 执行测试
    results = test_chart_persistence_fix()
    
    # 测试Vega-Lite错误修复
    test_vega_lite_error_fix()
    
    # 生成总结
    generate_fix_summary(results)
    
    print(f"\n🎯 修复重点:")
    print("1. ✅ 解决图表消失问题（容器 + 错误处理）")
    print("2. ✅ 修复Vega-Lite渲染错误（数据清理）")
    print("3. ✅ 处理无穷大值和NaN值")
    print("4. ✅ 清理特殊字符和字段冲突")
    print("5. ✅ 添加图表持久化机制")

if __name__ == "__main__":
    main()
