#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据脱敏解决方案
针对数据和公司名称的隐私保护
"""

import pandas as pd
import hashlib
import json
import os
from datetime import datetime, timedelta
import random
import numpy as np

class DataAnonymizer:
    """数据脱敏器"""
    
    def __init__(self, config_file="anonymization_config.json"):
        """
        初始化脱敏器
        
        Args:
            config_file: 脱敏配置文件路径
        """
        self.config_file = config_file
        self.config = self.load_config()
        self.mapping_cache = {}  # 缓存映射关系，保证一致性
        
    def load_config(self):
        """加载脱敏配置"""
        default_config = {
            "anonymization_level": "medium",  # low, medium, high
            "preserve_data_patterns": True,   # 保持数据模式
            "preserve_relationships": True,   # 保持关系
            "company_name_strategy": "generic", # generic, hash, fake
            "personal_data_strategy": "hash",   # hash, fake, remove
            "financial_data_strategy": "scale", # scale, range, remove
            "date_strategy": "shift",          # shift, remove, keep
            "geographic_strategy": "generic"    # generic, remove, keep
        }
        
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                default_config.update(user_config)
        
        return default_config
    
    def save_config(self):
        """保存脱敏配置"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)
    
    def anonymize_dataframe(self, df, preserve_analysis_capability=True):
        """
        对DataFrame进行脱敏处理
        
        Args:
            df: 原始DataFrame
            preserve_analysis_capability: 是否保持分析能力
            
        Returns:
            tuple: (脱敏后的DataFrame, 脱敏报告)
        """
        df_anonymized = df.copy()
        anonymization_report = {
            "original_shape": df.shape,
            "anonymized_columns": [],
            "preserved_columns": [],
            "anonymization_methods": {},
            "data_quality_impact": "minimal"
        }
        
        # 按列类型进行脱敏
        for column in df.columns:
            method_used = self._anonymize_column(df_anonymized, column, preserve_analysis_capability)
            if method_used:
                anonymization_report["anonymized_columns"].append(column)
                anonymization_report["anonymization_methods"][column] = method_used
            else:
                anonymization_report["preserved_columns"].append(column)
        
        return df_anonymized, anonymization_report
    
    def _anonymize_column(self, df, column, preserve_analysis_capability):
        """对单个列进行脱敏"""
        column_lower = column.lower()
        
        # 1. 个人信息脱敏
        if any(keyword in column_lower for keyword in ['姓名', '员工', '销售员', '客户', '联系人']):
            return self._anonymize_personal_names(df, column)
        
        # 2. 公司/产品名称脱敏
        if any(keyword in column_lower for keyword in ['公司', '企业', '产品名称', '品牌']):
            return self._anonymize_company_names(df, column)
        
        # 3. 财务数据脱敏
        if any(keyword in column_lower for keyword in ['销售额', '金额', '价格', '收入', '成本']):
            if preserve_analysis_capability:
                return self._scale_financial_data(df, column)
            else:
                return self._anonymize_financial_data(df, column)
        
        # 4. 地理信息脱敏
        if any(keyword in column_lower for keyword in ['地区', '城市', '地址', '位置']):
            return self._anonymize_geographic_data(df, column)
        
        # 5. 日期脱敏
        if any(keyword in column_lower for keyword in ['日期', '时间', 'date', 'time']):
            return self._anonymize_date_data(df, column)
        
        return None  # 不需要脱敏
    
    def _anonymize_personal_names(self, df, column):
        """脱敏个人姓名"""
        if self.config["personal_data_strategy"] == "hash":
            # 使用哈希值生成一致的代号
            def hash_name(name):
                if name not in self.mapping_cache:
                    hash_val = int(hashlib.md5(str(name).encode()).hexdigest()[:8], 16)
                    self.mapping_cache[name] = f"员工{hash_val % 1000:03d}"
                return self.mapping_cache[name]
            
            df[column] = df[column].apply(hash_name)
            return "hash_mapping"
        
        elif self.config["personal_data_strategy"] == "fake":
            # 使用假名
            fake_names = ["员工A", "员工B", "员工C", "员工D", "员工E"]
            unique_names = df[column].unique()
            name_mapping = {name: fake_names[i % len(fake_names)] for i, name in enumerate(unique_names)}
            df[column] = df[column].map(name_mapping)
            return "fake_names"
        
        elif self.config["personal_data_strategy"] == "remove":
            df[column] = "***"
            return "removed"
    
    def _anonymize_company_names(self, df, column):
        """脱敏公司/产品名称"""
        if self.config["company_name_strategy"] == "generic":
            # 使用通用产品类别
            product_mapping = {
                "笔记本电脑": "电脑类产品A",
                "台式电脑": "电脑类产品B", 
                "平板电脑": "电脑类产品C",
                "手机": "通讯设备A",
                "智能手表": "智能设备A",
                "耳机": "音频设备A",
                "键盘": "外设A",
                "鼠标": "外设B",
                "显示器": "显示设备A"
            }
            
            def map_product(product):
                return product_mapping.get(product, f"产品{hash(product) % 100:02d}")
            
            df[column] = df[column].apply(map_product)
            return "generic_mapping"
        
        elif self.config["company_name_strategy"] == "hash":
            # 使用哈希值
            def hash_product(product):
                if product not in self.mapping_cache:
                    hash_val = int(hashlib.md5(str(product).encode()).hexdigest()[:8], 16)
                    self.mapping_cache[product] = f"产品{hash_val % 1000:03d}"
                return self.mapping_cache[product]
            
            df[column] = df[column].apply(hash_product)
            return "hash_mapping"
    
    def _scale_financial_data(self, df, column):
        """缩放财务数据（保持分析能力）"""
        if self.config["financial_data_strategy"] == "scale":
            # 按比例缩放，保持相对关系
            scale_factor = random.uniform(0.1, 0.5)  # 缩放到10%-50%
            df[column] = (df[column] * scale_factor).round().astype(int)
            return f"scaled_by_{scale_factor:.2f}"
        
        elif self.config["financial_data_strategy"] == "range":
            # 转换为范围
            def to_range(value):
                if value < 1000:
                    return "0-1K"
                elif value < 5000:
                    return "1K-5K"
                elif value < 10000:
                    return "5K-10K"
                else:
                    return "10K+"
            
            df[column] = df[column].apply(to_range)
            return "range_mapping"
    
    def _anonymize_financial_data(self, df, column):
        """完全脱敏财务数据"""
        df[column] = "***"
        return "removed"
    
    def _anonymize_geographic_data(self, df, column):
        """脱敏地理信息"""
        if self.config["geographic_strategy"] == "generic":
            # 使用通用地区代号
            region_mapping = {
                "北京": "华北地区",
                "上海": "华东地区", 
                "广州": "华南地区",
                "深圳": "华南地区"
            }
            df[column] = df[column].map(region_mapping).fillna("其他地区")
            return "generic_regions"
        
        return None  # 保持原样
    
    def _anonymize_date_data(self, df, column):
        """脱敏日期数据"""
        if self.config["date_strategy"] == "shift":
            # 随机偏移日期，保持相对关系
            base_shift = random.randint(-365, 365)  # 随机偏移-365到365天
            
            def shift_date(date_str):
                try:
                    date_obj = pd.to_datetime(date_str)
                    shifted_date = date_obj + timedelta(days=base_shift)
                    return shifted_date.strftime('%Y-%m-%d')
                except:
                    return date_str
            
            df[column] = df[column].apply(shift_date)
            return f"shifted_by_{base_shift}_days"
        
        return None  # 保持原样

def create_anonymization_config():
    """创建脱敏配置文件"""
    config = {
        "anonymization_level": "medium",
        "preserve_data_patterns": True,
        "preserve_relationships": True,
        "company_name_strategy": "generic",
        "personal_data_strategy": "hash",
        "financial_data_strategy": "scale",
        "date_strategy": "shift",
        "geographic_strategy": "generic",
        "description": {
            "anonymization_level": "脱敏级别: low(低), medium(中), high(高)",
            "preserve_data_patterns": "是否保持数据模式",
            "preserve_relationships": "是否保持数据关系",
            "company_name_strategy": "公司名称策略: generic(通用), hash(哈希), fake(假名)",
            "personal_data_strategy": "个人数据策略: hash(哈希), fake(假名), remove(移除)",
            "financial_data_strategy": "财务数据策略: scale(缩放), range(范围), remove(移除)",
            "date_strategy": "日期策略: shift(偏移), remove(移除), keep(保持)",
            "geographic_strategy": "地理策略: generic(通用), remove(移除), keep(保持)"
        }
    }

    with open("anonymization_config.json", 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)

    return config

def demo_anonymization():
    """演示脱敏效果"""
    print("🔒 数据脱敏方案演示")
    print("=" * 60)

    # 加载原始数据
    df_original = pd.read_csv('uploaded_files/sales_data.csv')
    print("📊 原始数据:")
    print(df_original.head())
    print()

    # 创建脱敏器
    anonymizer = DataAnonymizer()

    # 执行脱敏
    df_anonymized, report = anonymizer.anonymize_dataframe(df_original)

    print("🔒 脱敏后数据:")
    print(df_anonymized.head())
    print()

    print("📋 脱敏报告:")
    print(f"原始数据形状: {report['original_shape']}")
    print(f"脱敏列: {report['anonymized_columns']}")
    print(f"保留列: {report['preserved_columns']}")
    print(f"脱敏方法: {report['anonymization_methods']}")

    return df_anonymized, report

def integrate_with_llm():
    """集成到LLM调用中"""
    print("\n🔧 LLM集成方案")
    print("=" * 60)

    integration_code = '''
# 在perfect_tongyi_integration.py中修改analyze_data函数

def analyze_data_with_anonymization(df, query, table_name="data_table", use_metadata=True, enable_anonymization=True):
    """带脱敏功能的数据分析"""

    if enable_anonymization:
        # 创建脱敏器
        from data_anonymization_solution import DataAnonymizer
        anonymizer = DataAnonymizer()

        # 脱敏数据
        df_safe, anonymization_report = anonymizer.anonymize_dataframe(df, preserve_analysis_capability=True)

        print(f"🔒 数据已脱敏，脱敏列: {anonymization_report['anonymized_columns']}")

        # 使用脱敏后的数据调用LLM
        code = llm.call(query, df_safe.to_string())
    else:
        # 使用原始数据
        code = llm.call(query, df.to_string())

    # 其余逻辑保持不变...
'''

    print(integration_code)

if __name__ == "__main__":
    # 创建配置文件
    create_anonymization_config()
    print("✅ 脱敏配置文件已创建: anonymization_config.json")

    # 演示脱敏效果
    demo_anonymization()

    # 显示集成方案
    integrate_with_llm()
