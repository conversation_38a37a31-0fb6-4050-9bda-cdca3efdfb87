#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终图表修复测试
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def final_chart_fix_test():
    """最终图表修复测试"""
    print("🎯 最终图表修复测试")
    print("=" * 60)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500.0, 20200.0, 15000.0, 9700.0, 8000.0],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    print("测试数据:")
    print(df)
    print()
    
    # 测试查询
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 40)
    
    try:
        result = analyze_data(df, query, "sales_data", use_metadata=True)
        
        if result.get('success'):
            print("✅ 分析成功")
            
            # 检查生成的代码
            code = result.get('code', '')
            output = result.get('output', '')
            
            print("生成的代码:")
            print(code)
            print()
            
            print("执行输出:")
            print(output)
            print()
            
            # 分析代码特征
            analysis = analyze_final_code(code)
            
            print("📊 最终代码分析:")
            for key, value in analysis.items():
                status = "✅" if value else "❌"
                print(f"  {status} {key}")
            
            # 检查是否解决了所有问题
            all_good = (
                analysis['uses_streamlit_native'] and
                not analysis['has_problematic_fields'] and
                not analysis['uses_plotly'] and
                analysis['has_data_cleaning']
            )
            
            if all_good:
                print("\n🎉 完美！所有问题都已解决：")
                print("  ✅ 使用Streamlit原生图表")
                print("  ✅ 无异常字段")
                print("  ✅ 无Plotly代码")
                print("  ✅ 包含数据清理")
                print("\n💡 现在应该不会再有控制台警告了！")
            else:
                print("\n⚠️ 仍有问题需要解决：")
                if not analysis['uses_streamlit_native']:
                    print("  - 未使用Streamlit原生图表")
                if analysis['has_problematic_fields']:
                    print("  - 仍有异常字段")
                if analysis['uses_plotly']:
                    print("  - 仍在使用Plotly")
                if not analysis['has_data_cleaning']:
                    print("  - 缺少数据清理")
            
            return analysis
            
        else:
            print(f"❌ 分析失败: {result.get('error', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_final_code(code):
    """分析最终代码"""
    analysis = {
        'uses_streamlit_native': False,
        'uses_plotly': False,
        'has_data_cleaning': False,
        'has_problematic_fields': False,
        'has_syntax_errors': False,
        'has_import_conflicts': False
    }
    
    # 检查Streamlit原生方法
    streamlit_methods = ['st.bar_chart', 'st.line_chart', 'st.scatter_chart', 'st.area_chart']
    analysis['uses_streamlit_native'] = any(method in code for method in streamlit_methods)
    
    # 检查Plotly使用
    plotly_indicators = ['import plotly', 'px.', 'go.', 'st.plotly_chart']
    analysis['uses_plotly'] = any(indicator in code for indicator in plotly_indicators)
    
    # 检查数据清理
    cleaning_indicators = ['replace([np.inf, -np.inf]', '.fillna(', '.astype(float)', 'dropna']
    analysis['has_data_cleaning'] = any(indicator in code for indicator in cleaning_indicators)
    
    # 检查异常字段
    problematic_fields = ['销售额_start', '销售额_end']
    analysis['has_problematic_fields'] = any(field in code for field in problematic_fields)
    
    # 检查语法错误
    try:
        compile(code, '<string>', 'exec')
        analysis['has_syntax_errors'] = False
    except SyntaxError:
        analysis['has_syntax_errors'] = True
    
    # 检查导入冲突
    import_conflicts = ['import streamlit as st', 'import pandas as pd']
    analysis['has_import_conflicts'] = any(conflict in code for conflict in import_conflicts)
    
    return analysis

def test_multiple_chart_types():
    """测试多种图表类型"""
    print("\n🧪 测试多种图表类型")
    print("=" * 40)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro'],
        '销售额': [25500.0, 20200.0, 15000.0],
        '销量': [120, 80, 40],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03']
    })
    
    test_queries = [
        "请分析各产品销售额，用柱状图展示",
        "生成销售额的折线图",
        "创建产品销量的条形图"
    ]
    
    results = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 测试 {i}: {query}")
        print("-" * 20)
        
        try:
            result = analyze_data(df, query, f"test_data_{i}", use_metadata=True)
            
            if result.get('success'):
                code = result.get('code', '')
                analysis = analyze_final_code(code)
                
                print(f"使用Streamlit原生: {'✅' if analysis['uses_streamlit_native'] else '❌'}")
                print(f"使用Plotly: {'❌' if analysis['uses_plotly'] else '✅'}")
                print(f"有数据清理: {'✅' if analysis['has_data_cleaning'] else '❌'}")
                
                results.append(analysis)
            else:
                print(f"❌ 失败: {result.get('error', '未知错误')}")
                results.append(None)
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            results.append(None)
    
    # 统计结果
    successful_results = [r for r in results if r is not None]
    if successful_results:
        streamlit_native_count = sum(1 for r in successful_results if r['uses_streamlit_native'])
        no_plotly_count = sum(1 for r in successful_results if not r['uses_plotly'])
        
        print(f"\n📊 多图表类型测试结果:")
        print(f"  成功率: {len(successful_results)}/{len(test_queries)} ({len(successful_results)/len(test_queries)*100:.1f}%)")
        print(f"  Streamlit原生使用率: {streamlit_native_count}/{len(successful_results)} ({streamlit_native_count/len(successful_results)*100 if successful_results else 0:.1f}%)")
        print(f"  无Plotly使用率: {no_plotly_count}/{len(successful_results)} ({no_plotly_count/len(successful_results)*100 if successful_results else 0:.1f}%)")

def provide_restart_instructions():
    """提供重启指导"""
    print(f"\n📋 重启指导")
    print("=" * 30)
    
    print("如果测试显示修复成功，请按以下步骤重启Streamlit：")
    print()
    print("1️⃣ 停止当前Streamlit服务:")
    print("   在运行Streamlit的终端按 Ctrl+C")
    print()
    print("2️⃣ 清理Python缓存:")
    print("   find . -type d -name '__pycache__' -exec rm -rf {} + 2>/dev/null")
    print("   find . -name '*.pyc' -delete 2>/dev/null")
    print()
    print("3️⃣ 重启Streamlit服务:")
    print("   streamlit run streamlit_app.py")
    print()
    print("4️⃣ 清理浏览器缓存:")
    print("   按 Ctrl+Shift+R 强制刷新")
    print("   或在开发者工具中清空缓存")
    print()
    print("5️⃣ 测试图表查询:")
    print("   输入: '请分析各产品销售额，用柱状图展示'")
    print("   观察控制台是否还有警告信息")
    print()
    print("🎯 预期结果:")
    print("  ✅ 图表稳定显示，不再闪退")
    print("  ✅ 控制台无 'Infinite extent' 警告")
    print("  ✅ 控制台无 'Scale bindings' 警告")
    print("  ✅ 无异常字段 (销售额_start/销售额_end)")

if __name__ == "__main__":
    print("🚀 最终图表修复验证工具")
    print("=" * 60)
    
    # 1. 主要测试
    main_result = final_chart_fix_test()
    
    # 2. 多图表类型测试
    test_multiple_chart_types()
    
    # 3. 提供重启指导
    provide_restart_instructions()
    
    # 4. 最终总结
    print(f"\n🎯 最终总结")
    print("=" * 30)
    
    if main_result:
        all_good = (
            main_result['uses_streamlit_native'] and
            not main_result['has_problematic_fields'] and
            not main_result['uses_plotly'] and
            main_result['has_data_cleaning']
        )
        
        if all_good:
            print("🎉 恭喜！所有修复都已生效")
            print("现在可以重启Streamlit服务，图表闪退问题应该完全解决了！")
        else:
            print("⚠️ 修复部分生效，可能需要进一步调整")
    else:
        print("❌ 测试失败，需要检查修复实现")
    
    print(f"\n📞 如果重启后仍有问题，请提供:")
    print("1. 重启后的第一次图表查询结果")
    print("2. 浏览器控制台的完整错误信息")
    print("3. 生成的具体代码内容")
