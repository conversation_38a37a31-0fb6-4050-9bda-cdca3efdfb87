#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的应用
验证导入和基本功能是否正常
"""

def test_imports():
    """测试所有导入是否正常"""
    print("🧪 测试导入...")
    
    try:
        from result_formatter import EnhancedResultFormatter
        print("✅ EnhancedResultFormatter 导入成功")
        
        from perfect_tongyi_integration import analyze_data
        print("✅ analyze_data 导入成功")
        
        import pandas as pd
        print("✅ pandas 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能...")
    
    try:
        from result_formatter import EnhancedResultFormatter
        from perfect_tongyi_integration import analyze_data
        import pandas as pd
        
        # 创建测试数据
        test_data = pd.DataFrame({
            '产品': ['A', 'B', 'C'],
            '价格': [100, 200, 300],
            '销量': [10, 20, 30]
        })
        
        # 测试简单查询
        result = analyze_data(test_data, "显示数据基本信息")
        
        if result and result.get('success'):
            print("✅ analyze_data 函数正常工作")
            
            # 测试输出类型检测
            output_type = EnhancedResultFormatter._detect_output_type(result.get('output', ''))
            print(f"✅ 输出类型检测: {output_type}")
            
            return True
        else:
            print("❌ analyze_data 函数执行失败")
            return False
            
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 修复后应用测试")
    print("=" * 40)
    
    # 测试导入
    import_success = test_imports()
    
    if import_success:
        # 测试基本功能
        function_success = test_basic_functionality()
        
        if function_success:
            print("\n🎉 所有测试通过！")
            print("✅ 应用已成功修复")
            print("🌐 访问: http://localhost:8506")
        else:
            print("\n❌ 功能测试失败")
    else:
        print("\n❌ 导入测试失败")

if __name__ == "__main__":
    main()
