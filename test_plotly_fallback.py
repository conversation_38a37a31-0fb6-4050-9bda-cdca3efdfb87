#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Plotly回退机制
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_plotly_fallback():
    """测试Plotly回退机制"""
    print("🔄 测试Plotly回退机制")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据: {df.shape}")
    
    # 测试饼图查询
    query = "请为我分析2024年各产品销售额，并用饼图展示"
    print(f"🔍 查询: {query}")
    
    try:
        result = analyze_data(df, query, table_name="sales_data")
        
        if result and result.get('success'):
            print("✅ 查询成功")
            
            code = result.get('code', '')
            output = result.get('output', '')
            has_chart = result.get('has_chart', False)
            
            print(f"\n📝 生成的代码:")
            print(code)
            
            # 分析使用的技术栈
            uses_plotly = 'plotly' in code.lower() and 'px.pie' in code
            uses_matplotlib = 'matplotlib' in code.lower() and 'plt.pie' in code
            has_try_except = 'try:' in code and 'except ImportError:' in code
            
            print(f"\n🔍 技术栈分析:")
            print(f"  📊 使用Plotly: {'✅' if uses_plotly else '❌'}")
            print(f"  📊 使用Matplotlib: {'✅' if uses_matplotlib else '❌'}")
            print(f"  🔄 包含回退机制: {'✅' if has_try_except else '❌'}")
            
            print(f"\n📊 执行输出:")
            print(output if output.strip() else "(无输出)")
            
            print(f"\n📈 图表生成: {'✅' if has_chart else '❌'}")
            
            # 评估回退机制效果
            if has_try_except:
                print("\n🎉 智能回退机制工作正常:")
                if uses_plotly:
                    print("  ✅ 优先尝试Plotly原生方案")
                if uses_matplotlib:
                    print("  ✅ 提供Matplotlib备用方案")
                print("  ✅ 确保饼图功能始终可用")
            else:
                print("\n⚠️ 回退机制可能未正确实现")
                
        else:
            print("❌ 查询失败")
            if result:
                print(f"错误: {result.get('error', '未知错误')}")
                
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        import traceback
        traceback.print_exc()

def test_manual_fallback():
    """手动测试回退逻辑"""
    print(f"\n🧪 手动测试回退逻辑")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    print("1️⃣ 测试Plotly可用性:")
    try:
        import plotly.express as px
        print("  ✅ Plotly可用")
        
        # 测试Plotly饼图
        product_sales = df.groupby('产品名称')['销售金额'].sum().reset_index()
        fig = px.pie(product_sales, values='销售金额', names='产品名称', 
                     title='销售金额分布')
        print("  ✅ Plotly饼图创建成功")
        
    except ImportError as e:
        print(f"  ❌ Plotly不可用: {e}")
    except Exception as e:
        print(f"  ❌ Plotly使用失败: {e}")
    
    print("\n2️⃣ 测试Matplotlib备用方案:")
    try:
        import matplotlib.pyplot as plt
        
        product_sales = df.groupby('产品名称')['销售金额'].sum()
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc']
        
        plt.figure(figsize=(10, 8))
        wedges, texts, autotexts = plt.pie(product_sales.values, 
                                          labels=product_sales.index, 
                                          autopct='%1.1f%%', 
                                          startangle=90, 
                                          colors=colors, 
                                          shadow=True)
        plt.title('销售金额分布', fontsize=16, fontweight='bold')
        plt.legend(wedges, product_sales.index, title="产品类别", 
                  loc="center left", bbox_to_anchor=(1, 0, 0.5, 1))
        plt.axis('equal')
        plt.tight_layout()
        
        # 保存测试
        plt.savefig('test_matplotlib_pie.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("  ✅ Matplotlib饼图创建成功")
        print("  ✅ 包含图例和样式优化")
        
    except Exception as e:
        print(f"  ❌ Matplotlib使用失败: {e}")

def summarize_solution():
    """总结解决方案"""
    print(f"\n🎯 解决方案总结")
    print("=" * 50)
    
    print("🔄 智能回退机制:")
    print("  1. 优先尝试Plotly原生方案")
    print("     - 完美的Streamlit集成")
    print("     - 自动图例和交互功能")
    print("     - 现代化视觉效果")
    print()
    print("  2. 自动回退到Matplotlib方案")
    print("     - 优化的图例显示")
    print("     - 丰富的颜色和样式")
    print("     - 确保功能始终可用")
    print()
    print("🎉 最终效果:")
    print("  ✅ 无论环境如何，饼图功能都能正常工作")
    print("  ✅ 优先使用最佳方案（Plotly原生）")
    print("  ✅ 确保向下兼容（Matplotlib备用）")
    print("  ✅ 用户体验始终一致")

if __name__ == "__main__":
    test_plotly_fallback()
    test_manual_fallback()
    summarize_solution()
