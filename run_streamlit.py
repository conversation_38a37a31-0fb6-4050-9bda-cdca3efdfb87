#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit应用启动脚本
自动检查依赖并启动应用
"""

import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'streamlit',
        'pandas',
        'pandasai',
        'python-dotenv',
        'requests',
        'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies():
    """安装缺失的依赖"""
    requirements_file = Path("requirements_streamlit.txt")
    
    if requirements_file.exists():
        print("📦 正在安装依赖...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
            ])
            print("✅ 依赖安装完成")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ 依赖安装失败: {e}")
            return False
    else:
        print("❌ 未找到requirements_streamlit.txt文件")
        return False

def check_env_file():
    """检查环境变量文件"""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("⚠️ 未找到.env文件")
        print("请创建.env文件并添加以下内容:")
        print("DASHSCOPE_API_KEY=your-dashscope-api-key-here")
        
        # 创建示例.env文件
        with open(".env.example", "w", encoding="utf-8") as f:
            f.write("# 通义千问API密钥\n")
            f.write("DASHSCOPE_API_KEY=your-dashscope-api-key-here\n")
        
        print("已创建.env.example文件作为参考")
        return False
    
    # 检查API密钥是否配置
    from dotenv import load_dotenv
    load_dotenv()
    
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key or api_key == 'your-dashscope-api-key-here':
        print("⚠️ DASHSCOPE_API_KEY未正确配置")
        print("请在.env文件中设置正确的API密钥")
        return False
    
    print("✅ 环境变量配置正确")
    return True

def create_directories():
    """创建必要的目录"""
    directories = [
        "uploaded_files",
        "chat_history",
        "charts"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ 目录结构创建完成")

def run_streamlit():
    """启动Streamlit应用"""
    print("🚀 启动Streamlit应用...")
    
    try:
        # 设置Streamlit配置
        os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
        os.environ['STREAMLIT_SERVER_PORT'] = '8501'
        
        # 启动应用
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "streamlit_app.py",
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🤖 智能数据分析助手 - 启动脚本")
    print("=" * 50)
    
    # 1. 检查依赖
    print("1️⃣ 检查依赖...")
    missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"❌ 缺少依赖: {', '.join(missing_packages)}")
        
        install_choice = input("是否自动安装缺失的依赖? (y/n): ").lower().strip()
        if install_choice in ['y', 'yes', '是']:
            if not install_dependencies():
                print("❌ 依赖安装失败，请手动安装")
                return
        else:
            print("请手动安装依赖后重新运行")
            return
    else:
        print("✅ 所有依赖已安装")
    
    # 2. 检查环境变量
    print("\n2️⃣ 检查环境变量...")
    if not check_env_file():
        print("❌ 环境变量配置有问题，请修复后重新运行")
        return
    
    # 3. 创建目录
    print("\n3️⃣ 创建目录结构...")
    create_directories()
    
    # 4. 启动应用
    print("\n4️⃣ 启动应用...")
    print("🌐 应用将在浏览器中打开: http://localhost:8501")
    print("按 Ctrl+C 停止应用")
    print("-" * 50)
    
    run_streamlit()

if __name__ == "__main__":
    main()
