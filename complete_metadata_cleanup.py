#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的元数据清理方案
彻底清除销售额_start和销售额_end字段及其无穷大值
"""

import json
import pandas as pd
import numpy as np
from pathlib import Path
import shutil
from datetime import datetime

def backup_metadata():
    """备份元数据文件"""
    print("📁 备份元数据文件...")
    
    metadata_path = Path("metadata_config/tables_metadata.json")
    if metadata_path.exists():
        backup_path = metadata_path.with_suffix(f'.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        shutil.copy2(metadata_path, backup_path)
        print(f"✅ 元数据已备份到: {backup_path}")
        return backup_path
    else:
        print("⚠️ 元数据文件不存在")
        return None

def clean_metadata_file():
    """清理元数据文件"""
    print("🧹 清理元数据文件...")
    
    metadata_path = Path("metadata_config/tables_metadata.json")
    if not metadata_path.exists():
        print("⚠️ 元数据文件不存在")
        return
    
    with open(metadata_path, 'r', encoding='utf-8') as f:
        metadata = json.load(f)
    
    problematic_fields = ['销售额_start', '销售额_end']
    cleaned_tables = []
    removed_tables = []
    
    # 清理每个表格
    for table_name in list(metadata.keys()):
        table_data = metadata[table_name]
        
        if 'columns' in table_data:
            # 检查是否包含问题字段
            has_problematic_fields = any(field in table_data['columns'] for field in problematic_fields)
            
            if has_problematic_fields:
                # 检查是否是测试表格（可以直接删除）
                if any(keyword in table_name.lower() for keyword in ['test', 'chart_test', 'problematic', 'vega_lite']):
                    print(f"🗑️ 删除测试表格: {table_name}")
                    del metadata[table_name]
                    removed_tables.append(table_name)
                else:
                    # 对于非测试表格，只删除问题字段
                    print(f"🔧 清理表格: {table_name}")
                    for field in problematic_fields:
                        if field in table_data['columns']:
                            print(f"  删除字段: {field}")
                            del table_data['columns'][field]
                    
                    # 清理主键列表
                    if 'primary_keys' in table_data:
                        original_keys = table_data['primary_keys']
                        cleaned_keys = [key for key in original_keys if key not in problematic_fields]
                        table_data['primary_keys'] = cleaned_keys
                        if original_keys != cleaned_keys:
                            print(f"  清理主键: {original_keys} → {cleaned_keys}")
                    
                    cleaned_tables.append(table_name)
    
    # 保存清理后的元数据
    with open(metadata_path, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 元数据清理完成:")
    print(f"  清理的表格: {len(cleaned_tables)}")
    print(f"  删除的表格: {len(removed_tables)}")
    
    return cleaned_tables, removed_tables

def clean_test_files():
    """清理测试文件"""
    print("🧹 清理测试文件...")
    
    test_files = [
        "uploaded_files/test_problematic_data.csv",
        "uploaded_files/test_cleaned_data.csv"
    ]
    
    removed_files = []
    
    for file_path in test_files:
        path = Path(file_path)
        if path.exists():
            # 备份后删除
            backup_path = path.with_suffix(f'.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv')
            shutil.move(path, backup_path)
            print(f"🗑️ 移除测试文件: {file_path} → {backup_path}")
            removed_files.append(file_path)
        else:
            print(f"📁 文件不存在: {file_path}")
    
    return removed_files

def fix_metadata_inference():
    """修复元数据推断系统"""
    print("🔧 修复元数据推断系统...")
    
    # 修复metadata_inference.py中的示例值生成逻辑
    inference_path = Path("metadata_inference.py")
    if not inference_path.exists():
        print("⚠️ metadata_inference.py 不存在")
        return
    
    # 读取文件内容
    with open(inference_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否需要修复
    if 'str(val) for val in non_null_values' in content:
        print("🔧 需要修复示例值生成逻辑")
        
        # 备份原文件
        backup_path = inference_path.with_suffix(f'.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.py')
        shutil.copy2(inference_path, backup_path)
        print(f"📁 已备份: {backup_path}")
        
        # 替换有问题的代码
        old_code = "examples = [str(val) for val in non_null_values[:3]]"
        new_code = """# 安全的示例值生成（过滤无穷大值）
                examples = []
                for val in non_null_values[:3]:
                    if pd.isna(val) or np.isinf(val):
                        continue  # 跳过NaN和无穷大值
                    examples.append(str(val))"""
        
        if old_code in content:
            content = content.replace(old_code, new_code)
            
            # 保存修复后的文件
            with open(inference_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ 元数据推断系统已修复")
        else:
            print("⚠️ 未找到需要修复的代码")
    else:
        print("✅ 元数据推断系统无需修复")

def verify_cleanup():
    """验证清理效果"""
    print("🔍 验证清理效果...")
    
    # 检查元数据文件
    metadata_path = Path("metadata_config/tables_metadata.json")
    if metadata_path.exists():
        with open(metadata_path, 'r', encoding='utf-8') as f:
            metadata = json.load(f)
        
        problematic_fields = ['销售额_start', '销售额_end']
        found_issues = []
        
        for table_name, table_data in metadata.items():
            if 'columns' in table_data:
                for field in problematic_fields:
                    if field in table_data['columns']:
                        found_issues.append(f"{table_name}.{field}")
        
        if found_issues:
            print(f"❌ 仍有问题字段: {found_issues}")
        else:
            print("✅ 元数据文件已清理干净")
    
    # 检查原始数据文件
    csv_path = Path("uploaded_files/sales_data.csv")
    if csv_path.exists():
        df = pd.read_csv(csv_path)
        problematic_fields = ['销售额_start', '销售额_end']
        has_issues = any(field in df.columns for field in problematic_fields)
        
        if has_issues:
            print("❌ 原始CSV文件包含问题字段")
        else:
            print("✅ 原始CSV文件正常")
    
    print("✅ 验证完成")

def main():
    """主清理函数"""
    print("🧹 完整元数据清理方案")
    print("=" * 60)
    
    print("🎯 问题根源:")
    print("- 测试脚本创建了包含无穷大值的数据")
    print("- 元数据推断系统将无穷大值保存为示例值")
    print("- 污染的元数据导致后续图表渲染失败")
    print()
    
    # 执行清理步骤
    print("🔧 开始清理...")
    
    # 1. 备份元数据
    backup_path = backup_metadata()
    
    # 2. 清理元数据文件
    cleaned_tables, removed_tables = clean_metadata_file()
    
    # 3. 清理测试文件
    removed_files = clean_test_files()
    
    # 4. 修复元数据推断系统
    fix_metadata_inference()
    
    # 5. 验证清理效果
    verify_cleanup()
    
    print("\n📊 清理总结:")
    print(f"✅ 备份文件: {backup_path}")
    print(f"✅ 清理的表格: {len(cleaned_tables)}")
    print(f"✅ 删除的表格: {len(removed_tables)}")
    print(f"✅ 删除的文件: {len(removed_files)}")
    
    print("\n💡 下一步:")
    print("1. 重启Streamlit应用")
    print("2. 只使用原始sales_data.csv文件")
    print("3. 测试图表生成功能")
    print("4. 确认控制台不再有Vega-Lite警告")
    
    print("\n🎉 清理完成！图表闪退问题应该已解决。")

if __name__ == "__main__":
    main()
