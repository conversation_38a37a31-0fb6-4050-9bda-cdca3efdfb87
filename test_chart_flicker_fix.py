#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图表闪烁问题修复效果
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data
from enhanced_tongyi_integration import EnhancedTongyiQianwenLLM

def test_chart_stability():
    """测试图表稳定性"""
    print("🧪 测试图表闪烁问题修复效果")
    print("=" * 60)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500, 20200, 15000, 9700, 8000],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    # 测试查询
    test_queries = [
        "请分析各产品销售额，用柱状图展示",
        "生成销售额的折线图",
        "创建产品销量的条形图"
    ]
    
    results = []
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📋 测试 {i}: {query}")
        print("-" * 40)
        
        try:
            # 使用增强版LLM进行分析
            result = analyze_data(df, query, "sales_data", use_metadata=True)
            
            if result.get('success'):
                print("✅ 分析成功")
                
                # 检查生成的代码
                code = result.get('code', '')
                analysis = analyze_chart_code(code)
                
                results.append({
                    'query': query,
                    'success': True,
                    'analysis': analysis
                })
                
                # 显示代码分析结果
                print("📊 代码分析:")
                for key, value in analysis.items():
                    status = "✅" if value else "❌"
                    print(f"  {status} {key}")
                
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
                results.append({
                    'query': query,
                    'success': False,
                    'analysis': {'error': result.get('error', '未知错误')}
                })
                
        except Exception as e:
            print(f"❌ 执行异常: {e}")
            results.append({
                'query': query,
                'success': False,
                'analysis': {'exception': str(e)}
            })
    
    # 总结结果
    print("\n" + "=" * 60)
    print("🎯 修复效果总结")
    print("=" * 60)
    
    success_count = sum(1 for r in results if r['success'])
    total_count = len(results)
    
    streamlit_native_count = sum(1 for r in results 
                                if r['success'] and r['analysis'].get('uses_streamlit_native'))
    no_matplotlib_count = sum(1 for r in results 
                             if r['success'] and not r['analysis'].get('uses_matplotlib'))
    
    print(f"成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    print(f"Streamlit原生使用率: {streamlit_native_count}/{success_count} ({streamlit_native_count/success_count*100 if success_count > 0 else 0:.1f}%)")
    print(f"无matplotlib代码率: {no_matplotlib_count}/{success_count} ({no_matplotlib_count/success_count*100 if success_count > 0 else 0:.1f}%)")
    
    if streamlit_native_count == success_count and no_matplotlib_count == success_count:
        print("\n✅ 修复完全成功！")
        print("- 所有图表都使用Streamlit原生方法")
        print("- 完全消除了matplotlib回退机制")
        print("- 图表闪烁问题应该已解决")
    elif streamlit_native_count >= success_count * 0.8:
        print("\n✅ 修复效果良好")
        print("- 大部分图表使用Streamlit原生方法")
        print("- 显著减少了渲染冲突")
    else:
        print("\n⚠️ 修复效果有限，需要进一步优化")
    
    return results

def analyze_chart_code(code):
    """分析图表代码"""
    if not code:
        return {'error': 'No code generated'}
    
    analysis = {
        'uses_streamlit_native': False,
        'uses_matplotlib': False,
        'uses_plotly': False,
        'has_multiple_chart_methods': False,
        'has_import_conflicts': False,
        'code_simplified': False
    }
    
    # 检查Streamlit原生方法
    streamlit_methods = ['st.bar_chart', 'st.line_chart', 'st.scatter_chart', 'st.area_chart']
    streamlit_count = sum(1 for method in streamlit_methods if method in code)
    analysis['uses_streamlit_native'] = streamlit_count > 0
    
    # 检查matplotlib
    matplotlib_indicators = ['import matplotlib', 'plt.', 'matplotlib.']
    analysis['uses_matplotlib'] = any(indicator in code for indicator in matplotlib_indicators)
    
    # 检查plotly
    plotly_indicators = ['import plotly', 'px.', 'go.', 'st.plotly_chart']
    plotly_count = sum(1 for indicator in plotly_indicators if indicator in code)
    analysis['uses_plotly'] = plotly_count > 0
    
    # 检查是否有多种图表方法（可能导致冲突）
    total_methods = streamlit_count + (1 if analysis['uses_matplotlib'] else 0) + (1 if analysis['uses_plotly'] else 0)
    analysis['has_multiple_chart_methods'] = total_methods > 1
    
    # 检查导入冲突
    import_statements = ['import streamlit as st', 'import pandas as pd', 'import matplotlib']
    analysis['has_import_conflicts'] = any(stmt in code for stmt in import_statements)
    
    # 检查代码是否简化（没有复杂的try-except结构）
    analysis['code_simplified'] = 'try:' not in code or code.count('try:') <= 1
    
    return analysis

def test_data_format_compatibility():
    """测试数据格式兼容性"""
    print("\n🧪 测试数据格式兼容性")
    print("=" * 40)
    
    # 创建各种数据格式进行测试
    test_cases = [
        {
            'name': '正常数据',
            'data': pd.DataFrame({
                '产品': ['A', 'B', 'C'],
                '销售额': [100, 200, 150]
            })
        },
        {
            'name': '包含NaN的数据',
            'data': pd.DataFrame({
                '产品': ['A', 'B', 'C'],
                '销售额': [100, None, 150]
            })
        },
        {
            'name': '包含零值的数据',
            'data': pd.DataFrame({
                '产品': ['A', 'B', 'C'],
                '销售额': [100, 0, 150]
            })
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📋 测试: {test_case['name']}")
        df = test_case['data']
        
        try:
            # 模拟Streamlit原生图表的数据处理
            data_series = df.groupby('产品')['销售额'].sum()
            
            # 检查数据是否包含无效值
            has_invalid = data_series.isnull().any() or (data_series == float('inf')).any() or (data_series == float('-inf')).any()
            
            if has_invalid:
                print("⚠️ 数据包含无效值，需要清理")
                # 清理数据
                data_series = data_series.fillna(0).replace([float('inf'), float('-inf')], 0)
                print("✅ 数据已清理")
            else:
                print("✅ 数据格式正常")
                
        except Exception as e:
            print(f"❌ 数据处理失败: {e}")

if __name__ == "__main__":
    test_chart_stability()
    test_data_format_compatibility()
    
    print("\n" + "=" * 60)
    print("🎯 图表闪烁问题修复总结")
    print("=" * 60)
    print("问题：图表初始显示正常，但随后闪烁并消失")
    print()
    print("根本原因：")
    print("1. 多重回退机制导致渲染冲突")
    print("2. matplotlib和Streamlit原生图表同时渲染")
    print("3. 数据格式问题导致无效的图表范围")
    print()
    print("解决方案：")
    print("1. ✅ 移除matplotlib回退机制")
    print("2. ✅ 强制使用Streamlit原生图表方法")
    print("3. ✅ 简化执行环境，消除渲染冲突")
    print("4. ✅ 改进数据格式处理")
    print()
    print("预期效果：")
    print("- 图表稳定显示，不再闪烁")
    print("- 消除控制台警告信息")
    print("- 代码逻辑简化，性能提升")
