#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试综合修复效果
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_comprehensive_fix():
    """测试综合修复效果"""
    print("🎯 测试综合图表修复效果")
    print("=" * 60)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro'],
        '销售额': [25500.0, 20200.0, 15000.0, 9700.0]
    })
    
    print("测试数据:")
    print(df)
    print()
    
    # 测试查询
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 40)
    
    try:
        result = analyze_data(df, query, "comprehensive_test", use_metadata=True)
        
        if result.get('success'):
            print("✅ 分析成功")
            
            # 检查生成的代码
            code = result.get('code', '')
            cleaned_code = result.get('plotly_code', '')
            
            print("原始生成代码:")
            print(code)
            print()
            
            print("修复后代码:")
            print(cleaned_code)
            print()
            
            # 分析修复效果
            fix_analysis = analyze_fix_effectiveness(code, cleaned_code)
            
            print("🔍 修复效果分析:")
            for aspect, result in fix_analysis.items():
                status = "✅" if result['success'] else "❌"
                print(f"  {status} {aspect}: {result['description']}")
            
            # 计算总体修复成功率
            success_count = sum(1 for r in fix_analysis.values() if r['success'])
            total_aspects = len(fix_analysis)
            success_rate = (success_count / total_aspects) * 100
            
            print(f"\n📊 综合修复成功率: {success_count}/{total_aspects} ({success_rate:.1f}%)")
            
            if success_rate >= 80:
                print("🎉 综合修复大获成功！")
                return True
            elif success_rate >= 60:
                print("⚠️ 综合修复部分成功")
                return True
            else:
                print("❌ 综合修复效果不佳")
                return False
                
        else:
            print(f"❌ 分析失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_fix_effectiveness(original_code, fixed_code):
    """分析修复效果"""
    analysis = {
        '单一图表类型': {
            'success': False,
            'description': '检查是否只生成一种图表类型'
        },
        'Streamlit原生': {
            'success': False,
            'description': '检查是否使用Streamlit原生图表'
        },
        '无Plotly依赖': {
            'success': False,
            'description': '检查是否消除了Plotly依赖'
        },
        '数据清理': {
            'success': False,
            'description': '检查是否包含数据清理机制'
        },
        '语法正确': {
            'success': False,
            'description': '检查代码语法是否正确'
        },
        '安全渲染': {
            'success': False,
            'description': '检查是否包含安全渲染检查'
        }
    }
    
    # 检查单一图表类型
    chart_methods = ['st.bar_chart', 'st.line_chart', 'st.area_chart', 'px.', 'st.plotly_chart']
    found_methods = [method for method in chart_methods if method in fixed_code]
    
    if len(found_methods) == 1 and found_methods[0].startswith('st.') and not found_methods[0] == 'st.plotly_chart':
        analysis['单一图表类型']['success'] = True
        analysis['单一图表类型']['description'] += f" - 使用: {found_methods[0]}"
    else:
        analysis['单一图表类型']['description'] += f" - 发现: {found_methods}"
    
    # 检查Streamlit原生
    streamlit_native = ['st.bar_chart', 'st.line_chart', 'st.area_chart']
    uses_native = any(method in fixed_code for method in streamlit_native)
    
    if uses_native:
        analysis['Streamlit原生']['success'] = True
        analysis['Streamlit原生']['description'] += " - 使用原生方法"
    else:
        analysis['Streamlit原生']['description'] += " - 未使用原生方法"
    
    # 检查无Plotly依赖
    plotly_indicators = ['import plotly', 'px.', 'st.plotly_chart']
    has_plotly = any(indicator in fixed_code for indicator in plotly_indicators)
    
    if not has_plotly:
        analysis['无Plotly依赖']['success'] = True
        analysis['无Plotly依赖']['description'] += " - 已消除Plotly"
    else:
        analysis['无Plotly依赖']['description'] += " - 仍有Plotly依赖"
    
    # 检查数据清理
    cleaning_indicators = ['数据清理', 'replace([np.inf', 'fillna(', 'pd.to_numeric']
    has_cleaning = any(indicator in fixed_code for indicator in cleaning_indicators)
    
    if has_cleaning:
        analysis['数据清理']['success'] = True
        analysis['数据清理']['description'] += " - 包含清理机制"
    else:
        analysis['数据清理']['description'] += " - 缺少清理机制"
    
    # 检查语法正确性
    try:
        compile(fixed_code, '<string>', 'exec')
        analysis['语法正确']['success'] = True
        analysis['语法正确']['description'] += " - 语法无误"
    except SyntaxError as e:
        analysis['语法正确']['description'] += f" - 语法错误: {e}"
    
    # 检查安全渲染
    safety_indicators = ['chart_data.empty', 'chart_data.sum()', 'use_container_width=True']
    has_safety = any(indicator in fixed_code for indicator in safety_indicators)
    
    if has_safety:
        analysis['安全渲染']['success'] = True
        analysis['安全渲染']['description'] += " - 包含安全检查"
    else:
        analysis['安全渲染']['description'] += " - 缺少安全检查"
    
    return analysis

def test_multiple_scenarios():
    """测试多种场景"""
    print("\n🧪 测试多种修复场景")
    print("=" * 40)
    
    df = pd.DataFrame({
        '产品名称': ['iPhone', 'iPad', 'MacBook'],
        '销售额': [25500.0, 20200.0, 15000.0]
    })
    
    test_scenarios = [
        ("请用柱状图分析产品销售额", "柱状图"),
        ("生成产品销售额的饼图", "饼图转柱状图"),
        ("用折线图显示销售趋势", "折线图")
    ]
    
    results = []
    
    for query, expected in test_scenarios:
        print(f"\n📋 场景: {query}")
        print(f"期望: {expected}")
        print("-" * 20)
        
        try:
            result = analyze_data(df, query, f"scenario_{len(results)}", use_metadata=True)
            
            if result.get('success'):
                cleaned_code = result.get('plotly_code', '')
                
                # 检查关键指标
                has_streamlit_native = any(method in cleaned_code for method in ['st.bar_chart', 'st.line_chart'])
                no_plotly = not any(indicator in cleaned_code for indicator in ['px.', 'st.plotly_chart'])
                has_safety = 'chart_data.empty' in cleaned_code
                
                print(f"Streamlit原生: {'✅' if has_streamlit_native else '❌'}")
                print(f"无Plotly: {'✅' if no_plotly else '❌'}")
                print(f"安全检查: {'✅' if has_safety else '❌'}")
                
                success = has_streamlit_native and no_plotly
                results.append(success)
            else:
                print(f"❌ 失败: {result.get('error')}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            results.append(False)
    
    success_rate = (sum(results) / len(results)) * 100 if results else 0
    print(f"\n📊 多场景测试成功率: {sum(results)}/{len(results)} ({success_rate:.1f}%)")
    
    return success_rate >= 80

if __name__ == "__main__":
    print("🚀 综合图表修复测试工具")
    print("=" * 60)
    
    # 1. 主要测试
    main_success = test_comprehensive_fix()
    
    # 2. 多场景测试
    multi_success = test_multiple_scenarios()
    
    # 3. 总结
    print(f"\n🎯 综合测试结果")
    print("=" * 30)
    
    if main_success and multi_success:
        print("🎉 综合修复完全成功！")
        print("所有关键问题都已解决：")
        print("✅ 单一图表类型生成")
        print("✅ 强制使用Streamlit原生图表")
        print("✅ 消除Plotly依赖和冲突")
        print("✅ 数据清理和安全渲染")
        print("✅ 语法错误修复")
        
        print(f"\n📋 立即重启测试:")
        print("1. 停止Streamlit服务: Ctrl+C")
        print("2. 清理缓存: find . -name '__pycache__' -exec rm -rf {} +")
        print("3. 重启服务: streamlit run streamlit_app.py")
        print("4. 清理浏览器缓存: Ctrl+Shift+R")
        print("5. 测试查询: '请分析各产品销售额，用柱状图展示'")
        
        print(f"\n🎯 预期效果:")
        print("✅ 只显示一个柱状图，不再有多图表")
        print("✅ 图表稳定显示，不再闪退")
        print("✅ 控制台无Vega-Lite警告")
        print("✅ 无异常字段名问题")
        
    elif main_success or multi_success:
        print("⚠️ 综合修复部分成功")
        print("建议重启Streamlit服务测试效果")
        
    else:
        print("❌ 综合修复未达到预期")
        print("需要进一步调试和优化")
    
    print(f"\n💡 技术总结:")
    print("这次综合修复解决了以下关键问题：")
    print("1. 强制转换所有图表为Streamlit原生类型")
    print("2. 消除Plotly依赖，避免多图表冲突")
    print("3. 添加完整的数据清理和安全检查")
    print("4. 修复语法错误和代码结构问题")
    print("5. 统一图表渲染流程，确保稳定性")
