#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断连续查询问题
"""

import pandas as pd
import os
from perfect_tongyi_integration import analyze_data

def test_consecutive_queries():
    """测试连续查询问题"""
    print("🔍 诊断连续查询问题")
    print("=" * 50)
    
    # 1. 加载数据
    print("1️⃣ 加载数据")
    try:
        df = pd.read_csv('uploaded_files/sales_data.csv')
        print(f"✅ 数据加载成功: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        print(f"📊 数据预览:")
        print(df.head())
        print()
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 2. 第一次查询
    print("2️⃣ 第一次查询")
    query1 = "分析2024年各产品的销售额"
    print(f"🔍 查询: {query1}")
    
    try:
        result1 = analyze_data(df, query1, table_name="sales_data")
        if result1 and result1.get('success'):
            print("✅ 第一次查询成功")
            print(f"📝 生成的代码:")
            print(result1.get('code', ''))
            print(f"📊 输出:")
            print(result1.get('output', ''))
        else:
            print("❌ 第一次查询失败")
            if result1:
                print(f"错误: {result1.get('error', '未知错误')}")
    except Exception as e:
        print(f"❌ 第一次查询异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "-" * 50)
    
    # 3. 第二次查询
    print("3️⃣ 第二次查询")
    query2 = "分析2024年销售最高的产品"
    print(f"🔍 查询: {query2}")
    
    try:
        result2 = analyze_data(df, query2, table_name="sales_data")
        if result2 and result2.get('success'):
            print("✅ 第二次查询成功")
            print(f"📝 生成的代码:")
            print(result2.get('code', ''))
            print(f"📊 输出:")
            print(result2.get('output', ''))
        else:
            print("❌ 第二次查询失败")
            if result2:
                print(f"错误: {result2.get('error', '未知错误')}")
    except Exception as e:
        print(f"❌ 第二次查询异常: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("🎯 诊断完成")

def check_working_directory():
    """检查工作目录"""
    print("📁 当前工作目录:", os.getcwd())
    print("📂 目录内容:")
    for item in os.listdir('.'):
        if os.path.isdir(item):
            print(f"  📁 {item}/")
        else:
            print(f"  📄 {item}")
    
    print("\n📂 uploaded_files目录:")
    if os.path.exists('uploaded_files'):
        for item in os.listdir('uploaded_files'):
            print(f"  📄 {item}")
    else:
        print("  ❌ uploaded_files目录不存在")

if __name__ == "__main__":
    check_working_directory()
    print()
    test_consecutive_queries()
