#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终解决方案测试
"""

import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_solution():
    """测试完整解决方案"""
    print("🎯 测试完整解决方案")
    print("=" * 60)
    
    # 加载数据
    try:
        df = pd.read_csv('uploaded_files/sales_data.csv')
        print(f"✅ 数据加载成功: {df.shape}")
        print(f"📋 列名: {list(df.columns)}")
        print()
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 模拟Streamlit环境
    class MockStreamlit:
        @staticmethod
        def plotly_chart(fig, use_container_width=True):
            print(f"📊 Plotly图表: {fig.layout.title.text}")
        
        @staticmethod
        def bar_chart(data):
            print(f"📊 Streamlit柱状图: {len(data)}个数据点")
        
        @staticmethod
        def dataframe(data, use_container_width=True):
            print(f"📋 数据表格: {data.shape}")
        
        @staticmethod
        def subheader(text):
            print(f"## {text}")
        
        @staticmethod
        def caption(text):
            print(f"   {text}")
        
        @staticmethod
        def write(text):
            print(f"   {text}")
        
        @staticmethod
        def warning(text):
            print(f"⚠️ {text}")
        
        @staticmethod
        def success(text):
            print(f"✅ {text}")
        
        @staticmethod
        def columns(n):
            return [MockStreamlit() for _ in range(n)]
        
        def __enter__(self):
            return self
        
        def __exit__(self, *args):
            pass
    
    # 导入修复后的函数
    try:
        from streamlit_app import fix_chart_generation_error, enhanced_analyze_with_fallback
        print("✅ 修复函数导入成功")
    except ImportError as e:
        print(f"❌ 修复函数导入失败: {e}")
        return
    
    # 测试用例
    test_queries = [
        "分析各地区的产品销售总额",  # 简单查询（应该成功）
        "请按照各地区各中各产品销售总额情况分析",  # 复杂查询（可能需要备用方案）
        "显示每个地区的产品销售分布"  # 另一个复杂查询
    ]
    
    # 模拟Streamlit全局变量
    import streamlit as st
    st = MockStreamlit()
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}️⃣ 测试查询: {query}")
        print("-" * 50)
        
        try:
            # 使用增强的分析函数
            result = enhanced_analyze_with_fallback(df, query)
            
            if result['success']:
                print("✅ 分析成功")
                
                if result.get('fallback_used'):
                    print("🔧 使用了备用方案")
                else:
                    print("🎯 使用了标准方案")
                
                # 显示结果信息
                analysis_result = result['result']
                if analysis_result.get('output'):
                    print(f"📄 输出长度: {len(analysis_result['output'])} 字符")
                
                if analysis_result.get('error'):
                    print(f"⚠️ 警告: {analysis_result['error']}")
                
                if analysis_result.get('has_chart'):
                    print("📊 包含图表")
                
            else:
                print(f"❌ 分析失败: {result.get('error')}")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")

def test_error_recovery():
    """测试错误恢复机制"""
    print("\n🔧 测试错误恢复机制")
    print("=" * 60)
    
    # 模拟有错误的结果
    mock_result = {
        'error': 'expected an indented block after \'for\' statement on line 5 (<string>, line 6)',
        'success': True,
        'output': 'some output'
    }
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    # 模拟Streamlit环境
    class MockStreamlit:
        @staticmethod
        def warning(text):
            print(f"⚠️ {text}")
        
        @staticmethod
        def subheader(text):
            print(f"## {text}")
        
        @staticmethod
        def dataframe(data, use_container_width=True):
            print(f"📋 数据表格显示: {data.shape}")
        
        @staticmethod
        def bar_chart(data):
            print(f"📊 柱状图显示: {len(data)}个数据点")
        
        @staticmethod
        def caption(text):
            print(f"   {text}")
        
        @staticmethod
        def write(text):
            print(f"   {text}")
        
        @staticmethod
        def success(text):
            print(f"✅ {text}")
        
        @staticmethod
        def columns(n):
            return [MockStreamlit() for _ in range(n)]
        
        def __enter__(self):
            return self
        
        def __exit__(self, *args):
            pass
    
    # 导入并测试错误修复函数
    try:
        from streamlit_app import fix_chart_generation_error
        
        # 模拟Streamlit
        import streamlit as st
        st = MockStreamlit()
        
        print("测试错误修复函数...")
        result = fix_chart_generation_error(mock_result, df)
        
        if result:
            print("✅ 错误修复成功")
        else:
            print("❌ 错误修复失败")
            
    except Exception as e:
        print(f"❌ 错误修复测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_complete_solution()
    test_error_recovery()
