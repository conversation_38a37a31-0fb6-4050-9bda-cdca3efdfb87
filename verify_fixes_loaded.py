#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复是否正确加载
"""

import sys
import importlib
import inspect

def verify_fixes_loaded():
    """验证所有修复是否正确加载"""
    print("🔍 验证修复是否正确加载")
    print("=" * 60)
    
    verification_results = {
        'enhanced_llm_fixes': False,
        'perfect_integration_fixes': False,
        'syntax_repair_methods': False,
        'code_cleaning_fixes': False
    }
    
    # 1. 验证增强版LLM的修复
    try:
        # 强制重新导入模块
        if 'enhanced_tongyi_integration' in sys.modules:
            importlib.reload(sys.modules['enhanced_tongyi_integration'])
        
        from enhanced_tongyi_integration import EnhancedTongyiQianwenLLM
        
        # 检查语法修复方法是否存在
        llm = EnhancedTongyiQianwenLLM()
        
        required_methods = ['fix_syntax_errors', 'validate_syntax', 'attempt_syntax_repair']
        methods_found = []
        
        for method_name in required_methods:
            if hasattr(llm, method_name):
                methods_found.append(method_name)
                print(f"✅ 找到方法: {method_name}")
            else:
                print(f"❌ 缺少方法: {method_name}")
        
        verification_results['syntax_repair_methods'] = len(methods_found) == len(required_methods)
        
        # 测试语法修复功能
        test_code = """import streamlit as st
fig = px.bar(data, x='x', y='y', title='test',
fig.update_layout()"""
        
        try:
            fixed_code = llm.fix_syntax_errors(test_code)
            is_valid = llm.validate_syntax(fixed_code)
            
            if is_valid:
                print("✅ 语法修复功能正常工作")
                verification_results['enhanced_llm_fixes'] = True
            else:
                print("❌ 语法修复功能有问题")
        except Exception as e:
            print(f"❌ 语法修复测试失败: {e}")
            
    except Exception as e:
        print(f"❌ 增强版LLM导入失败: {e}")
    
    # 2. 验证perfect_tongyi_integration的修复
    try:
        if 'perfect_tongyi_integration' in sys.modules:
            importlib.reload(sys.modules['perfect_tongyi_integration'])
        
        # 检查源代码中是否包含我们的修复
        import perfect_tongyi_integration
        source_code = inspect.getsource(perfect_tongyi_integration)
        
        # 检查关键修复是否存在
        key_fixes = [
            "cleaned_code",  # 代码清理机制
            "# 已在执行环境中提供",  # 导入清理标记
            "result['plotly_code'] = cleaned_code"  # 代码保存修复
        ]
        
        fixes_found = []
        for fix in key_fixes:
            if fix in source_code:
                fixes_found.append(fix)
                print(f"✅ 找到修复: {fix}")
            else:
                print(f"❌ 缺少修复: {fix}")
        
        verification_results['perfect_integration_fixes'] = len(fixes_found) >= 2
        verification_results['code_cleaning_fixes'] = "cleaned_code" in source_code
        
    except Exception as e:
        print(f"❌ perfect_tongyi_integration检查失败: {e}")
    
    # 3. 验证模块版本和时间戳
    try:
        import os
        import time
        
        files_to_check = [
            'enhanced_tongyi_integration.py',
            'perfect_tongyi_integration.py'
        ]
        
        print(f"\n📁 文件修改时间检查:")
        for file_path in files_to_check:
            if os.path.exists(file_path):
                mtime = os.path.getmtime(file_path)
                mtime_str = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(mtime))
                print(f"  {file_path}: {mtime_str}")
            else:
                print(f"  ❌ 文件不存在: {file_path}")
                
    except Exception as e:
        print(f"❌ 文件时间戳检查失败: {e}")
    
    # 4. 总结验证结果
    print(f"\n🎯 验证结果总结:")
    print("=" * 30)
    
    total_checks = len(verification_results)
    passed_checks = sum(verification_results.values())
    
    for check_name, result in verification_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {check_name}: {status}")
    
    success_rate = (passed_checks / total_checks) * 100
    print(f"\n总体通过率: {passed_checks}/{total_checks} ({success_rate:.1f}%)")
    
    if success_rate >= 75:
        print("\n✅ 修复已正确加载，可以重启Streamlit服务")
        return True
    else:
        print("\n❌ 修复加载不完整，需要检查文件")
        return False

def check_import_conflicts():
    """检查导入冲突"""
    print(f"\n🔍 检查导入冲突")
    print("=" * 30)
    
    try:
        # 测试导入
        import enhanced_tongyi_integration
        import perfect_tongyi_integration
        
        print("✅ 所有模块导入成功")
        
        # 检查是否有循环导入
        import sys
        modules = [name for name in sys.modules.keys() if 'tongyi' in name]
        print(f"已加载的相关模块: {modules}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入冲突检测到: {e}")
        return False

def generate_restart_script():
    """生成重启脚本"""
    print(f"\n📝 生成重启脚本")
    print("=" * 30)
    
    restart_script = """#!/bin/bash
# Streamlit服务重启脚本

echo "🔄 开始重启Streamlit服务..."

# 1. 停止现有服务
echo "1️⃣ 停止现有Streamlit进程..."
pkill -f streamlit 2>/dev/null || echo "没有找到运行中的Streamlit进程"

# 2. 清理Python缓存
echo "2️⃣ 清理Python缓存..."
find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
find . -name "*.pyc" -delete 2>/dev/null || true

# 3. 等待进程完全停止
echo "3️⃣ 等待进程完全停止..."
sleep 2

# 4. 重启Streamlit服务
echo "4️⃣ 重启Streamlit服务..."
streamlit run streamlit_app.py --server.port 8501 --server.address 0.0.0.0

echo "✅ Streamlit服务重启完成"
"""
    
    # Windows版本
    restart_script_windows = """@echo off
REM Streamlit服务重启脚本 (Windows)

echo 🔄 开始重启Streamlit服务...

REM 1. 停止现有服务
echo 1️⃣ 停止现有Streamlit进程...
taskkill /f /im python.exe 2>nul || echo 没有找到运行中的Python进程

REM 2. 清理Python缓存
echo 2️⃣ 清理Python缓存...
for /d /r . %%d in (__pycache__) do @if exist "%%d" rd /s /q "%%d"
del /s /q *.pyc 2>nul

REM 3. 等待进程完全停止
echo 3️⃣ 等待进程完全停止...
timeout /t 2 /nobreak >nul

REM 4. 重启Streamlit服务
echo 4️⃣ 重启Streamlit服务...
streamlit run streamlit_app.py --server.port 8501 --server.address 0.0.0.0

echo ✅ Streamlit服务重启完成
"""
    
    try:
        with open('restart_streamlit.sh', 'w', encoding='utf-8') as f:
            f.write(restart_script)
        print("✅ 生成Linux/Mac重启脚本: restart_streamlit.sh")
        
        with open('restart_streamlit.bat', 'w', encoding='utf-8') as f:
            f.write(restart_script_windows)
        print("✅ 生成Windows重启脚本: restart_streamlit.bat")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成重启脚本失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Streamlit服务重启验证工具")
    print("=" * 60)
    
    # 1. 验证修复是否加载
    fixes_loaded = verify_fixes_loaded()
    
    # 2. 检查导入冲突
    no_conflicts = check_import_conflicts()
    
    # 3. 生成重启脚本
    scripts_generated = generate_restart_script()
    
    # 4. 提供重启建议
    print(f"\n🎯 重启建议")
    print("=" * 30)
    
    if fixes_loaded and no_conflicts:
        print("✅ 所有修复已正确加载，建议立即重启Streamlit服务")
        print("\n重启步骤:")
        print("1. 按Ctrl+C停止当前Streamlit服务")
        print("2. 运行: python verify_fixes_loaded.py")
        print("3. 清理缓存: find . -name '__pycache__' -exec rm -rf {} +")
        print("4. 重启服务: streamlit run streamlit_app.py")
        print("5. 清理浏览器缓存并刷新页面")
    else:
        print("❌ 发现问题，建议先修复后再重启")
        if not fixes_loaded:
            print("  - 修复代码加载不完整")
        if not no_conflicts:
            print("  - 存在导入冲突")
    
    print(f"\n📋 下一步操作:")
    print("1. 如果验证通过，请重启Streamlit服务")
    print("2. 重启后运行图表查询测试")
    print("3. 检查控制台是否还有警告信息")
    print("4. 如果问题持续，请运行详细调试脚本")
