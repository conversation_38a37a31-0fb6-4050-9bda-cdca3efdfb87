#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试连续查询修复
验证DataFrame副本修复是否解决了连续查询问题
"""

import pandas as pd
import os
from perfect_tongyi_integration import analyze_data

def test_dataframe_immutability():
    """测试DataFrame不可变性"""
    print("🧪 测试DataFrame不可变性")
    print("=" * 50)
    
    # 加载原始数据
    original_df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 原始数据形状: {original_df.shape}")
    print(f"📋 原始列名: {list(original_df.columns)}")
    
    # 保存原始数据的副本用于比较
    original_copy = original_df.copy()
    
    # 执行第一次查询
    print("\n1️⃣ 执行第一次查询")
    result1 = analyze_data(original_df, "分析2024年各产品的销售额", table_name="sales_data")
    
    print(f"📊 查询后数据形状: {original_df.shape}")
    print(f"📋 查询后列名: {list(original_df.columns)}")
    
    # 检查原始DataFrame是否被修改
    if original_df.equals(original_copy):
        print("✅ 原始DataFrame未被修改")
    else:
        print("❌ 原始DataFrame被修改了")
        print("修改的列:")
        for col in original_df.columns:
            if col not in original_copy.columns:
                print(f"  + 新增列: {col}")
    
    # 执行第二次查询
    print("\n2️⃣ 执行第二次查询")
    result2 = analyze_data(original_df, "分析2024年销售最高的产品", table_name="sales_data")
    
    print(f"📊 第二次查询后数据形状: {original_df.shape}")
    print(f"📋 第二次查询后列名: {list(original_df.columns)}")
    
    # 再次检查原始DataFrame
    if original_df.equals(original_copy):
        print("✅ 原始DataFrame仍未被修改")
        return True
    else:
        print("❌ 原始DataFrame在第二次查询后被修改")
        return False

def test_consecutive_queries_success():
    """测试连续查询成功率"""
    print("\n🔄 测试连续查询成功率")
    print("=" * 50)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    queries = [
        "分析2024年各产品的销售额",
        "分析2024年销售最高的产品", 
        "计算各产品的平均销售金额",
        "显示销售数量最多的产品",
        "分析各销售区域的总销售额"
    ]
    
    success_count = 0
    total_queries = len(queries)
    
    for i, query in enumerate(queries, 1):
        print(f"\n{i}️⃣ 查询: {query}")
        try:
            result = analyze_data(df, query, table_name="sales_data")
            if result and result.get('success'):
                print(f"✅ 查询 {i} 成功")
                success_count += 1
            else:
                print(f"❌ 查询 {i} 失败")
                if result:
                    print(f"   错误: {result.get('error', '未知错误')}")
        except Exception as e:
            print(f"❌ 查询 {i} 异常: {e}")
    
    success_rate = (success_count / total_queries) * 100
    print(f"\n📊 连续查询成功率: {success_count}/{total_queries} ({success_rate:.1f}%)")
    
    return success_rate >= 80  # 80%以上成功率认为修复成功

def main():
    """主测试函数"""
    print("🎯 连续查询修复验证测试")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists('uploaded_files/sales_data.csv'):
        print("❌ 测试数据文件不存在")
        return
    
    # 测试1: DataFrame不可变性
    immutability_test = test_dataframe_immutability()
    
    # 测试2: 连续查询成功率
    success_rate_test = test_consecutive_queries_success()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 测试结果总结")
    print("=" * 60)
    
    if immutability_test:
        print("✅ DataFrame不可变性测试: 通过")
    else:
        print("❌ DataFrame不可变性测试: 失败")
    
    if success_rate_test:
        print("✅ 连续查询成功率测试: 通过")
    else:
        print("❌ 连续查询成功率测试: 失败")
    
    if immutability_test and success_rate_test:
        print("\n🎉 所有测试通过！连续查询问题已修复")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    main()
