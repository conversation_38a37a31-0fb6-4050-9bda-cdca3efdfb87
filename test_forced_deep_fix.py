#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试强制深度修复效果
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_forced_deep_fix():
    """测试强制深度修复"""
    print("🚀 测试强制深度修复效果")
    print("=" * 60)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro'],
        '销售额': [25500.0, 20200.0, 15000.0, 9700.0]
    })
    
    print("测试数据:")
    print(df)
    print()
    
    # 测试查询
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 40)
    
    try:
        result = analyze_data(df, query, "forced_test", use_metadata=True)
        
        if result.get('success'):
            print("✅ 分析成功")
            
            # 检查生成的代码
            code = result.get('code', '')
            
            print("生成的代码:")
            print(code)
            print()
            
            # 检查深度修复特征
            deep_fix_features = {
                '数据清理': 'replace([np.inf, -np.inf]' in code,
                '类型转换': 'pd.to_numeric' in code,
                '索引处理': 'duplicated().any()' in code,
                '正则清理': 're.sub(' in code,
                '安全渲染': 'use_container_width=True' in code,
                '数据验证': 'chart_data.empty' in code,
                '导入numpy': 'import numpy as np' in code,
                '导入re': 'import re' in code
            }
            
            print("🔍 深度修复特征检查:")
            for feature, present in deep_fix_features.items():
                status = "✅" if present else "❌"
                print(f"  {status} {feature}")
            
            # 计算修复覆盖率
            present_count = sum(deep_fix_features.values())
            total_features = len(deep_fix_features)
            coverage = (present_count / total_features) * 100
            
            print(f"\n📊 深度修复覆盖率: {present_count}/{total_features} ({coverage:.1f}%)")
            
            if coverage >= 75:
                print("🎉 深度修复已成功应用！")
                return True
            elif coverage >= 50:
                print("⚠️ 深度修复部分应用")
                return False
            else:
                print("❌ 深度修复未应用")
                return False
                
        else:
            print(f"❌ 分析失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_chart_types():
    """测试多种图表类型的深度修复"""
    print("\n🧪 测试多种图表类型的深度修复")
    print("=" * 50)
    
    df = pd.DataFrame({
        '产品名称': ['iPhone', 'iPad', 'MacBook'],
        '销售额': [25500.0, 20200.0, 15000.0],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03']
    })
    
    test_cases = [
        ("请分析各产品销售额，用柱状图展示", "bar"),
        ("生成销售额的折线图", "line"),
        ("创建产品销售趋势图", "line")
    ]
    
    results = []
    
    for query, expected_type in test_cases:
        print(f"\n📋 测试: {query}")
        print(f"期望类型: {expected_type}")
        print("-" * 30)
        
        try:
            result = analyze_data(df, query, f"test_{expected_type}", use_metadata=True)
            
            if result.get('success'):
                code = result.get('code', '')
                
                # 检查是否使用了深度修复
                has_deep_fix = any([
                    'import numpy as np' in code,
                    'replace([np.inf, -np.inf]' in code,
                    'pd.to_numeric' in code,
                    'use_container_width=True' in code
                ])
                
                # 检查图表类型
                if expected_type == 'bar':
                    correct_type = 'st.bar_chart' in code
                elif expected_type == 'line':
                    correct_type = 'st.line_chart' in code
                else:
                    correct_type = True
                
                print(f"深度修复: {'✅' if has_deep_fix else '❌'}")
                print(f"正确类型: {'✅' if correct_type else '❌'}")
                
                results.append({
                    'query': query,
                    'has_deep_fix': has_deep_fix,
                    'correct_type': correct_type,
                    'success': True
                })
            else:
                print(f"❌ 失败: {result.get('error')}")
                results.append({
                    'query': query,
                    'has_deep_fix': False,
                    'correct_type': False,
                    'success': False
                })
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            results.append({
                'query': query,
                'has_deep_fix': False,
                'correct_type': False,
                'success': False
            })
    
    # 统计结果
    successful = [r for r in results if r['success']]
    deep_fix_count = sum(1 for r in successful if r['has_deep_fix'])
    correct_type_count = sum(1 for r in successful if r['correct_type'])
    
    print(f"\n📊 多类型测试结果:")
    print(f"成功率: {len(successful)}/{len(results)} ({len(successful)/len(results)*100:.1f}%)")
    if successful:
        print(f"深度修复率: {deep_fix_count}/{len(successful)} ({deep_fix_count/len(successful)*100:.1f}%)")
        print(f"类型正确率: {correct_type_count}/{len(successful)} ({correct_type_count/len(successful)*100:.1f}%)")
    
    return len(successful) > 0 and deep_fix_count > 0

if __name__ == "__main__":
    print("🔧 强制深度修复测试工具")
    print("=" * 60)
    
    # 1. 主要测试
    main_success = test_forced_deep_fix()
    
    # 2. 多类型测试
    multi_success = test_multiple_chart_types()
    
    # 3. 总结
    print(f"\n🎯 测试总结")
    print("=" * 30)
    
    if main_success and multi_success:
        print("🎉 强制深度修复成功！")
        print("现在可以重启Streamlit服务，图表闪退问题应该得到根本解决。")
        
        print(f"\n📋 重启步骤:")
        print("1. 停止Streamlit服务: Ctrl+C")
        print("2. 清理缓存: find . -name '__pycache__' -exec rm -rf {} +")
        print("3. 重启服务: streamlit run streamlit_app.py")
        print("4. 清理浏览器缓存: Ctrl+Shift+R")
        print("5. 测试查询: '请分析各产品销售额，用柱状图展示'")
        
        print(f"\n🎯 预期效果:")
        print("✅ 图表稳定显示，不再闪退")
        print("✅ 控制台无 'Infinite extent' 警告")
        print("✅ 控制台无 'Scale bindings' 警告")
        print("✅ 数据处理更安全可靠")
        
    elif main_success or multi_success:
        print("⚠️ 强制深度修复部分成功")
        print("建议重启Streamlit服务测试效果")
        
    else:
        print("❌ 强制深度修复未成功")
        print("可能需要检查代码修改是否正确保存")
    
    print(f"\n💡 技术说明:")
    print("这次修复通过强制AI使用我们的深度修复模板，")
    print("确保生成的代码包含完整的Vega-Lite渲染优化，")
    print("从根本上解决图表闪退和控制台警告问题。")
