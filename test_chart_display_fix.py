#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图表显示修复效果
"""

import streamlit as st
import pandas as pd
import sys
import os

# 添加当前目录到路径
sys.path.append('.')

try:
    from perfect_tongyi_integration import analyze_data
    from result_formatter import EnhancedResultFormatter
    
    st.title("🔧 图表显示修复测试")
    
    # 创建测试数据
    data = {
        '地区': ['北京', '上海', '广州', '深圳'],
        '销售额': [23400, 13980, 15300, 20700]
    }
    df = pd.DataFrame(data)
    
    # 将数据保存到session_state供图表显示使用
    st.session_state.current_data = df
    
    st.subheader("📊 测试数据")
    st.dataframe(df)
    
    if st.button("🚀 测试饼图显示修复"):
        with st.spinner("正在生成图表..."):
            result = analyze_data(df, '请分析2024年各地区的销售总额，生成饼图显示占比', 'sales_data', use_metadata=True)
            
            if result and result.get('success'):
                st.success("✅ 分析完成！")
                
                # 显示结果状态
                st.subheader("📊 结果状态")
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("成功", "✅" if result.get('success') else "❌")
                
                with col2:
                    st.metric("Plotly原生", "✅" if result.get('uses_plotly_native') else "❌")
                
                with col3:
                    st.metric("有图表", "✅" if result.get('has_chart') else "❌")
                
                # 显示生成的代码
                if result.get('code'):
                    with st.expander("📝 生成的代码", expanded=False):
                        st.code(result['code'], language='python')
                
                # 使用修复后的格式化器显示结果
                if result.get('output'):
                    st.subheader("📋 分析结果")
                    EnhancedResultFormatter.format_and_display_result(result)
                
                # 验证修复效果
                if result.get('uses_plotly_native') and result.get('has_chart'):
                    st.success("🎉 修复成功！Plotly图表应该正确显示了")
                    st.info("💡 图表通过_display_plotly_chart()方法在Streamlit上下文中重新执行显示")
                else:
                    st.warning("⚠️ 可能仍有问题，请检查配置")
            
            else:
                st.error("❌ 分析失败")
                if result:
                    st.error(f"错误: {result.get('error', '未知错误')}")
    
    st.markdown("---")
    st.markdown("### 📖 修复说明")
    st.markdown("""
    **修复的问题**：
    1. **原问题**：Plotly图表代码在后端执行时调用st.plotly_chart()，但不在Streamlit上下文中，导致图表无法显示
    2. **修复方案**：保存完整的Plotly代码，在前端Streamlit上下文中重新执行图表显示部分
    3. **预期效果**：现在应该能看到正确的交互式Plotly饼图
    
    **技术细节**：
    - 后端执行时设置 `has_chart = True` 和 `uses_plotly_native = True`
    - 保存完整代码到 `plotly_code` 字段
    - 前端调用 `_display_plotly_chart()` 方法重新执行图表显示
    """)

except ImportError as e:
    st.error(f"❌ 导入失败: {e}")
    st.error("请确保所有依赖都已正确安装")
