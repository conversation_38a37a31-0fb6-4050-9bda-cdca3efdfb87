#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的元数据清理脚本
删除所有测试表格，只保留用户真正上传的表格
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

try:
    from metadata_manager import metadata_manager
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def main():
    """主函数"""
    print("🚀 开始清理元数据中的测试表格")
    print("=" * 50)
    
    try:
        # 显示清理前状态
        before_tables = metadata_manager.get_all_tables()
        print(f"📊 清理前表格数: {len(before_tables)}")
        print(f"📋 清理前表格列表:")
        for table in before_tables:
            print(f"  - {table}")
        
        # 执行清理
        print(f"\n🧹 执行清理操作...")
        result = metadata_manager.cleanup_test_tables()
        
        # 显示清理结果
        print(f"\n📊 清理结果:")
        print(f"  成功删除: {result['deleted']} 个表格")
        print(f"  剩余表格: {result['remaining']} 个表格")
        
        if result['deleted_tables']:
            print(f"\n🗑️ 已删除的表格:")
            for table in result['deleted_tables']:
                print(f"  - {table}")
        
        # 显示清理后状态
        after_tables = metadata_manager.get_all_tables()
        if after_tables:
            print(f"\n✅ 剩余表格:")
            for table in after_tables:
                table_metadata = metadata_manager.get_table_metadata(table)
                if table_metadata:
                    print(f"  - {table} (业务领域: {table_metadata.business_domain})")
        else:
            print(f"\n✅ 所有测试表格已清理完毕，元数据存储现在是干净的")
        
        print(f"\n🎉 清理完成！")
        print(f"  - 删除了 {result['deleted']} 个测试表格")
        print(f"  - 保留了 {result['remaining']} 个用户表格")
        print(f"  - 元数据存储已优化")
        
        if result['remaining'] == 0:
            print(f"\n💡 下一步建议:")
            print(f"  1. 上传您的真实数据文件（CSV/Excel）")
            print(f"  2. 系统会自动为新数据创建元数据")
            print(f"  3. 配置列的业务含义以提升AI理解")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 清理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎯 清理成功完成！")
        print(f"现在您可以上传真实的数据文件，系统将为其创建干净的元数据。")
    else:
        print(f"\n⚠️ 清理过程中出现问题，请检查错误信息。")
