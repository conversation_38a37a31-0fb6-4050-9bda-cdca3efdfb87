#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit缓存清理脚本
在Streamlit应用中运行以清理元数据缓存
"""

import streamlit as st
from metadata_manager import metadata_manager

def clear_metadata_cache():
    """清理元数据缓存"""
    st.write("🔄 正在清理元数据缓存...")
    
    try:
        # 清空内存缓存
        metadata_manager.tables_metadata.clear()
        
        # 重新加载配置
        metadata_manager._load_configurations()
        
        # 获取最新的表格列表
        tables = metadata_manager.get_all_tables()
        
        st.success(f"✅ 元数据缓存已清理，当前表格数量: {len(tables)}")
        st.write("📊 当前表格列表:")
        for table in tables:
            st.write(f"  - {table}")
        
        return True
        
    except Exception as e:
        st.error(f"❌ 清理失败: {e}")
        return False

if __name__ == "__main__":
    st.title("🔄 元数据缓存清理工具")
    
    if st.button("清理元数据缓存"):
        clear_metadata_cache()
    
    st.write("---")
    st.write("💡 使用说明:")
    st.write("1. 点击上方按钮清理元数据缓存")
    st.write("2. 清理完成后，返回主页面")
    st.write("3. 检查元数据管理页面是否已更新")
