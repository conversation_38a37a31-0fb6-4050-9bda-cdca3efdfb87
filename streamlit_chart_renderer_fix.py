#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit图表渲染修复器
基于Streamlit技术文档的深度修复方案
"""

import pandas as pd
import numpy as np
import streamlit as st
import re
from typing import Any, Dict, Optional, Union

class StreamlitChartRenderer:
    """
    专门针对Streamlit图表渲染问题的修复器
    解决Vega-Lite渲染引擎的数据格式冲突
    """
    
    @staticmethod
    def sanitize_column_names(data: Union[pd.DataFrame, pd.Series]) -> Union[pd.DataFrame, pd.Series]:
        """
        清理列名，避免Vega-Lite渲染冲突
        """
        if isinstance(data, pd.Series):
            # 清理Series的name和index
            if data.name:
                # 移除可能导致冲突的字符
                clean_name = re.sub(r'[^\w\u4e00-\u9fff]', '_', str(data.name))
                data.name = clean_name
            
            # 清理index名称
            if hasattr(data.index, 'name') and data.index.name:
                clean_index_name = re.sub(r'[^\w\u4e00-\u9fff]', '_', str(data.index.name))
                data.index.name = clean_index_name
            
            return data
        
        elif isinstance(data, pd.DataFrame):
            # 清理DataFrame列名
            clean_columns = []
            for col in data.columns:
                clean_col = re.sub(r'[^\w\u4e00-\u9fff]', '_', str(col))
                clean_columns.append(clean_col)
            
            data.columns = clean_columns
            return data
        
        return data
    
    @staticmethod
    def prepare_bar_chart_data(data: Union[pd.DataFrame, pd.Series, dict]) -> pd.Series:
        """
        为st.bar_chart准备正确格式的数据
        """
        print("🔧 准备柱状图数据...")
        
        if isinstance(data, dict):
            # 从字典创建Series
            series = pd.Series(data)
        elif isinstance(data, pd.DataFrame):
            # 从DataFrame提取数据
            if len(data.columns) >= 2:
                # 假设第一列是标签，第二列是数值
                series = data.set_index(data.columns[0])[data.columns[1]]
            else:
                # 单列DataFrame
                series = data.iloc[:, 0]
        elif isinstance(data, pd.Series):
            series = data.copy()
        else:
            raise ValueError(f"不支持的数据类型: {type(data)}")
        
        # 数据清理
        series = StreamlitChartRenderer.clean_chart_data(series)
        
        # 清理名称
        series = StreamlitChartRenderer.sanitize_column_names(series)
        
        print(f"✅ 数据准备完成，形状: {series.shape}")
        print(f"数据类型: {series.dtype}")
        print(f"数据范围: {series.min()} - {series.max()}")
        
        return series
    
    @staticmethod
    def clean_chart_data(data: pd.Series) -> pd.Series:
        """
        清理图表数据，移除可能导致渲染问题的值
        """
        print("🧹 清理图表数据...")
        
        # 1. 处理无穷大值
        data = data.replace([np.inf, -np.inf], np.nan)
        
        # 2. 处理NaN值
        data = data.fillna(0)
        
        # 3. 确保数据类型为数值
        try:
            data = pd.to_numeric(data, errors='coerce').fillna(0)
        except Exception as e:
            print(f"⚠️ 数值转换警告: {e}")
        
        # 4. 处理过大或过小的值
        max_val = data.max()
        min_val = data.min()
        
        if max_val > 1e15:  # 处理过大的值
            print("⚠️ 检测到过大的数值，进行缩放")
            data = data / 1e6  # 转换为百万单位
        
        if abs(min_val) > 1e15:  # 处理过小的值
            print("⚠️ 检测到过小的数值，进行处理")
            data = data.clip(lower=-1e12)
        
        # 5. 确保索引唯一
        if data.index.duplicated().any():
            print("⚠️ 检测到重复索引，进行处理")
            data = data.groupby(data.index).sum()
        
        return data
    
    @staticmethod
    def safe_bar_chart(data: Union[pd.DataFrame, pd.Series, dict], 
                      title: str = None,
                      x_label: str = None,
                      y_label: str = None) -> bool:
        """
        安全的柱状图渲染方法
        """
        try:
            # 准备数据
            chart_data = StreamlitChartRenderer.prepare_bar_chart_data(data)
            
            # 验证数据
            if chart_data.empty:
                st.warning("📊 数据为空，无法生成图表")
                return False
            
            if chart_data.sum() == 0:
                st.info("📊 所有数值为0，无法生成有意义的图表")
                return False
            
            # 显示标题
            if title:
                st.subheader(title)
            
            # 使用Streamlit原生方法渲染
            st.bar_chart(
                chart_data,
                use_container_width=True
            )
            
            # 显示数据摘要
            with st.expander("📋 数据详情"):
                st.write("**数据摘要:**")
                st.write(f"- 总计: {chart_data.sum():,.2f}")
                st.write(f"- 平均值: {chart_data.mean():,.2f}")
                st.write(f"- 最大值: {chart_data.max():,.2f}")
                st.write(f"- 最小值: {chart_data.min():,.2f}")
                
                st.write("**详细数据:**")
                for idx, val in chart_data.items():
                    st.write(f"• {idx}: {val:,.2f}")
            
            return True
            
        except Exception as e:
            st.error(f"❌ 图表渲染失败: {e}")
            print(f"图表渲染错误详情: {e}")
            
            # 显示备用信息
            try:
                st.write("**数据预览:**")
                if isinstance(data, (pd.DataFrame, pd.Series)):
                    st.dataframe(data)
                else:
                    st.json(data)
            except:
                st.write("无法显示数据预览")
            
            return False
    
    @staticmethod
    def generate_streamlit_chart_code(data_var: str = "df", 
                                    group_col: str = "产品名称", 
                                    value_col: str = "销售额") -> str:
        """
        生成安全的Streamlit图表代码
        """
        code = f"""# 使用安全的Streamlit图表渲染
import numpy as np
import pandas as pd

# 数据处理
chart_data = {data_var}.groupby('{group_col}')['{value_col}'].sum()

# 数据清理
chart_data = chart_data.replace([np.inf, -np.inf], np.nan)
chart_data = chart_data.fillna(0)
chart_data = pd.to_numeric(chart_data, errors='coerce').fillna(0)

# 处理重复索引
if chart_data.index.duplicated().any():
    chart_data = chart_data.groupby(chart_data.index).sum()

# 排序
chart_data = chart_data.sort_values(ascending=False)

# 验证数据
print("图表数据:")
print(chart_data)
print(f"数据类型: {{chart_data.dtype}}")
print(f"数据范围: {{chart_data.min()}} - {{chart_data.max()}}")

# 渲染图表
if not chart_data.empty and chart_data.sum() != 0:
    st.subheader("📊 {value_col}分析")
    st.bar_chart(chart_data, use_container_width=True)
    
    # 显示详细信息
    st.write("**详细数据:**")
    for item, value in chart_data.items():
        st.write(f"• {{item}}: {{value:,.0f}}")
else:
    st.warning("数据无法生成图表")
"""
        return code

def test_chart_renderer():
    """测试图表渲染器"""
    st.title("🔧 Streamlit图表渲染器测试")
    
    # 创建测试数据
    test_data = {
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500.0, 20200.0, 15000.0, 9700.0, 8000.0]
    }
    df = pd.DataFrame(test_data)
    
    st.subheader("📋 测试数据")
    st.dataframe(df)
    
    # 测试1：从DataFrame生成图表
    st.subheader("🧪 测试1：从DataFrame生成图表")
    success1 = StreamlitChartRenderer.safe_bar_chart(
        df, 
        title="📊 产品销售额分析",
        x_label="产品名称",
        y_label="销售额"
    )
    
    # 测试2：从Series生成图表
    st.subheader("🧪 测试2：从Series生成图表")
    series_data = df.set_index('产品名称')['销售额']
    success2 = StreamlitChartRenderer.safe_bar_chart(
        series_data,
        title="📊 产品销售额分析（Series）"
    )
    
    # 测试3：从字典生成图表
    st.subheader("🧪 测试3：从字典生成图表")
    dict_data = dict(zip(df['产品名称'], df['销售额']))
    success3 = StreamlitChartRenderer.safe_bar_chart(
        dict_data,
        title="📊 产品销售额分析（字典）"
    )
    
    # 显示生成的代码
    st.subheader("📝 生成的安全代码")
    code = StreamlitChartRenderer.generate_streamlit_chart_code()
    st.code(code, language='python')
    
    # 测试结果
    st.subheader("🎯 测试结果")
    results = [success1, success2, success3]
    success_count = sum(results)
    
    if success_count == 3:
        st.success("✅ 所有测试通过！图表渲染器工作正常")
    elif success_count >= 2:
        st.warning(f"⚠️ {success_count}/3 测试通过，部分功能正常")
    else:
        st.error("❌ 多数测试失败，需要进一步调试")

if __name__ == "__main__":
    test_chart_renderer()
