#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控LLM代码生成过程
追踪销售额_start和销售额_end字段的动态创建
"""

import pandas as pd
import numpy as np
import re
from enhanced_tongyi_integration import EnhancedTongyiQianwenLLM

def test_llm_code_generation():
    """测试LLM代码生成过程"""
    print("🔍 测试LLM代码生成过程")
    print("=" * 50)
    
    # 加载正常的sales_data
    df = pd.read_csv("uploaded_files/sales_data.csv")
    print(f"📊 原始数据形状: {df.shape}")
    print(f"📋 原始列名: {list(df.columns)}")
    
    # 检查原始数据是否包含问题字段
    problematic_fields = ['销售额_start', '销售额_end']
    for field in problematic_fields:
        if field in df.columns:
            print(f"❌ 原始数据包含问题字段: {field}")
            return
    
    print("✅ 原始数据正常")
    
    # 创建LLM实例
    llm = EnhancedTongyiQianwenLLM()
    llm.set_current_data("sales_data", df)
    
    # 测试不同类型的查询
    test_queries = [
        "分析各产品销售额，生成柱状图",
        "显示销售趋势图表",
        "创建产品对比可视化",
        "统计各地区销售情况",
        "分析销售员业绩"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n🧪 测试查询 {i}: {query}")
        print("-" * 40)
        
        try:
            # 生成代码
            code = llm.call(query, df.to_string())
            
            print("📝 生成的代码:")
            print(code)
            print()
            
            # 检查生成的代码是否包含问题字段
            if '销售额_start' in code or '销售额_end' in code:
                print("❌ 生成的代码包含问题字段！")
                print("🔍 问题代码片段:")
                for line in code.split('\n'):
                    if '销售额_start' in line or '销售额_end' in line:
                        print(f"  {line}")
                return True
            else:
                print("✅ 生成的代码正常")
            
            # 模拟执行代码（不实际执行，只检查语法）
            try:
                compile(code, '<string>', 'exec')
                print("✅ 代码语法正确")
            except SyntaxError as e:
                print(f"⚠️ 代码语法错误: {e}")
                
        except Exception as e:
            print(f"❌ 代码生成失败: {e}")
    
    return False

def analyze_prompt_content():
    """分析提示词内容"""
    print("\n🔍 分析提示词内容")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv("uploaded_files/sales_data.csv")
    
    # 创建LLM实例
    llm = EnhancedTongyiQianwenLLM()
    llm.set_current_data("sales_data", df)
    
    # 构建提示词
    query = "分析各产品销售额，生成柱状图"
    value = df.to_string()
    
    # 获取增强提示词
    enhanced_prompt = llm._build_enhanced_prompt(query, value, should_chart=True)
    
    print("📝 增强提示词内容:")
    print("=" * 30)
    print(enhanced_prompt)
    print("=" * 30)
    
    # 检查提示词是否包含问题字段
    if '销售额_start' in enhanced_prompt or '销售额_end' in enhanced_prompt:
        print("❌ 提示词包含问题字段！")
        
        # 查找具体位置
        lines = enhanced_prompt.split('\n')
        for i, line in enumerate(lines, 1):
            if '销售额_start' in line or '销售额_end' in line:
                print(f"  第{i}行: {line}")
        return True
    else:
        print("✅ 提示词正常")
        return False

def check_metadata_context():
    """检查元数据上下文"""
    print("\n🔍 检查元数据上下文")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv("uploaded_files/sales_data.csv")
    
    # 创建LLM实例
    llm = EnhancedTongyiQianwenLLM()
    llm.set_current_data("sales_data", df)
    
    # 获取元数据上下文
    metadata_context = llm._get_metadata_context()
    
    if metadata_context:
        print("📝 元数据上下文:")
        print("=" * 30)
        print(metadata_context)
        print("=" * 30)
        
        # 检查元数据上下文是否包含问题字段
        if '销售额_start' in metadata_context or '销售额_end' in metadata_context:
            print("❌ 元数据上下文包含问题字段！")
            
            # 查找具体位置
            lines = metadata_context.split('\n')
            for i, line in enumerate(lines, 1):
                if '销售额_start' in line or '销售额_end' in line:
                    print(f"  第{i}行: {line}")
            return True
        else:
            print("✅ 元数据上下文正常")
            return False
    else:
        print("⚠️ 无元数据上下文")
        return False

def simulate_code_execution():
    """模拟代码执行过程"""
    print("\n🔍 模拟代码执行过程")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv("uploaded_files/sales_data.csv")
    print(f"📊 执行前数据形状: {df.shape}")
    print(f"📋 执行前列名: {list(df.columns)}")
    
    # 生成代码
    llm = EnhancedTongyiQianwenLLM()
    llm.set_current_data("sales_data", df)
    
    query = "分析各产品销售额，生成柱状图"
    code = llm.call(query, df.to_string())
    
    print("📝 将要执行的代码:")
    print("-" * 30)
    print(code)
    print("-" * 30)
    
    # 创建一个安全的执行环境
    safe_globals = {
        'pd': pd,
        'np': np,
        'df': df.copy(),  # 使用副本
        'print': print,
        'st': None  # 模拟streamlit
    }
    
    # 模拟streamlit组件
    class MockStreamlit:
        @staticmethod
        def subheader(text):
            print(f"[ST] 子标题: {text}")
        
        @staticmethod
        def bar_chart(data, **kwargs):
            print(f"[ST] 柱状图数据: {type(data)}")
            if hasattr(data, 'index'):
                print(f"[ST] 索引: {list(data.index)}")
            if hasattr(data, 'values'):
                print(f"[ST] 值: {list(data.values)}")
        
        @staticmethod
        def container():
            return MockStreamlit()
        
        def __enter__(self):
            return self
        
        def __exit__(self, *args):
            pass
        
        @staticmethod
        def write(text):
            print(f"[ST] 写入: {text}")
        
        @staticmethod
        def dataframe(data):
            print(f"[ST] 数据框: {data}")
        
        @staticmethod
        def warning(text):
            print(f"[ST] 警告: {text}")
        
        @staticmethod
        def error(text):
            print(f"[ST] 错误: {text}")
        
        @staticmethod
        def expander(text):
            return MockStreamlit()
    
    safe_globals['st'] = MockStreamlit()
    
    try:
        # 执行代码
        print("🚀 开始执行代码...")
        exec(code, safe_globals)
        
        # 检查执行后的数据
        executed_df = safe_globals['df']
        print(f"\n📊 执行后数据形状: {executed_df.shape}")
        print(f"📋 执行后列名: {list(executed_df.columns)}")
        
        # 检查是否产生了问题字段
        problematic_fields = ['销售额_start', '销售额_end']
        for field in problematic_fields:
            if field in executed_df.columns:
                print(f"❌ 执行后产生了问题字段: {field}")
                field_data = executed_df[field]
                print(f"   数据类型: {field_data.dtype}")
                print(f"   示例值: {field_data.head().tolist()}")
                print(f"   无穷大值: {np.isinf(field_data).sum()}")
                return True
        
        print("✅ 执行后数据正常")
        return False
        
    except Exception as e:
        print(f"❌ 代码执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 监控LLM代码生成过程")
    print("=" * 60)
    
    issues_found = []
    
    # 1. 测试代码生成
    if test_llm_code_generation():
        issues_found.append("LLM生成的代码包含问题字段")
    
    # 2. 分析提示词
    if analyze_prompt_content():
        issues_found.append("提示词包含问题字段")
    
    # 3. 检查元数据上下文
    if check_metadata_context():
        issues_found.append("元数据上下文包含问题字段")
    
    # 4. 模拟执行
    if simulate_code_execution():
        issues_found.append("代码执行过程中产生问题字段")
    
    print(f"\n📊 监控结果:")
    if issues_found:
        print("❌ 发现问题:")
        for issue in issues_found:
            print(f"  - {issue}")
        
        print(f"\n💡 建议:")
        print("1. 检查LLM的提示词模板")
        print("2. 检查元数据系统是否还有残留")
        print("3. 检查代码生成逻辑")
        print("4. 可能需要重新训练或调整LLM参数")
    else:
        print("✅ 未发现明显问题")
        print("💡 问题可能在运行时动态产生，建议在Streamlit应用中实时监控")

if __name__ == "__main__":
    main()
