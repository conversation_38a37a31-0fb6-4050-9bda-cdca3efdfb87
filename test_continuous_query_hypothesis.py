#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试连续查询假设的实验
验证是否连续提问导致第二次回答出现问题
"""

import pandas as pd
from perfect_tongyi_integration import TongyiQianwenLLM
import time

def create_test_data():
    """创建测试数据"""
    return pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'] * 4,
        '地区': ['北京', '上海', '广州', '深圳'] * 5,
        '销售额': [8000, 4500, 15000, 1800, 3200] * 4,
        '销量': [120, 80, 40, 150, 100] * 4,
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05'] * 4
    })

def test_syntax_validity(code):
    """测试代码语法是否正确"""
    try:
        compile(code, '<string>', 'exec')
        return True, None
    except SyntaxError as e:
        return False, str(e)

def experiment_1_isolated_queries():
    """实验1：独立执行查询（无连续上下文）"""
    print("🧪 实验1：独立执行查询")
    print("=" * 50)
    
    llm = TongyiQianwenLLM()
    df = create_test_data()
    df_info = df.to_string()
    
    queries = [
        "分析2024年各产品销售情况",
        "请分析各产品销售额，按照柱状图展示",
        "生成各地区销售额的饼图",
        "创建产品销量的条形图"
    ]
    
    results = {}
    
    for i, query in enumerate(queries, 1):
        print(f"\n📋 查询 {i}: {query}")
        
        # 创建新的LLM实例，确保无状态
        fresh_llm = TongyiQianwenLLM()
        
        try:
            code = fresh_llm.call(query, df_info)
            is_valid, error = test_syntax_validity(code)
            
            results[query] = {
                'code': code,
                'valid': is_valid,
                'error': error
            }
            
            if is_valid:
                print("✅ 代码语法正确")
            else:
                print(f"❌ 语法错误: {error}")
                
        except Exception as e:
            print(f"❌ 调用失败: {e}")
            results[query] = {'valid': False, 'error': str(e)}
        
        # 等待一下，避免API限制
        time.sleep(1)
    
    return results

def experiment_2_repeated_complex_query():
    """实验2：重复执行复杂查询"""
    print("\n🧪 实验2：重复执行复杂查询")
    print("=" * 50)
    
    df = create_test_data()
    df_info = df.to_string()
    
    complex_query = "请分析各产品销售额，按照柱状图展示"
    success_count = 0
    total_attempts = 5
    
    for i in range(total_attempts):
        print(f"\n📋 尝试 {i+1}: {complex_query}")
        
        # 每次创建新的LLM实例
        llm = TongyiQianwenLLM()
        
        try:
            code = llm.call(complex_query, df_info)
            is_valid, error = test_syntax_validity(code)
            
            if is_valid:
                print("✅ 代码语法正确")
                success_count += 1
            else:
                print(f"❌ 语法错误: {error}")
                # 显示有问题的代码片段
                lines = code.split('\n')
                for j, line in enumerate(lines, 1):
                    if 'for ' in line and line.endswith(':'):
                        print(f"  行 {j}: {line}")
                        if j < len(lines):
                            print(f"  行 {j+1}: {lines[j]}")
                        break
                
        except Exception as e:
            print(f"❌ 调用失败: {e}")
        
        time.sleep(1)
    
    success_rate = success_count / total_attempts * 100
    print(f"\n📊 成功率: {success_count}/{total_attempts} ({success_rate:.1f}%)")
    
    return success_rate

def experiment_3_simple_vs_complex():
    """实验3：简单查询 vs 复杂查询的成功率对比"""
    print("\n🧪 实验3：简单查询 vs 复杂查询对比")
    print("=" * 50)
    
    df = create_test_data()
    df_info = df.to_string()
    
    simple_queries = [
        "计算总销售额",
        "显示前5行数据",
        "计算平均销量"
    ]
    
    complex_queries = [
        "请分析各产品销售额，按照柱状图展示",
        "生成各地区销售额的饼图，并添加数据标签",
        "创建产品销量的条形图，包含数值标注"
    ]
    
    def test_query_set(queries, query_type):
        success_count = 0
        total_count = len(queries)
        
        for query in queries:
            llm = TongyiQianwenLLM()
            try:
                code = llm.call(query, df_info)
                is_valid, error = test_syntax_validity(code)
                if is_valid:
                    success_count += 1
                    print(f"✅ {query}")
                else:
                    print(f"❌ {query} - {error}")
            except Exception as e:
                print(f"❌ {query} - {e}")
            
            time.sleep(0.5)
        
        success_rate = success_count / total_count * 100
        print(f"\n📊 {query_type}成功率: {success_count}/{total_count} ({success_rate:.1f}%)")
        return success_rate
    
    simple_rate = test_query_set(simple_queries, "简单查询")
    complex_rate = test_query_set(complex_queries, "复杂查询")
    
    return simple_rate, complex_rate

def main():
    """主函数"""
    print("🔬 连续查询问题根因分析实验")
    print("=" * 60)
    
    # 实验1：独立查询
    results1 = experiment_1_isolated_queries()
    
    # 实验2：重复复杂查询
    success_rate = experiment_2_repeated_complex_query()
    
    # 实验3：简单vs复杂查询对比
    simple_rate, complex_rate = experiment_3_simple_vs_complex()
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 实验结论")
    print("=" * 60)
    
    print(f"1. 复杂查询重复执行成功率: {success_rate:.1f}%")
    print(f"2. 简单查询成功率: {simple_rate:.1f}%")
    print(f"3. 复杂查询成功率: {complex_rate:.1f}%")
    
    if complex_rate < simple_rate:
        print("\n✅ 验证了假设：问题与查询复杂度相关，而非连续提问")
    else:
        print("\n❓ 需要进一步分析")
    
    print("\n💡 关键发现：")
    print("- 每次LLM调用都是独立的，无上下文传递")
    print("- 复杂图表生成代码更容易出现缩进问题")
    print("- 问题是概率性的，与AI模型的固有限制相关")

if __name__ == "__main__":
    main()
