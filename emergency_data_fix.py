#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
紧急数据修复脚本
立即清理所有可能导致图表闪退的数据源
"""

import pandas as pd
import numpy as np
import re
import os
from pathlib import Path

def emergency_clean_uploaded_files():
    """紧急清理所有上传的文件"""
    print("🚨 紧急清理上传文件...")
    
    upload_dir = Path("uploaded_files")
    if not upload_dir.exists():
        print("📁 上传目录不存在")
        return
    
    files_cleaned = 0
    
    for file_path in upload_dir.glob("*"):
        if file_path.suffix.lower() in ['.csv', '.xlsx', '.xls']:
            try:
                print(f"🧹 清理文件: {file_path.name}")
                
                # 加载文件
                if file_path.suffix.lower() == '.csv':
                    df = pd.read_csv(file_path, encoding='utf-8')
                else:
                    df = pd.read_excel(file_path)
                
                # 检查是否需要清理
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                has_inf = np.isinf(df[numeric_cols]).sum().sum() > 0
                has_nan = df[numeric_cols].isnull().sum().sum() > 0
                has_problematic_cols = any('销售额_start' in str(col) or '销售额_end' in str(col) for col in df.columns)
                
                if has_inf or has_nan or has_problematic_cols:
                    print(f"  发现问题: 无穷大值={has_inf}, NaN值={has_nan}, 问题列={has_problematic_cols}")
                    
                    # 清理数据
                    cleaned_df = clean_dataframe_emergency(df)
                    
                    # 备份原文件
                    backup_path = file_path.with_suffix(f'.backup{file_path.suffix}')
                    file_path.rename(backup_path)
                    print(f"  原文件备份为: {backup_path.name}")
                    
                    # 保存清理后的文件
                    if file_path.suffix.lower() == '.csv':
                        cleaned_df.to_csv(file_path, index=False, encoding='utf-8')
                    else:
                        cleaned_df.to_excel(file_path, index=False)
                    
                    print(f"  ✅ 文件已清理并保存")
                    files_cleaned += 1
                else:
                    print(f"  ✅ 文件无需清理")
                    
            except Exception as e:
                print(f"  ❌ 清理失败: {e}")
    
    print(f"\n📊 清理完成: {files_cleaned} 个文件已清理")

def clean_dataframe_emergency(df):
    """紧急清理DataFrame"""
    if df is None or df.empty:
        return df
    
    # 创建副本
    cleaned_df = df.copy()
    
    # 1. 清理列名
    original_columns = list(cleaned_df.columns)
    cleaned_df.columns = [re.sub(r'[^\w\u4e00-\u9fff]', '_', str(col)) for col in cleaned_df.columns]
    
    # 2. 处理数值列
    numeric_cols = cleaned_df.select_dtypes(include=[np.number]).columns
    
    for col in numeric_cols:
        # 替换无穷大值
        cleaned_df[col] = cleaned_df[col].replace([np.inf, -np.inf], np.nan)
        # 填充NaN值
        cleaned_df[col] = cleaned_df[col].fillna(0)
        # 确保数据类型
        cleaned_df[col] = pd.to_numeric(cleaned_df[col], errors='coerce').fillna(0)
    
    # 3. 处理重复索引
    if cleaned_df.index.duplicated().any():
        cleaned_df = cleaned_df.reset_index(drop=True)
    
    return cleaned_df

def check_current_data_issues():
    """检查当前数据问题"""
    print("🔍 检查当前数据问题...")
    
    upload_dir = Path("uploaded_files")
    if not upload_dir.exists():
        print("📁 上传目录不存在")
        return
    
    total_files = 0
    problematic_files = 0
    
    for file_path in upload_dir.glob("*"):
        if file_path.suffix.lower() in ['.csv', '.xlsx', '.xls']:
            total_files += 1
            
            try:
                # 加载文件
                if file_path.suffix.lower() == '.csv':
                    df = pd.read_csv(file_path, encoding='utf-8')
                else:
                    df = pd.read_excel(file_path)
                
                # 检查问题
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                inf_count = np.isinf(df[numeric_cols]).sum().sum()
                nan_count = df[numeric_cols].isnull().sum().sum()
                
                problematic_cols = [col for col in df.columns if '销售额_start' in str(col) or '销售额_end' in str(col)]
                
                if inf_count > 0 or nan_count > 0 or problematic_cols:
                    problematic_files += 1
                    print(f"❌ {file_path.name}:")
                    print(f"   无穷大值: {inf_count}")
                    print(f"   NaN值: {nan_count}")
                    print(f"   问题列: {problematic_cols}")
                    
                    # 检查具体的问题列
                    for col in problematic_cols:
                        if col in df.columns:
                            col_inf = np.isinf(df[col]).sum()
                            col_nan = df[col].isnull().sum()
                            print(f"     {col}: 无穷大值={col_inf}, NaN值={col_nan}")
                else:
                    print(f"✅ {file_path.name}: 无问题")
                    
            except Exception as e:
                print(f"❌ {file_path.name}: 检查失败 - {e}")
    
    print(f"\n📊 检查结果: {problematic_files}/{total_files} 文件有问题")
    return problematic_files > 0

def create_test_clean_data():
    """创建测试用的清理数据"""
    print("🧪 创建测试清理数据...")
    
    # 创建包含问题的测试数据
    test_data = {
        '产品名称@#$': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro'],
        '销售额_start': [6999000, np.inf, 14999000, 1899000],
        '销售额_end': [7999000, 5599000, -np.inf, 2899000],
        '销售额': [7499000, np.nan, 15499000, 2399000],
        '销量': [1200, 800, 400, 1500],
        '价格': [6999, 4599, 14999, 1899]
    }
    
    df = pd.DataFrame(test_data)
    
    # 保存问题数据
    upload_dir = Path("uploaded_files")
    upload_dir.mkdir(exist_ok=True)
    
    problem_file = upload_dir / "test_problematic_data.csv"
    df.to_csv(problem_file, index=False, encoding='utf-8')
    print(f"📁 问题数据已保存: {problem_file}")
    
    # 清理数据
    cleaned_df = clean_dataframe_emergency(df)
    
    # 保存清理后的数据
    clean_file = upload_dir / "test_cleaned_data.csv"
    cleaned_df.to_csv(clean_file, index=False, encoding='utf-8')
    print(f"📁 清理数据已保存: {clean_file}")
    
    print("\n对比:")
    print("问题数据:")
    print(df.head())
    print(f"无穷大值: {np.isinf(df.select_dtypes(include=[np.number])).sum().sum()}")
    print(f"NaN值: {df.isnull().sum().sum()}")
    
    print("\n清理后数据:")
    print(cleaned_df.head())
    print(f"无穷大值: {np.isinf(cleaned_df.select_dtypes(include=[np.number])).sum().sum()}")
    print(f"NaN值: {cleaned_df.isnull().sum().sum()}")

def main():
    """主函数"""
    print("🚨 紧急数据修复工具")
    print("=" * 60)
    
    # 检查当前问题
    has_issues = check_current_data_issues()
    
    if has_issues:
        print("\n🔧 执行紧急清理...")
        emergency_clean_uploaded_files()
        
        print("\n🔍 重新检查...")
        check_current_data_issues()
    else:
        print("\n✅ 当前数据无问题")
    
    # 创建测试数据
    print("\n🧪 创建测试数据...")
    create_test_clean_data()
    
    print("\n💡 建议:")
    print("1. 重启Streamlit应用以加载清理后的数据")
    print("2. 测试图表生成功能")
    print("3. 检查控制台是否还有Vega-Lite警告")
    print("4. 如果问题仍然存在，请检查session_state中的数据")

if __name__ == "__main__":
    main()
