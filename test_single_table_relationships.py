#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单表关系配置优化功能
验证无关联列的关系配置是否正常工作
"""

import pandas as pd
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

try:
    from metadata_manager import metadata_manager
    from metadata_ui import MetadataUI
    print("✅ 模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_single_column_suggestions():
    """测试单列建议功能"""
    print("🔍 测试单列关系建议功能")
    print("=" * 50)
    
    # 典型的单表列名（很多列是独立的）
    single_table_columns = [
        '客户编号', '客户名称', '联系电话', '邮箱地址', 
        '注册日期', '客户等级', '所在地区', '行业类型',
        '最后登录时间', '账户状态', '信用评级'
    ]
    
    print(f"📊 单表列名: {single_table_columns}")
    
    suggestions = MetadataUI._analyze_column_relationships(single_table_columns)
    
    print(f"💡 智能建议数量: {len(suggestions)}")
    
    # 分类显示建议
    single_col_suggestions = [s for s in suggestions if not s['targets']]
    multi_col_suggestions = [s for s in suggestions if s['targets']]
    
    print(f"\n🔸 单列属性建议 ({len(single_col_suggestions)} 个):")
    for i, suggestion in enumerate(single_col_suggestions, 1):
        print(f"  {i}. {suggestion['source']}")
        print(f"     类型: {suggestion['type']}")
        print(f"     描述: {suggestion['description']}")
        print(f"     关联列: 无需配置 ✅")
        print()
    
    print(f"🔗 多列关系建议 ({len(multi_col_suggestions)} 个):")
    for i, suggestion in enumerate(multi_col_suggestions, 1):
        print(f"  {i}. {suggestion['source']} → {', '.join(suggestion['targets'])}")
        print(f"     类型: {suggestion['type']}")
        print(f"     描述: {suggestion['description']}")
        print()

def test_relationship_conversion_without_targets():
    """测试无关联列的关系配置转换"""
    print("🔄 测试无关联列的关系配置转换")
    print("=" * 50)
    
    # 模拟混合关系配置（有些有关联列，有些没有）
    mixed_relationships_config = [
        {
            "source_column": "客户编号",
            "target_columns": [],  # 无关联列
            "relationship_type": "标识关系",
            "description": "唯一标识，用于客户识别和数据关联"
        },
        {
            "source_column": "所在地区",
            "target_columns": [],  # 无关联列
            "relationship_type": "层级关系",
            "description": "地理维度，用于区域分析和地域统计"
        },
        {
            "source_column": "销售额",
            "target_columns": ["单价", "销量"],  # 有关联列
            "relationship_type": "计算关系",
            "description": "销售额 = 单价 × 销量"
        },
        {
            "source_column": "客户等级",
            "target_columns": [],  # 无关联列
            "relationship_type": "分类关系",
            "description": "客户分级维度，用于客户分层分析"
        }
    ]
    
    print("📋 原始混合配置:")
    for i, config in enumerate(mixed_relationships_config, 1):
        targets_info = f"关联列: {', '.join(config['target_columns'])}" if config['target_columns'] else "关联列: 无"
        print(f"  {i}. 源列: {config['source_column']}")
        print(f"     {targets_info}")
        print(f"     类型: {config['relationship_type']}")
        print(f"     描述: {config['description']}")
        print()
    
    # 转换为兼容格式
    converted_relationships = {}
    for config in mixed_relationships_config:
        source = config["source_column"]
        targets = config["target_columns"]
        rel_type = config["relationship_type"]
        description = config["description"]
        
        if source and description:
            if targets:
                # 有关联列：源列_目标列1_目标列2
                rel_key = f"{source}_{'_'.join(targets)}"
            else:
                # 无关联列：仅源列
                rel_key = source
            
            rel_value = f"[{rel_type}] {description}"
            converted_relationships[rel_key] = rel_value
    
    print("🔄 转换后的兼容格式:")
    for key, value in converted_relationships.items():
        key_type = "多列关系" if "_" in key else "单列属性"
        print(f"  {key}: {value}")
        print(f"    类型: {key_type}")
        print()

def test_relationship_types_requirements():
    """测试不同关系类型对关联列的要求"""
    print("📋 测试关系类型的关联列要求")
    print("=" * 50)
    
    relationship_types = [
        "计算关系",    # 需要关联列
        "分组关系",    # 可选关联列
        "约束关系",    # 需要关联列
        "依赖关系",    # 需要关联列
        "层级关系",    # 可选关联列
        "时间关系",    # 可选关联列
        "业务关系",    # 可选关联列
        "标识关系",    # 可选关联列
        "分类关系"     # 可选关联列
    ]
    
    # 需要关联列的类型
    needs_targets = ["计算关系", "约束关系", "依赖关系"]
    
    print("关系类型分析:")
    for rel_type in relationship_types:
        requirement = "必需关联列" if rel_type in needs_targets else "可选关联列"
        icon = "🔗" if rel_type in needs_targets else "🔸"
        print(f"  {icon} {rel_type}: {requirement}")
    
    print(f"\n💡 设计理念:")
    print(f"  - 计算关系: 需要明确的计算公式，必须有关联列")
    print(f"  - 约束关系: 需要明确的约束对象，必须有关联列")
    print(f"  - 依赖关系: 需要明确的依赖对象，必须有关联列")
    print(f"  - 其他关系: 主要描述列的属性特征，关联列可选")

def create_single_table_test_data():
    """创建单表测试数据并配置关系"""
    print("\n📊 创建单表测试数据")
    print("=" * 50)
    
    # 创建客户信息表（典型的单表，大部分列独立）
    customer_data = pd.DataFrame({
        '客户编号': ['C001', 'C002', 'C003', 'C004'],
        '客户名称': ['张三公司', '李四企业', '王五集团', '赵六有限'],
        '联系电话': ['138****1234', '139****5678', '136****9012', '137****3456'],
        '所在地区': ['北京', '上海', '广州', '深圳'],
        '行业类型': ['制造业', '服务业', '零售业', '科技业'],
        '客户等级': ['VIP', '普通', 'VIP', '高级'],
        '注册日期': ['2023-01-15', '2023-03-20', '2023-05-10', '2023-07-08'],
        '账户状态': ['正常', '正常', '冻结', '正常'],
        '信用评级': ['AAA', 'AA', 'A', 'AAA']
    })
    
    table_name = "customer_info_single_table"
    
    print(f"📝 注册表格: {table_name}")
    print(f"列名: {list(customer_data.columns)}")
    
    # 注册表格
    metadata_manager.register_table(table_name, customer_data, use_smart_inference=True)
    
    # 模拟配置混合关系（单列属性 + 少量多列关系）
    relationships_config = [
        {
            "source_column": "客户编号",
            "target_columns": [],
            "relationship_type": "标识关系",
            "description": "客户唯一标识，用于数据关联和查找"
        },
        {
            "source_column": "所在地区",
            "target_columns": [],
            "relationship_type": "层级关系",
            "description": "地理维度，用于区域分析和客户分布统计"
        },
        {
            "source_column": "客户等级",
            "target_columns": [],
            "relationship_type": "分类关系",
            "description": "客户分级维度，用于客户价值分层分析"
        },
        {
            "source_column": "注册日期",
            "target_columns": [],
            "relationship_type": "时间关系",
            "description": "时间维度，用于客户增长趋势分析"
        }
    ]
    
    # 转换并保存关系
    new_relationships = {}
    for config in relationships_config:
        source = config["source_column"]
        targets = config["target_columns"]
        rel_type = config["relationship_type"]
        description = config["description"]
        
        rel_key = source if not targets else f"{source}_{'_'.join(targets)}"
        rel_value = f"[{rel_type}] {description}"
        new_relationships[rel_key] = rel_value
    
    # 更新表格元数据
    table_metadata = metadata_manager.get_table_metadata(table_name)
    table_metadata.relationships = new_relationships
    metadata_manager._save_configurations()
    
    print(f"\n🔗 配置的关系:")
    for key, value in new_relationships.items():
        rel_type = "单列属性" if "_" not in key else "多列关系"
        print(f"  {key}: {value}")
        print(f"    类型: {rel_type}")
    
    # 生成LLM上下文验证
    context = metadata_manager.generate_llm_context(table_name, customer_data)
    
    print(f"\n📄 生成的LLM上下文片段:")
    context_lines = context.split('\n')
    relationship_section = False
    
    for line in context_lines:
        if "列间关系:" in line:
            relationship_section = True
        
        if relationship_section:
            print(line)
            if line.strip() == "" and relationship_section:
                break

def main():
    """主测试函数"""
    print("🚀 开始测试单表关系配置优化功能")
    print("=" * 60)
    
    try:
        # 1. 测试单列建议功能
        test_single_column_suggestions()
        
        # 2. 测试无关联列的关系转换
        test_relationship_conversion_without_targets()
        
        # 3. 测试关系类型要求
        test_relationship_types_requirements()
        
        # 4. 创建单表测试数据
        create_single_table_test_data()
        
        print("\n" + "=" * 60)
        print("🎉 单表关系配置优化功能测试完成！")
        
        print("\n✅ 测试结果:")
        print("- 单列属性建议功能正常")
        print("- 无关联列的关系转换正常")
        print("- 关系类型要求区分正确")
        print("- 单表数据配置验证通过")
        
        print("\n🎯 优化效果:")
        print("- 单表场景更友好，无需强制配置关联列")
        print("- 智能区分不同关系类型的要求")
        print("- 提供单列属性的智能建议")
        print("- 保持多列关系的完整功能")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
