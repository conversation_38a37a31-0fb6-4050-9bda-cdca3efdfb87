#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多列显示修复效果
"""

import sys
import os
import pandas as pd

# 添加当前目录到路径，以便导入本地模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_multicolumn_display():
    """测试多列显示修复效果"""
    print("🧪 测试多列显示修复效果")
    print("=" * 50)
    
    # 模拟后端生成的正确DataFrame输出格式（4列：索引+销售区域+产品名称+销售金额）
    test_output = """  销售区域   产品名称  销售金额
0   上海   台式电脑  6200
1   北京  笔记本电脑  8500
2   广州   平板电脑  3200
3   杭州     耳机   450
4   深圳     手机  2800"""
    
    # 模拟result对象
    test_result = {
        'query': '分析2024年各地区的各个产品销售收入情况',
        'output': test_output,
        'success': True
    }
    
    print("📊 测试数据:")
    print(test_output)
    print("\n" + "=" * 50)
    
    try:
        # 导入修复后的结果格式化器
        from result_formatter import EnhancedResultFormatter
        
        print("✅ 成功导入EnhancedResultFormatter")
        
        # 测试输出类型检测
        output_type = EnhancedResultFormatter._detect_output_type(test_output)
        print(f"🔍 检测到的输出类型: {output_type}")
        
        # 测试数据解析
        if output_type == 'series_data':
            print("\n📋 测试多列数据解析逻辑:")
            
            # 手动调用解析逻辑进行测试
            lines = test_output.strip().split('\n')
            data_dict = {}
            full_data_rows = []  # 保存完整的多列数据
            header_line = None
            header_columns = []
            
            for i, line in enumerate(lines):
                line = line.strip()
                if line and not line.startswith('Name:') and not line.startswith('dtype:'):
                    # 检测并保存表头行
                    if i == 0 and any(header in line for header in ['产品名称', '地区', '类别', '销售员', '销售区域']):
                        header_line = line
                        header_columns = line.split()
                        print(f"📝 检测到表头: {header_columns}")
                        continue

                    # 检查是否是DataFrame格式（索引 + 多列数据）
                    parts = line.split()
                    if len(parts) >= 3 and parts[0].isdigit():
                        print(f"🔍 解析数据行: {parts}")
                        
                        # 动态解析多列数据
                        if header_columns and len(parts) >= len(header_columns):
                            # 根据表头确定列的含义
                            row_data = {}
                            for j, col_name in enumerate(header_columns):
                                if j + 1 < len(parts):  # +1 因为要跳过索引列
                                    row_data[col_name] = parts[j + 1]
                            
                            print(f"📊 行数据映射: {row_data}")
                            
                            # 保存完整行数据
                            full_data_rows.append(row_data)
            
            print(f"\n📊 完整数据行数: {len(full_data_rows)}")
            print(f"📊 表头列数: {len(header_columns)}")
            
            # 验证完整数据
            if full_data_rows and header_columns:
                print("✅ 成功解析多列数据！")
                
                # 创建完整的多列DataFrame
                df = pd.DataFrame(full_data_rows)
                print(f"📋 创建的DataFrame形状: {df.shape}")
                print(f"📋 DataFrame列名: {list(df.columns)}")
                print("📋 DataFrame内容:")
                print(df.to_string(index=False))
                
                # 验证期望结果
                expected_columns = ['销售区域', '产品名称', '销售金额']
                expected_data = [
                    {'销售区域': '上海', '产品名称': '台式电脑', '销售金额': '6200'},
                    {'销售区域': '北京', '产品名称': '笔记本电脑', '销售金额': '8500'},
                    {'销售区域': '广州', '产品名称': '平板电脑', '销售金额': '3200'},
                    {'销售区域': '杭州', '产品名称': '耳机', '销售金额': '450'},
                    {'销售区域': '深圳', '产品名称': '手机', '销售金额': '2800'}
                ]
                
                # 检查列名
                if list(df.columns) == expected_columns:
                    print("✅ 列名匹配正确！")
                else:
                    print(f"❌ 列名不匹配！实际: {list(df.columns)}, 期望: {expected_columns}")
                    return False
                
                # 检查数据内容
                if len(df) == len(expected_data):
                    print("✅ 数据行数正确！")
                    
                    # 检查每行数据
                    all_correct = True
                    for i, (_, row) in enumerate(df.iterrows()):
                        expected_row = expected_data[i]
                        for col in expected_columns:
                            if str(row[col]) != str(expected_row[col]):
                                print(f"❌ 第{i}行{col}列不匹配！实际: {row[col]}, 期望: {expected_row[col]}")
                                all_correct = False
                    
                    if all_correct:
                        print("✅ 所有数据内容匹配正确！")
                        print("🎉 多列显示修复测试通过！")
                        return True
                    else:
                        print("❌ 部分数据内容不匹配！")
                        return False
                else:
                    print(f"❌ 数据行数不匹配！实际: {len(df)}, 期望: {len(expected_data)}")
                    return False
            else:
                print("❌ 未能解析出完整的多列数据！")
                return False
        else:
            print(f"⚠️  输出类型不是series_data，而是: {output_type}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔄 测试向后兼容性")
    print("=" * 50)
    
    # 测试传统的2列格式
    test_output_2col = """产品名称 销售额
台式电脑 6200
笔记本电脑 8500
平板电脑 3200"""
    
    test_result_2col = {
        'query': '各产品销售额',
        'output': test_output_2col,
        'success': True
    }
    
    try:
        from result_formatter import EnhancedResultFormatter
        
        output_type = EnhancedResultFormatter._detect_output_type(test_output_2col)
        print(f"🔍 2列格式检测类型: {output_type}")
        
        if output_type in ['series_data', 'tabular_data']:
            print("✅ 向后兼容性测试通过！")
            return True
        else:
            print(f"⚠️  向后兼容性可能有问题，检测类型: {output_type}")
            return False
            
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔧 多列显示修复测试")
    print("=" * 60)
    
    # 测试1: 多列显示
    test1_result = test_multicolumn_display()
    
    # 测试2: 向后兼容性
    test2_result = test_backward_compatibility()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"✅ 多列显示测试: {'通过' if test1_result else '失败'}")
    print(f"✅ 向后兼容测试: {'通过' if test2_result else '失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 所有测试通过！多列显示问题已成功修复！")
        print("现在前端应该能正确显示完整的3列数据：销售区域、产品名称、销售金额")
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
