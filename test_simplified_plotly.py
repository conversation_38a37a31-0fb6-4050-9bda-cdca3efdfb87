#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的Plotly原生图表功能
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_simplified_plotly_pie():
    """测试简化后的Plotly饼图"""
    print("🥧 测试简化后的Plotly饼图功能")
    print("=" * 50)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据: {df.shape}")
    
    # 测试饼图查询
    query = "请为我生成销售金额分布的饼图"
    print(f"🔍 查询: {query}")
    
    try:
        result = analyze_data(df, query, table_name="sales_data")
        
        if result and result.get('success'):
            print("✅ 查询成功")
            
            code = result.get('code', '')
            output = result.get('output', '')
            has_chart = result.get('has_chart', False)
            
            print(f"\n📝 生成的代码:")
            print(code)
            
            # 分析代码特征
            uses_plotly = 'plotly' in code.lower() or 'px.pie' in code
            uses_streamlit_native = 'st.plotly_chart' in code
            has_save_chart = 'save_chart()' in code
            has_legend = 'legend' in code.lower()
            
            print(f"\n🔍 代码特征分析:")
            print(f"  📊 使用Plotly: {'✅' if uses_plotly else '❌'}")
            print(f"  🎨 Streamlit原生显示: {'✅' if uses_streamlit_native else '❌'}")
            print(f"  💾 包含save_chart(): {'❌ (不需要)' if not has_save_chart else '⚠️ (多余)'}")
            print(f"  🏷️ 包含图例设置: {'✅' if has_legend else '❌'}")
            
            print(f"\n📊 执行输出:")
            print(output if output.strip() else "(无输出)")
            
            print(f"\n📈 图表生成标志: {has_chart}")
            
            # 评估简化效果
            if uses_plotly and uses_streamlit_native and not has_save_chart:
                print("\n🎉 完美！简化成功：")
                print("  ✅ 使用Plotly原生图表")
                print("  ✅ 直接通过st.plotly_chart()显示")
                print("  ✅ 无重复的保存功能")
                print("  ✅ 完全原生Streamlit风格")
            elif uses_plotly and uses_streamlit_native:
                print("\n⚠️ 基本正确，但可能有多余功能")
            else:
                print("\n❌ 需要进一步优化")
                
        else:
            print("❌ 查询失败")
            if result:
                print(f"错误: {result.get('error', '未知错误')}")
                
    except Exception as e:
        print(f"❌ 查询异常: {e}")
        import traceback
        traceback.print_exc()

def test_different_chart_types():
    """测试不同图表类型的处理"""
    print(f"\n📊 测试不同图表类型的处理")
    print("=" * 50)
    
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    test_cases = [
        ("饼图", "生成销售金额分布的饼图", "应该使用Plotly原生"),
        ("柱状图", "生成销售金额的柱状图", "可以使用matplotlib或Plotly"),
        ("折线图", "生成销售趋势的折线图", "可以使用matplotlib或Plotly")
    ]
    
    for chart_type, query, expected in test_cases:
        print(f"\n📈 {chart_type}测试:")
        print(f"  查询: {query}")
        print(f"  期望: {expected}")
        
        try:
            result = analyze_data(df, query, table_name="sales_data")
            
            if result and result.get('success'):
                code = result.get('code', '')
                
                # 分析图表类型
                if 'px.pie' in code or 'plt.pie' in code:
                    actual_type = "饼图"
                elif 'px.bar' in code or 'plt.bar' in code:
                    actual_type = "柱状图"
                elif 'px.line' in code or 'plt.plot' in code:
                    actual_type = "折线图"
                else:
                    actual_type = "其他"
                
                uses_plotly_native = 'st.plotly_chart' in code
                
                print(f"  实际: {actual_type}")
                print(f"  原生显示: {'✅' if uses_plotly_native else '❌'}")
                
                if chart_type == "饼图" and actual_type == "饼图" and uses_plotly_native:
                    print("  🎉 饼图完美使用原生风格")
                elif actual_type == chart_type:
                    print("  ✅ 图表类型正确")
                else:
                    print("  ⚠️ 图表类型可能不匹配")
                    
            else:
                print("  ❌ 查询失败")
                
        except Exception as e:
            print(f"  ❌ 异常: {e}")

def summarize_optimization():
    """总结优化效果"""
    print(f"\n🎯 优化效果总结")
    print("=" * 50)
    
    print("✅ 已实现的优化:")
    print("  1. 饼图使用Plotly原生显示")
    print("  2. 通过st.plotly_chart()直接集成")
    print("  3. 自动包含图例和交互功能")
    print("  4. 完全符合Streamlit原生风格")
    print("  5. 避免了重复的图表保存功能")
    
    print("\n🚫 已移除的冗余功能:")
    print("  1. 不必要的save_chart()调用")
    print("  2. 重复的matplotlib图表保存")
    print("  3. 额外的图表文件管理")
    
    print("\n🏆 最终效果:")
    print("  - 用户请求饼图 → Plotly原生饼图")
    print("  - 自动显示图例和百分比")
    print("  - 支持交互（悬停、缩放等）")
    print("  - 完美融入Streamlit界面")
    print("  - 无重复功能，代码简洁")

if __name__ == "__main__":
    test_simplified_plotly_pie()
    test_different_chart_types()
    summarize_optimization()
