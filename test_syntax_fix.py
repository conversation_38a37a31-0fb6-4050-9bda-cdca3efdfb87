#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试语法修复功能
"""

import pandas as pd
from enhanced_tongyi_integration import EnhancedTongyiQianwenLLM

def test_syntax_fix():
    """测试语法修复功能"""
    print("🧪 测试语法修复功能")
    print("=" * 60)
    
    # 创建LLM实例
    llm = EnhancedTongyiQianwenLLM()
    
    # 测试有语法错误的代码
    problematic_codes = [
        {
            'name': '缺少闭合括号的Plotly代码',
            'code': '''import streamlit as st
import plotly.express as px
grouped_data = df.groupby('产品名称')['销售额'].sum().reset_index()
fig = px.bar(grouped_data, x='产品名称', y='销售额', title='各产品销售额分布', 
fig.update_layout(xaxis_tickangle=-45)
st.plotly_chart(fig, use_container_width=True)'''
        },
        {
            'name': '多个缺失括号',
            'code': '''import plotly.express as px
fig = px.scatter(df, x='价格', y='销量', title='价格与销量关系'
fig.update_layout(showlegend=False
st.plotly_chart(fig)'''
        },
        {
            'name': '正常的代码',
            'code': '''import streamlit as st
import plotly.express as px
fig = px.bar(df, x='产品', y='销售额', title='销售额对比')
st.plotly_chart(fig, use_container_width=True)'''
        }
    ]
    
    for test_case in problematic_codes:
        print(f"\n📋 测试: {test_case['name']}")
        print("-" * 40)
        
        original_code = test_case['code']
        print("原始代码:")
        print(original_code)
        print()
        
        # 测试语法验证
        is_valid_original = llm.validate_syntax(original_code)
        print(f"原始代码语法: {'✅ 正确' if is_valid_original else '❌ 错误'}")
        
        # 测试语法修复
        fixed_code = llm.fix_syntax_errors(original_code)
        print("修复后的代码:")
        print(fixed_code)
        print()
        
        # 验证修复后的代码
        is_valid_fixed = llm.validate_syntax(fixed_code)
        print(f"修复后代码语法: {'✅ 正确' if is_valid_fixed else '❌ 错误'}")
        
        # 如果修复失败，尝试进一步修复
        if not is_valid_fixed:
            print("尝试进一步修复...")
            repaired_code = llm.attempt_syntax_repair(fixed_code)
            is_valid_repaired = llm.validate_syntax(repaired_code)
            print(f"进一步修复结果: {'✅ 成功' if is_valid_repaired else '❌ 失败'}")
            if is_valid_repaired:
                print("最终修复的代码:")
                print(repaired_code)

def test_real_scenario():
    """测试真实场景"""
    print("\n🧪 测试真实场景")
    print("=" * 40)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500, 20200, 15000, 9700, 8000],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    # 创建LLM实例并设置数据
    llm = EnhancedTongyiQianwenLLM()
    llm.set_current_data("test_data", df)
    
    # 测试查询
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 30)
    
    try:
        # 调用LLM
        result = llm.call(query, df.to_string())
        
        if result:
            print("✅ LLM调用成功")
            print("生成的代码:")
            print(result)
            
            # 验证生成的代码语法
            is_valid = llm.validate_syntax(result)
            print(f"\n生成代码语法: {'✅ 正确' if is_valid else '❌ 错误'}")
            
        else:
            print("❌ LLM调用失败")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_data_analysis_with_fix():
    """测试带语法修复的数据分析"""
    print("\n🧪 测试带语法修复的数据分析")
    print("=" * 40)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro'],
        '销售额': [25500, 20200, 15000]
    })
    
    # 使用analyze_data函数
    from perfect_tongyi_integration import analyze_data
    
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 30)
    
    try:
        result = analyze_data(df, query, "test_data", use_metadata=True)
        
        if result.get('success'):
            print("✅ 数据分析成功")
            
            # 检查结果
            code = result.get('code', '')
            output = result.get('output', '')
            error = result.get('error', '')
            
            print(f"生成代码长度: {len(code)} 字符")
            print(f"输出长度: {len(output)} 字符")
            print(f"错误信息: {error if error else '无'}")
            
            # 检查是否有语法错误
            llm = EnhancedTongyiQianwenLLM()
            is_valid = llm.validate_syntax(code)
            print(f"代码语法: {'✅ 正确' if is_valid else '❌ 错误'}")
            
        else:
            print(f"❌ 数据分析失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

if __name__ == "__main__":
    test_syntax_fix()
    test_real_scenario()
    test_data_analysis_with_fix()
    
    print("\n" + "=" * 60)
    print("🎯 语法修复功能测试总结")
    print("=" * 60)
    print("功能：自动检测和修复AI生成代码中的语法错误")
    print()
    print("修复能力：")
    print("1. ✅ 检测缺失的闭合括号")
    print("2. ✅ 修复Plotly图表代码的常见语法错误")
    print("3. ✅ 验证代码语法正确性")
    print("4. ✅ 多层次修复机制")
    print()
    print("预期效果：")
    print("- 减少因语法错误导致的图表闪退")
    print("- 提高代码执行成功率")
    print("- 改善用户体验")
