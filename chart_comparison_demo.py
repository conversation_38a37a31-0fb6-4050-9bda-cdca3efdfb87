import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt

st.title("📊 图表方法对比演示")

# 创建示例数据
data = {
    "产品名称": ["iPhone 15", "iPad Air", "MacBook Pro", "AirPods Pro", "Apple Watch"],
    "销售额": [8000, 4500, 15000, 1800, 3200]
}
df = pd.DataFrame(data)

st.subheader("📋 数据")
st.dataframe(df)

# 方法1：Streamlit原生（推荐）
st.subheader("✅ 方法1：Streamlit原生柱状图（推荐）")
sales_data = df.set_index("产品名称")["销售额"]
st.bar_chart(sales_data)

st.success("""
**优势：**
- 渲染速度快
- 自动适应容器宽度  
- 内置交互功能
- 不会出现字体问题
- 与Streamlit主题一致
""")

# 方法2：Matplotlib（问题较多）
st.subheader("❌ 方法2：Matplotlib（容易出问题）")
st.warning("""
**问题：**
- 可能出现缩进错误
- 字体显示问题
- 渲染速度慢
- 需要额外的保存和显示步骤
- 在服务器环境中可能无法显示
""")

# 显示matplotlib代码示例（不执行）
st.code("""
# 容易出问题的matplotlib代码
import matplotlib.pyplot as plt
plt.figure(figsize=(12, 8))
ax = sales_data.plot(kind='bar')
for i, v in enumerate(sales_data):  # 这里容易出现缩进错误
ax.text(i, v, f'{v}')  # ❌ 缺少缩进
plt.show()
""", language='python')

st.info("💡 建议：优先使用Streamlit原生图表方法，避免matplotlib的各种问题！")
