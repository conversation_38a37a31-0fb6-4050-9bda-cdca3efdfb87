
import streamlit as st
import pandas as pd
import numpy as np

def monitor_data_changes():
    """监控数据变化"""
    st.write("🔍 实时数据监控")
    
    if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
        df = st.session_state.current_data
        st.write(f"📊 当前数据形状: {df.shape}")
        st.write(f"📋 当前列名: {list(df.columns)}")
        
        problematic_fields = ['销售额_start', '销售额_end']
        for field in problematic_fields:
            if field in df.columns:
                st.error(f"❌ 发现问题字段: {field}")
                field_data = df[field]
                st.write(f"数据类型: {field_data.dtype}")
                st.write(f"无穷大值数量: {np.isinf(field_data).sum()}")
                st.write("示例值:")
                st.write(field_data.head())
            else:
                st.success(f"✅ 未发现字段: {field}")
    else:
        st.info("⚠️ 当前无数据")

if __name__ == "__main__":
    st.title("🔍 数据监控工具")
    monitor_data_changes()
    
    if st.button("刷新监控"):
        st.rerun()
