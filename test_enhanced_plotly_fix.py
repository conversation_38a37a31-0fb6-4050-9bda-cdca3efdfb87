#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强版LLM的Plotly修复
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def test_enhanced_llm_plotly_fix():
    """测试增强版LLM的Plotly饼图修复"""
    print("🔧 测试增强版LLM的Plotly饼图修复")
    print("=" * 60)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"📊 数据: {df.shape}")
    
    # 测试问题查询（之前生成matplotlib的查询）
    problematic_queries = [
        "分析2024年各地区的销售收入",
        "分析各产品的销售情况并用饼图展示",
        "生成销售金额分布的饼图",
        "用饼图显示各区域的销售占比"
    ]
    
    results = []
    
    for i, query in enumerate(problematic_queries, 1):
        print(f"\n{i}️⃣ 测试查询: {query}")
        print("-" * 50)
        
        try:
            # 使用元数据支持（这会调用EnhancedTongyiQianwenLLM）
            result = analyze_data(df, query, table_name="sales_data", use_metadata=True)
            
            if result and result.get('success'):
                code = result.get('code', '')
                output = result.get('output', '')
                uses_plotly_native = result.get('uses_plotly_native', False)
                has_chart = result.get('has_chart', False)
                
                print(f"📝 生成的代码:")
                print(code[:400] + "..." if len(code) > 400 else code)
                
                # 详细分析代码内容
                analysis = analyze_code_content(code)
                
                print(f"\n🔍 代码内容分析:")
                for key, value in analysis.items():
                    print(f"  {key}: {value}")
                
                print(f"\n🏷️ 系统标志:")
                print(f"  uses_plotly_native: {uses_plotly_native}")
                print(f"  has_chart: {has_chart}")
                
                print(f"\n📊 执行输出:")
                print(output[:200] + "..." if len(output) > 200 else output)
                
                # 评估修复效果
                fix_score = evaluate_fix_effectiveness(analysis, uses_plotly_native, has_chart)
                print(f"\n🎯 修复效果评分: {fix_score}/5")
                
                if fix_score >= 4:
                    print("🎉 修复成功！使用了Plotly原生饼图")
                elif fix_score >= 3:
                    print("✅ 部分修复，但仍有改进空间")
                else:
                    print("❌ 修复失败，仍在使用matplotlib")
                
                results.append({
                    'query': query,
                    'analysis': analysis,
                    'fix_score': fix_score,
                    'uses_plotly_native': uses_plotly_native,
                    'has_chart': has_chart
                })
                
            else:
                print("❌ 查询失败")
                if result:
                    print(f"错误: {result.get('error', '未知错误')}")
                
                results.append({
                    'query': query,
                    'analysis': {'failed': True},
                    'fix_score': 0,
                    'uses_plotly_native': False,
                    'has_chart': False
                })
                
        except Exception as e:
            print(f"❌ 查询异常: {e}")
            import traceback
            traceback.print_exc()
            
            results.append({
                'query': query,
                'analysis': {'error': str(e)},
                'fix_score': 0,
                'uses_plotly_native': False,
                'has_chart': False
            })
    
    return results

def analyze_code_content(code):
    """分析代码内容"""
    analysis = {}
    
    # 检查导入
    analysis['📦 导入Plotly'] = 'plotly' in code.lower() or 'px' in code
    analysis['📦 导入Matplotlib'] = 'matplotlib' in code.lower() or 'plt' in code
    
    # 检查图表类型
    analysis['🥧 使用px.pie'] = 'px.pie' in code
    analysis['🥧 使用plt.pie'] = 'plt.pie' in code
    
    # 检查显示方法
    analysis['🎨 使用st.plotly_chart'] = 'st.plotly_chart' in code
    analysis['💾 使用save_chart'] = 'save_chart()' in code
    
    # 检查Plotly特有功能
    analysis['🔧 update_traces'] = 'update_traces' in code
    analysis['🔧 update_layout'] = 'update_layout' in code
    
    # 检查数据处理
    analysis['📊 reset_index'] = 'reset_index()' in code
    analysis['📄 print输出'] = 'print(' in code
    
    return analysis

def evaluate_fix_effectiveness(analysis, uses_plotly_native, has_chart):
    """评估修复效果"""
    score = 0
    
    # 使用Plotly相关功能 (+2分)
    if analysis.get('📦 导入Plotly') and analysis.get('🥧 使用px.pie'):
        score += 2
    
    # 使用st.plotly_chart显示 (+1分)
    if analysis.get('🎨 使用st.plotly_chart'):
        score += 1
    
    # 没有使用matplotlib饼图 (+1分)
    if not analysis.get('🥧 使用plt.pie'):
        score += 1
    
    # 系统正确识别 (+1分)
    if uses_plotly_native and not has_chart:
        score += 1
    
    return score

def analyze_overall_results(results):
    """分析总体结果"""
    print(f"\n📊 总体修复效果分析")
    print("=" * 60)
    
    total_queries = len(results)
    successful_fixes = sum(1 for r in results if r['fix_score'] >= 4)
    partial_fixes = sum(1 for r in results if 3 <= r['fix_score'] < 4)
    failed_fixes = sum(1 for r in results if r['fix_score'] < 3)
    
    avg_score = sum(r['fix_score'] for r in results) / total_queries if total_queries > 0 else 0
    
    print(f"📈 修复统计:")
    print(f"  总查询数: {total_queries}")
    print(f"  完全修复: {successful_fixes} ({successful_fixes/total_queries*100:.1f}%)")
    print(f"  部分修复: {partial_fixes} ({partial_fixes/total_queries*100:.1f}%)")
    print(f"  修复失败: {failed_fixes} ({failed_fixes/total_queries*100:.1f}%)")
    print(f"  平均评分: {avg_score:.1f}/5")
    
    # 技术栈使用分析
    plotly_usage = sum(1 for r in results if r['analysis'].get('🥧 使用px.pie', False))
    matplotlib_usage = sum(1 for r in results if r['analysis'].get('🥧 使用plt.pie', False))
    
    print(f"\n🔧 技术栈使用:")
    print(f"  使用Plotly饼图: {plotly_usage}/{total_queries} ({plotly_usage/total_queries*100:.1f}%)")
    print(f"  使用Matplotlib饼图: {matplotlib_usage}/{total_queries} ({matplotlib_usage/total_queries*100:.1f}%)")
    
    # 判断修复是否成功
    fix_successful = successful_fixes >= total_queries * 0.8  # 80%以上成功
    
    print(f"\n🎯 修复结果:")
    if fix_successful:
        print("🎉 增强版LLM的Plotly模板修复成功！")
        print("✅ 大部分查询现在使用Plotly原生饼图")
        print("✅ 不会再跳转到'AI生成的数据可视化图表'")
    else:
        print("⚠️ 增强版LLM的修复仍需进一步调整")
        print("❌ 部分查询仍在使用matplotlib")
    
    return fix_successful

if __name__ == "__main__":
    results = test_enhanced_llm_plotly_fix()
    fix_successful = analyze_overall_results(results)
    
    if not fix_successful:
        print(f"\n🔧 进一步调试建议:")
        print("1. 检查enhanced_tongyi_integration.py中的提示词顺序")
        print("2. 确认Plotly模板是否在matplotlib规则之前")
        print("3. 验证强制指令是否被正确应用")
        print("4. 考虑移除或弱化matplotlib饼图相关指导")
