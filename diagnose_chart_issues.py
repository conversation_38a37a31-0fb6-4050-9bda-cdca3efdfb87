#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断图表问题
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data

def diagnose_chart_issues():
    """诊断图表问题"""
    print("🔍 诊断图表渲染问题")
    print("=" * 60)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro'],
        '销售额': [25500.0, 20200.0, 15000.0, 9700.0]
    })
    
    print("测试数据:")
    print(df)
    print()
    
    # 测试单一图表类型请求
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("期望结果: 只生成柱状图")
    print("-" * 40)
    
    try:
        result = analyze_data(df, query, "chart_diagnosis", use_metadata=True)
        
        if result.get('success'):
            print("✅ 分析成功")
            
            # 检查生成的代码
            code = result.get('code', '')
            cleaned_code = result.get('plotly_code', '')
            
            print("原始生成代码:")
            print(code)
            print()
            
            print("清理后代码:")
            print(cleaned_code)
            print()
            
            # 分析问题
            issues = analyze_code_issues(code, cleaned_code)
            
            print("🔍 问题分析:")
            for issue, details in issues.items():
                status = "❌" if details['found'] else "✅"
                print(f"  {status} {issue}: {details['description']}")
            
            return issues
            
        else:
            print(f"❌ 分析失败: {result.get('error', '未知错误')}")
            return None
            
    except Exception as e:
        print(f"❌ 诊断异常: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_code_issues(original_code, cleaned_code):
    """分析代码问题"""
    issues = {
        '多图表类型': {
            'found': False,
            'description': '检查是否同时生成多种图表'
        },
        '异常字段名': {
            'found': False,
            'description': '检查是否包含销售额_start等异常字段'
        },
        'Plotly使用': {
            'found': False,
            'description': '检查是否使用了Plotly而非Streamlit原生'
        },
        '数据清理缺失': {
            'found': False,
            'description': '检查数据清理代码是否存在'
        },
        '代码冲突': {
            'found': False,
            'description': '检查是否存在代码逻辑冲突'
        }
    }
    
    # 检查多图表类型
    chart_methods = ['px.pie', 'px.bar', 'st.bar_chart', 'st.line_chart', 'st.plotly_chart']
    found_charts = [method for method in chart_methods if method in original_code or method in cleaned_code]
    
    if len(found_charts) > 1:
        issues['多图表类型']['found'] = True
        issues['多图表类型']['description'] += f" - 发现: {found_charts}"
    
    # 检查异常字段名
    anomalous_fields = ['销售额_start', '销售额_end']
    for field in anomalous_fields:
        if field in original_code or field in cleaned_code:
            issues['异常字段名']['found'] = True
            issues['异常字段名']['description'] += f" - 发现: {field}"
    
    # 检查Plotly使用
    plotly_indicators = ['import plotly', 'px.', 'st.plotly_chart']
    for indicator in plotly_indicators:
        if indicator in original_code or indicator in cleaned_code:
            issues['Plotly使用']['found'] = True
            issues['Plotly使用']['description'] += f" - 发现: {indicator}"
    
    # 检查数据清理
    cleaning_indicators = ['数据清理', 'replace([np.inf', 'fillna(']
    has_cleaning = any(indicator in cleaned_code for indicator in cleaning_indicators)
    if not has_cleaning:
        issues['数据清理缺失']['found'] = True
        issues['数据清理缺失']['description'] += " - 清理代码未找到"
    
    # 检查代码冲突
    if 'st.bar_chart' in cleaned_code and 'px.' in cleaned_code:
        issues['代码冲突']['found'] = True
        issues['代码冲突']['description'] += " - Streamlit和Plotly混用"
    
    return issues

def test_specific_chart_types():
    """测试特定图表类型"""
    print("\n🧪 测试特定图表类型")
    print("=" * 40)
    
    df = pd.DataFrame({
        '产品名称': ['iPhone', 'iPad', 'MacBook'],
        '销售额': [25500.0, 20200.0, 15000.0]
    })
    
    test_cases = [
        ("请用柱状图分析产品销售额", "应该只生成柱状图"),
        ("生成产品销售额的饼图", "应该只生成饼图"),
        ("用折线图显示销售趋势", "应该只生成折线图")
    ]
    
    results = []
    
    for query, expected in test_cases:
        print(f"\n📋 测试: {query}")
        print(f"期望: {expected}")
        print("-" * 20)
        
        try:
            result = analyze_data(df, query, f"test_{len(results)}", use_metadata=True)
            
            if result.get('success'):
                code = result.get('code', '')
                
                # 统计图表类型
                chart_count = {
                    'pie': code.count('px.pie') + code.count('st.pie'),
                    'bar': code.count('px.bar') + code.count('st.bar_chart'),
                    'line': code.count('px.line') + code.count('st.line_chart'),
                    'plotly': code.count('st.plotly_chart')
                }
                
                total_charts = sum(chart_count.values())
                
                print(f"图表统计: {chart_count}")
                print(f"总图表数: {total_charts}")
                
                if total_charts == 1:
                    print("✅ 单一图表类型")
                elif total_charts > 1:
                    print("❌ 多图表类型问题")
                else:
                    print("⚠️ 未检测到图表代码")
                
                results.append({
                    'query': query,
                    'chart_count': chart_count,
                    'total_charts': total_charts,
                    'success': total_charts == 1
                })
            else:
                print(f"❌ 失败: {result.get('error')}")
                results.append({
                    'query': query,
                    'chart_count': {},
                    'total_charts': 0,
                    'success': False
                })
                
        except Exception as e:
            print(f"❌ 异常: {e}")
            results.append({
                'query': query,
                'chart_count': {},
                'total_charts': 0,
                'success': False
            })
    
    return results

if __name__ == "__main__":
    print("🚀 图表问题诊断工具")
    print("=" * 60)
    
    # 1. 主要诊断
    main_issues = diagnose_chart_issues()
    
    # 2. 特定图表类型测试
    chart_type_results = test_specific_chart_types()
    
    # 3. 总结分析
    print(f"\n🎯 诊断总结")
    print("=" * 30)
    
    if main_issues:
        critical_issues = [issue for issue, details in main_issues.items() if details['found']]
        
        if critical_issues:
            print("❌ 发现关键问题:")
            for issue in critical_issues:
                print(f"  • {issue}")
        else:
            print("✅ 未发现明显问题")
    
    # 图表类型测试总结
    if chart_type_results:
        successful_tests = [r for r in chart_type_results if r['success']]
        success_rate = len(successful_tests) / len(chart_type_results) * 100
        
        print(f"\n图表类型测试:")
        print(f"成功率: {len(successful_tests)}/{len(chart_type_results)} ({success_rate:.1f}%)")
        
        if success_rate < 100:
            print("❌ 存在多图表生成问题")
        else:
            print("✅ 图表类型控制正常")
    
    print(f"\n💡 下一步建议:")
    if main_issues and any(details['found'] for details in main_issues.values()):
        print("1. 需要修复检测到的关键问题")
        print("2. 重新设计图表生成逻辑")
        print("3. 强化数据清理机制")
        print("4. 优化Vega-Lite数据格式")
    else:
        print("1. 问题可能在前端渲染层面")
        print("2. 检查浏览器缓存和Streamlit版本")
        print("3. 验证数据传递过程")
    
    print(f"\n📋 建议的修复优先级:")
    print("1. 解决多图表生成问题（最高优先级）")
    print("2. 消除异常字段名")
    print("3. 统一使用Streamlit原生图表")
    print("4. 强化数据清理和验证")
