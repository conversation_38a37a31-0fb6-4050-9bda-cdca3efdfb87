#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试图表生成和结果格式化功能
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data
from result_formatter import ResultFormatter
import os
from pathlib import Path

def test_chart_generation():
    """测试图表生成功能"""
    print("🎨 测试图表生成功能...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '地区': ['北京', '上海', '广州', '深圳', '杭州'],
        '销售额': [1000, 1200, 800, 900, 700],
        '客户数': [50, 60, 40, 45, 35]
    })
    
    # 测试图表生成查询
    chart_queries = [
        "生成销售额的饼图",
        "画出各地区销售额的柱状图",
        "制作销售额和客户数的对比图表"
    ]
    
    for query in chart_queries:
        print(f"\n🔍 测试查询: {query}")
        print("-" * 50)
        
        result = analyze_data(test_data, query)
        
        if result and result.get('success'):
            print("✅ 分析执行成功")
            
            if result.get('has_chart'):
                chart_path = result.get('chart_path')
                if chart_path and Path(chart_path).exists():
                    print(f"✅ 图表已生成: {chart_path}")
                    print(f"   文件大小: {Path(chart_path).stat().st_size} bytes")
                else:
                    print("❌ 图表文件未找到")
            else:
                print("⚠️ 未生成图表")
                
            if result.get('code'):
                print("📝 生成的代码:")
                print(result['code'])
        else:
            print("❌ 分析执行失败")
            if result and result.get('error'):
                print(f"   错误: {result['error']}")
        
        print("-" * 50)

def test_result_formatting():
    """测试结果格式化功能"""
    print("\n📊 测试结果格式化功能...")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        '产品': ['A', 'B', 'C', 'D'],
        '价格': [100, 200, 150, 300],
        '销量': [50, 30, 40, 20],
        '评分': [4.5, 4.2, 4.8, 4.0]
    })
    
    # 测试不同类型的分析查询
    format_queries = [
        "显示数据的基本信息",
        "计算各列的统计摘要",
        "计算总销售额",
        "按价格排序显示所有数据",
        "计算各产品的销售额"
    ]
    
    for query in format_queries:
        print(f"\n🔍 测试查询: {query}")
        print("-" * 50)
        
        result = analyze_data(test_data, query)
        
        if result and result.get('success'):
            print("✅ 分析执行成功")
            
            # 测试结果格式化
            formatted_result = ResultFormatter.format_analysis_result(result)
            
            if formatted_result:
                print(f"📋 格式化类型: {formatted_result['type']}")
                
                if formatted_result.get('dataframes'):
                    print(f"📊 包含 {len(formatted_result['dataframes'])} 个数据表")
                
                if formatted_result.get('statistics'):
                    print(f"📈 统计信息: {formatted_result['statistics']}")
                
                if formatted_result.get('insights'):
                    print(f"💡 洞察: {formatted_result['insights']}")
            else:
                print("⚠️ 结果格式化失败")
                
            print("📝 原始输出:")
            print(result.get('output', ''))
        else:
            print("❌ 分析执行失败")
            if result and result.get('error'):
                print(f"   错误: {result['error']}")
        
        print("-" * 50)

def test_charts_directory():
    """测试图表目录"""
    print("\n📁 检查图表目录...")
    
    charts_dir = Path("charts")
    if charts_dir.exists():
        print(f"✅ 图表目录存在: {charts_dir}")
        
        chart_files = list(charts_dir.glob("*.png"))
        print(f"📊 现有图表文件数量: {len(chart_files)}")
        
        for chart_file in chart_files[-5:]:  # 显示最新的5个文件
            stat = chart_file.stat()
            print(f"   📈 {chart_file.name} ({stat.st_size} bytes)")
    else:
        print("⚠️ 图表目录不存在，将自动创建")
        charts_dir.mkdir(exist_ok=True)
        print("✅ 图表目录已创建")

def main():
    """主测试函数"""
    print("🧪 图表生成和结果格式化测试")
    print("=" * 60)
    
    # 检查图表目录
    test_charts_directory()
    
    # 测试结果格式化
    test_result_formatting()
    
    # 测试图表生成
    test_chart_generation()
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    
    # 检查最终状态
    charts_dir = Path("charts")
    if charts_dir.exists():
        chart_files = list(charts_dir.glob("*.png"))
        print(f"📊 总共生成了 {len(chart_files)} 个图表文件")

if __name__ == "__main__":
    main()
