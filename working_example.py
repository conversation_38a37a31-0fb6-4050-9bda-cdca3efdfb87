#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PandasAI V2 工作示例
"""

import os
import pandas as pd

def create_mock_llm():
    """创建一个模拟LLM用于测试"""
    from pandasai.llm.base import LLM
    
    class MockLLM(LLM):
        def __init__(self):
            pass
        
        def call(self, instruction, value):
            # 返回一个简单的模拟响应
            return "# Mock response\nprint('This is a mock response')"
        
        @property
        def type(self):
            return "mock"
    
    return MockLLM()

def test_with_mock_llm():
    """使用模拟LLM测试"""
    print("=== 使用模拟LLM测试 ===")
    
    try:
        # 创建测试数据
        data = {
            'Name': ['Alice', '<PERSON>', '<PERSON>', '<PERSON>'],
            'Age': [25, 30, 35, 28],
            'Salary': [50000, 60000, 70000, 55000]
        }
        df = pd.DataFrame(data)
        print("✅ 测试数据创建成功")
        
        # 创建模拟LLM
        mock_llm = create_mock_llm()
        print("✅ 模拟LLM创建成功")
        
        # 导入SmartDataframe
        from pandasai import SmartDataframe
        
        # 使用模拟LLM创建SmartDataframe
        smart_df = SmartDataframe(df, config={"llm": mock_llm})
        print("✅ SmartDataframe创建成功")
        
        # 显示基本信息
        print(f"数据形状: {smart_df.shape}")
        print(f"列名: {list(smart_df.columns)}")
        print("前几行数据:")
        print(smart_df.head())
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_real_usage_examples():
    """显示真实使用示例"""
    print("\n=== 真实使用示例 ===")
    
    print("""
1. 使用OpenAI:
```python
import os
import pandas as pd
from pandasai import SmartDataframe
from pandasai.llm import OpenAI

# 设置API密钥
os.environ['OPENAI_API_KEY'] = 'your-openai-api-key'

# 或者直接在代码中指定
llm = OpenAI(
    api_token="your-openai-api-key",
    model="gpt-3.5-turbo",
    temperature=0.1
)

# 创建数据
df = pd.DataFrame({
    'sales': [100, 200, 300, 400],
    'region': ['North', 'South', 'East', 'West']
})

# 创建SmartDataframe
smart_df = SmartDataframe(df, config={"llm": llm})

# 自然语言查询
result = smart_df.chat("What is the total sales?")
print(result)
```

2. 使用通义千问:
```python
from pandasai.llm import OpenAI

# 配置通义千问 (使用OpenAI兼容接口)
llm = OpenAI(
    api_token="your-dashscope-api-key",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    model="qwen-plus",
    temperature=0.1
)

smart_df = SmartDataframe(df, config={"llm": llm})
result = smart_df.chat("分析销售数据的趋势")
```

3. 配置选项:
```python
config = {
    "llm": llm,
    "verbose": True,          # 显示详细信息
    "conversational": False,  # 非对话模式
    "save_charts": True,      # 保存图表
    "save_charts_path": "./charts/"
}

smart_df = SmartDataframe(df, config=config)
```
""")

def create_env_file():
    """创建环境变量示例文件"""
    env_content = """# PandasAI V2 环境变量配置示例
# 复制此文件为 .env 并填入真实的API密钥

# OpenAI API密钥
OPENAI_API_KEY=your-openai-api-key-here

# 通义千问 DashScope API密钥
DASHSCOPE_API_KEY=your-dashscope-api-key-here

# PandasAI API密钥 (如果需要)
PANDASAI_API_KEY=your-pandasai-api-key-here

# 其他可选配置
PANDASAI_SAVE_CHARTS=true
PANDASAI_SAVE_CHARTS_PATH=./charts/
"""
    
    try:
        with open('.env.example', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ 创建了 .env.example 文件")
    except Exception as e:
        print(f"⚠️  创建环境变量示例文件失败: {e}")

def main():
    """主函数"""
    print("PandasAI V2 工作示例和配置")
    print("=" * 50)
    
    # 测试基本功能
    test_ok = test_with_mock_llm()
    
    if test_ok:
        print("\n🎉 PandasAI V2 基本功能测试成功!")
        print("SmartDataframe可以正常创建和使用。")
    else:
        print("\n❌ 基本功能测试失败")
        return
    
    # 显示使用示例
    show_real_usage_examples()
    
    # 创建环境变量示例文件
    create_env_file()
    
    print("\n" + "=" * 50)
    print("PandasAI V2 安装和配置完成!")
    print("\n下一步操作:")
    print("1. 获取LLM API密钥:")
    print("   - OpenAI: https://platform.openai.com/api-keys")
    print("   - 通义千问: https://dashscope.console.aliyun.com/")
    print("2. 设置环境变量或在代码中配置API密钥")
    print("3. 参考上面的示例代码开始使用")
    print("4. 查看 pandasai_v2_examples.py 了解更多用法")
    
    print("\n重要提醒:")
    print("- PandasAI V2 需要LLM API密钥才能进行自然语言查询")
    print("- 支持OpenAI、通义千问等多种LLM")
    print("- 通义千问使用OpenAI兼容接口，配置简单")

if __name__ == "__main__":
    main()
