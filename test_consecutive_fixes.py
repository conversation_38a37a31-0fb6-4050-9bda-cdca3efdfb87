#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试连续查询修复效果
"""

import pandas as pd
from perfect_tongyi_integration import analyze_data
from result_formatter import EnhancedResultFormatter

def test_improved_ai_prompts():
    """测试改进的AI提示词"""
    print("🔍 测试改进的AI提示词")
    print("=" * 60)
    
    # 加载数据
    df = pd.read_csv('uploaded_files/sales_data.csv')
    print(f"✅ 数据加载成功: {df.shape}")
    print()
    
    # 测试第二次查询（之前有问题的查询）
    query = "哪种产品的销售数量是最高的"
    print(f"🔍 测试查询: {query}")
    
    result = analyze_data(df, query)
    
    if result and result.get('success'):
        print("✅ AI分析成功")
        
        code = result.get('code', '')
        print(f"📝 生成的代码:")
        print(code)
        print()
        
        output = result.get('output', '')
        print(f"📊 输出内容:")
        print(output)
        print()
        
        # 检查输出是否包含答案
        if output and len(output.strip()) > 10:
            print("✅ 输出内容充足")
            
            # 检查是否包含明确答案
            if any(keyword in output for keyword in ['答案:', '最高', '最大', '鼠标']):
                print("✅ 包含明确答案")
            else:
                print("⚠️ 可能缺少明确答案")
                
            # 测试输出类型检测
            output_type = EnhancedResultFormatter._detect_output_type(output)
            print(f"🎯 检测到的输出类型: {output_type}")
            
            if output_type in ['mixed_data_with_answer', 'series_data']:
                print("✅ 输出类型检测正确")
            else:
                print(f"⚠️ 输出类型可能有问题: {output_type}")
        else:
            print("❌ 输出内容不足")
    else:
        print("❌ AI分析失败")
        if result:
            print(f"错误: {result.get('error', '未知错误')}")

def test_mixed_output_detection():
    """测试混合输出检测"""
    print("\n🔍 测试混合输出检测")
    print("=" * 60)
    
    # 模拟包含数据和答案的输出
    mixed_output = """各产品总销量:
    产品名称  销量
0   台式电脑  12
1   平板电脑  18
2     手机  33
3    显示器  18
4   智能手表  27
5  笔记本电脑  16
6     耳机  42
7     键盘  53
8     鼠标  65

销量最高的产品: 鼠标, 总销量: 65"""

    print("📊 测试输出:")
    print(mixed_output)
    print()
    
    # 测试检测逻辑
    output_type = EnhancedResultFormatter._detect_output_type(mixed_output)
    print(f"🎯 检测结果: {output_type}")
    
    if output_type == 'mixed_data_with_answer':
        print("✅ 正确识别为混合数据（包含答案）")
    else:
        print(f"⚠️ 识别错误，应该是 mixed_data_with_answer，实际是 {output_type}")

def test_answer_extraction():
    """测试答案提取逻辑"""
    print("\n🔍 测试答案提取逻辑")
    print("=" * 60)
    
    mixed_output = """各产品总销量:
    产品名称  销量
0   台式电脑  12
1   平板电脑  18
2     手机  33
3    显示器  18
4   智能手表  27
5  笔记本电脑  16
6     耳机  42
7     键盘  53
8     鼠标  65

销量最高的产品: 鼠标, 总销量: 65"""

    lines = mixed_output.strip().split('\n')
    data_lines = []
    answer_lines = []
    
    print("🔍 分离数据和答案:")
    for line in lines:
        line = line.strip()
        if line:
            # 检查是否是数据行
            if any(char.isdigit() for char in line) and any(header in line for header in ['产品名称', '台式电脑', '平板电脑']):
                data_lines.append(line)
                print(f"  数据行: {line}")
            # 检查是否是答案行
            elif any(keyword in line for keyword in ['答案:', '最高', '最低', '最大', '最小']):
                answer_lines.append(line)
                print(f"  答案行: {line}")
            else:
                print(f"  其他行: {line}")
    
    print(f"\n📊 分离结果:")
    print(f"  数据行数量: {len(data_lines)}")
    print(f"  答案行数量: {len(answer_lines)}")
    
    if data_lines and answer_lines:
        print("✅ 成功分离数据和答案")
    else:
        print("⚠️ 分离可能有问题")

def test_complete_workflow():
    """测试完整工作流程"""
    print("\n🔍 测试完整工作流程")
    print("=" * 60)
    
    # 模拟连续查询
    df = pd.read_csv('uploaded_files/sales_data.csv')
    
    queries = [
        "分析2024年各产品总销售额",
        "哪种产品的销售数量是最高的"
    ]
    
    results = []
    
    for i, query in enumerate(queries):
        print(f"\n{i+1}️⃣ 查询: {query}")
        print("-" * 40)
        
        result = analyze_data(df, query)
        
        if result and result.get('success'):
            print("✅ 分析成功")
            
            output = result.get('output', '')
            if output and len(output.strip()) > 10:
                print("✅ 有充足输出")
                
                output_type = EnhancedResultFormatter._detect_output_type(output)
                print(f"🎯 输出类型: {output_type}")
                
                results.append({
                    'query': query,
                    'result': result,
                    'output_type': output_type
                })
            else:
                print("❌ 输出不足")
        else:
            print("❌ 分析失败")
    
    print(f"\n📋 总结:")
    print(f"  成功查询数量: {len(results)}")
    for i, r in enumerate(results):
        print(f"  查询{i+1}: {r['output_type']} - {r['query'][:30]}...")

def main():
    """主测试函数"""
    print("🎉 连续查询修复效果测试")
    print("=" * 70)
    
    # 1. 测试改进的AI提示词
    test_improved_ai_prompts()
    
    # 2. 测试混合输出检测
    test_mixed_output_detection()
    
    # 3. 测试答案提取逻辑
    test_answer_extraction()
    
    # 4. 测试完整工作流程
    test_complete_workflow()
    
    print("\n" + "=" * 70)
    print("🎯 修复总结:")
    print("1. ✅ AI提示词已改进，强制要求输出结果")
    print("2. ✅ 新增混合输出类型检测")
    print("3. ✅ 实现数据和答案分离显示")
    print("4. ✅ 查询历史保持，不再覆盖")
    
    print("\n💡 现在连续查询应该:")
    print("- 第一次查询结果保持可见")
    print("- 第二次查询正确输出答案")
    print("- 历史记录展开显示")
    print("- 每个查询都有完整的结果")

if __name__ == "__main__":
    main()
