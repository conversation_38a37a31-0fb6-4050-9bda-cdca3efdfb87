{"tables": {"sales_data": {"table_name": "sales_data", "description": "sales_data数据表，包含6个字段和5条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"日期": {"name": "日期", "display_name": "日期", "description": "日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-03T19:44:15.822703", "updated_at": "2025-08-03T20:42:20.700076"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的11", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本电脑", "台式电脑", "平板电脑"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T19:44:15.822703", "updated_at": "2025-08-03T20:51:32.526920"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["8500", "6200", "3200"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T19:44:15.822703", "updated_at": "2025-08-03T19:44:15.822703"}, "销量": {"name": "销量", "display_name": "销量", "description": "销量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["5", "3", "8"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-03T19:44:15.822703", "updated_at": "2025-08-03T19:44:15.822703"}, "地区": {"name": "地区", "display_name": "地区", "description": "地区字段，表示地理位置或区域信息，每个值都是唯一的", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["地理", "区域", "空间"], "created_at": "2025-08-03T19:44:15.822703", "updated_at": "2025-08-03T19:44:15.822703"}, "销售员": {"name": "销售员", "display_name": "销售员", "description": "销售员字段，表示相关人员的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "人员管理和绩效分析的主体标识", "examples": ["张三", "李四", "王五"], "constraints": {}, "tags": ["人员", "标识", "管理"], "created_at": "2025-08-03T19:44:15.822703", "updated_at": "2025-08-03T19:44:15.822703"}}, "relationships": {"日期_产品名称": "[业务关系] test"}, "primary_keys": ["日期", "产品名称"], "created_at": "2025-08-03T19:44:15.822703", "updated_at": "2025-08-03T20:51:32.526920", "version": "1.0.0"}, "finance_data": {"table_name": "finance_data", "description": "finance_data数据表，包含5个字段和5条记录，属于财务分析领域", "business_domain": "财务分析", "columns": {"月份": {"name": "月份", "display_name": "月份", "description": "月份字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01", "2024-02", "2024-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-03T19:44:15.864000", "updated_at": "2025-08-03T19:44:15.864000"}, "收入": {"name": "收入", "display_name": "收入", "description": "收入字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["150000", "180000", "165000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T19:44:15.864000", "updated_at": "2025-08-03T19:44:15.864000"}, "成本": {"name": "成本", "display_name": "成本", "description": "成本字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["90000", "108000", "99000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T19:44:15.864000", "updated_at": "2025-08-03T19:44:15.864000"}, "利润": {"name": "利润", "display_name": "利润", "description": "利润字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["60000", "72000", "66000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T19:44:15.864000", "updated_at": "2025-08-03T19:44:15.864000"}, "部门": {"name": "部门", "display_name": "部门", "description": "部门字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["销售部", "市场部", "技术部"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T19:44:15.864000", "updated_at": "2025-08-03T19:44:15.864000"}}, "relationships": {}, "primary_keys": ["月份", "收入"], "created_at": "2025-08-03T19:44:15.864000", "updated_at": "2025-08-03T19:44:15.864000", "version": "1.0.0"}, "inventory_data": {"table_name": "inventory_data", "description": "inventory_data数据表，包含6个字段和5条记录，属于库存管理领域", "business_domain": "库存管理", "columns": {"商品编号": {"name": "商品编号", "display_name": "商品编号", "description": "商品编号字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["P001", "P002", "P003"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T19:44:15.899542", "updated_at": "2025-08-03T19:44:15.900203"}, "商品名称": {"name": "商品名称", "display_name": "商品名称", "description": "商品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["iPhone 15", "MacBook Pro", "iPad Air"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T19:44:15.900203", "updated_at": "2025-08-03T19:44:15.900203"}, "库存数量": {"name": "库存数量", "display_name": "库存数量", "description": "库存数量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["150", "80", "200"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-03T19:44:15.900203", "updated_at": "2025-08-03T19:44:15.900203"}, "单价": {"name": "单价", "display_name": "单价", "description": "单价字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["6999", "14999", "4599"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T19:44:15.900203", "updated_at": "2025-08-03T19:44:15.900203"}, "供应商": {"name": "供应商", "display_name": "供应商", "description": "供应商字段的数据信息，包含1种不同的值", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["苹果公司"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T19:44:15.900203", "updated_at": "2025-08-03T19:44:15.900203"}, "仓库位置": {"name": "仓库位置", "display_name": "仓库位置", "description": "仓库位置字段的数据信息，包含3种不同的值", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["A区", "B区", "C区"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T19:44:15.900203", "updated_at": "2025-08-03T19:44:15.900203"}}, "relationships": {}, "primary_keys": ["商品编号", "商品名称"], "created_at": "2025-08-03T19:44:15.900203", "updated_at": "2025-08-03T19:44:15.900203", "version": "1.0.0"}, "sales_with_relationships": {"table_name": "sales_with_relationships", "description": "sales_with_relationships数据表，包含7个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"日期": {"name": "日期", "display_name": "日期", "description": "日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-03T19:58:41.189536", "updated_at": "2025-08-03T19:58:41.189536"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本电脑", "台式电脑", "平板电脑"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T19:58:41.189536", "updated_at": "2025-08-03T19:58:41.189536"}, "单价": {"name": "单价", "display_name": "单价", "description": "单价字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["8500", "6200", "3200"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T19:58:41.189536", "updated_at": "2025-08-03T19:58:41.189536"}, "销量": {"name": "销量", "display_name": "销量", "description": "销量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["2", "3", "5"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-03T19:58:41.189536", "updated_at": "2025-08-03T19:58:41.189536"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["17000", "18600", "16000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T19:58:41.189536", "updated_at": "2025-08-03T19:58:41.189536"}, "地区": {"name": "地区", "display_name": "地区", "description": "地区字段，表示地理位置或区域信息，每个值都是唯一的", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["地理", "区域", "空间"], "created_at": "2025-08-03T19:58:41.189536", "updated_at": "2025-08-03T19:58:41.189536"}, "销售员": {"name": "销售员", "display_name": "销售员", "description": "销售员字段，表示相关人员的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "人员管理和绩效分析的主体标识", "examples": ["张三", "李四", "王五"], "constraints": {}, "tags": ["人员", "标识", "管理"], "created_at": "2025-08-03T19:58:41.189536", "updated_at": "2025-08-03T19:58:41.189536"}}, "relationships": {"销售额_单价_销量": "[计算关系] 销售额 = 单价 × 销量", "地区_销售额": "[分组关系] 按地区统计销售额"}, "primary_keys": ["日期", "产品名称"], "created_at": "2025-08-03T19:58:41.189536", "updated_at": "2025-08-03T19:58:41.189536", "version": "1.0.0"}, "customer_info_single_table": {"table_name": "customer_info_single_table", "description": "customer_info_single_table数据表，包含9个字段和4条记录，属于客户管理领域", "business_domain": "客户管理", "columns": {"客户编号": {"name": "客户编号", "display_name": "客户编号", "description": "客户编号字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["C001", "C002", "C003"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T20:06:16.405166", "updated_at": "2025-08-03T20:06:16.405166"}, "客户名称": {"name": "客户名称", "display_name": "客户名称", "description": "客户名称字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["张三公司", "李四企业", "王五集团"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T20:06:16.405166", "updated_at": "2025-08-03T20:06:16.405166"}, "联系电话": {"name": "联系电话", "display_name": "联系电话", "description": "联系电话字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["138****1234", "139****5678", "136****9012"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T20:06:16.405166", "updated_at": "2025-08-03T20:06:16.405166"}, "所在地区": {"name": "所在地区", "display_name": "所在地区", "description": "所在地区字段，表示地理位置或区域信息，每个值都是唯一的", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["地理", "区域", "空间"], "created_at": "2025-08-03T20:06:16.405166", "updated_at": "2025-08-03T20:06:16.405166"}, "行业类型": {"name": "行业类型", "display_name": "行业类型", "description": "行业类型字段，用于数据分类和分组，每个值都是唯一的", "data_type": "object", "business_meaning": "数据分组和分类分析的维度标识", "examples": ["制造业", "服务业", "零售业"], "constraints": {}, "tags": ["分类", "维度", "分组"], "created_at": "2025-08-03T20:06:16.405166", "updated_at": "2025-08-03T20:06:16.405166"}, "客户等级": {"name": "客户等级", "display_name": "客户等级", "description": "客户等级字段，表示相关的状态或等级信息，包含3种不同的值", "data_type": "object", "business_meaning": "业务状态和流程管理的标识", "examples": ["VIP", "普通", "高级"], "constraints": {}, "tags": ["状态", "流程", "管理"], "created_at": "2025-08-03T20:06:16.405166", "updated_at": "2025-08-03T20:06:16.405166"}, "注册日期": {"name": "注册日期", "display_name": "注册日期", "description": "注册日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2023-01-15", "2023-03-20", "2023-05-10"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-03T20:06:16.405166", "updated_at": "2025-08-03T20:06:16.405166"}, "账户状态": {"name": "账户状态", "display_name": "账户状态", "description": "账户状态字段，表示相关的状态或等级信息，包含2种不同的值", "data_type": "object", "business_meaning": "业务状态和流程管理的标识", "examples": ["正常", "冻结"], "constraints": {}, "tags": ["状态", "流程", "管理"], "created_at": "2025-08-03T20:06:16.405166", "updated_at": "2025-08-03T20:06:16.405166"}, "信用评级": {"name": "信用评级", "display_name": "信用评级", "description": "信用评级字段的数据信息，包含3种不同的值", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["AAA", "AA", "A"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T20:06:16.405166", "updated_at": "2025-08-03T20:06:16.405166"}}, "relationships": {"客户编号": "[标识关系] 客户唯一标识，用于数据关联和查找", "所在地区": "[层级关系] 地理维度，用于区域分析和客户分布统计", "客户等级": "[分类关系] 客户分级维度，用于客户价值分层分析", "注册日期": "[时间关系] 时间维度，用于客户增长趋势分析"}, "primary_keys": ["客户编号", "客户名称"], "created_at": "2025-08-03T20:06:16.405166", "updated_at": "2025-08-03T20:06:16.405166", "version": "1.0.0"}, "sales_metadata_test": {"table_name": "sales_metadata_test", "description": "sales_metadata_test数据表，包含6个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"销售日期": {"name": "销售日期", "display_name": "销售日期", "description": "销售日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-03T20:13:44.822295", "updated_at": "2025-08-03T20:13:44.822295"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本电脑", "台式电脑", "平板电脑"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T20:13:44.822295", "updated_at": "2025-08-03T20:13:44.822295"}, "销售金额": {"name": "销售金额", "display_name": "销售金额", "description": "销售金额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["8500", "6200", "3200"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T20:13:44.822295", "updated_at": "2025-08-03T20:13:44.822295"}, "销售数量": {"name": "销售数量", "display_name": "销售数量", "description": "销售数量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["2", "3", "5"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-03T20:13:44.822295", "updated_at": "2025-08-03T20:13:44.822295"}, "销售区域": {"name": "销售区域", "display_name": "销售区域", "description": "销售区域字段，表示地理位置或区域信息，每个值都是唯一的", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["地理", "区域", "空间"], "created_at": "2025-08-03T20:13:44.822295", "updated_at": "2025-08-03T20:13:44.822295"}, "销售代表": {"name": "销售代表", "display_name": "销售代表", "description": "销售代表字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["张三", "李四", "王五"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T20:13:44.822295", "updated_at": "2025-08-03T20:13:44.822295"}}, "relationships": {}, "primary_keys": ["销售日期", "产品名称"], "created_at": "2025-08-03T20:13:44.822295", "updated_at": "2025-08-03T20:13:44.822295", "version": "1.0.0"}, "meaningful_table": {"table_name": "meaningful_table", "description": "meaningful_table数据表，包含4个字段和2条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"销售日期": {"name": "销售日期", "display_name": "销售日期", "description": "销售日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-03T20:13:44.864535", "updated_at": "2025-08-03T20:13:44.864535"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本电脑", "台式电脑"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T20:13:44.864535", "updated_at": "2025-08-03T20:13:44.864535"}, "销售金额": {"name": "销售金额", "display_name": "销售金额", "description": "销售金额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["8500", "6200"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T20:13:44.864535", "updated_at": "2025-08-03T20:13:44.864535"}, "销售数量": {"name": "销售数量", "display_name": "销售数量", "description": "销售数量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["2", "3"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-03T20:13:44.864535", "updated_at": "2025-08-03T20:13:44.864535"}}, "relationships": {}, "primary_keys": ["销售日期", "产品名称"], "created_at": "2025-08-03T20:13:44.864535", "updated_at": "2025-08-03T20:13:44.864535", "version": "1.0.0"}, "cryptic_table": {"table_name": "cryptic_table", "description": "cryptic_table数据表，包含4个字段和2条记录", "business_domain": "通用", "columns": {"col_a": {"name": "col_a", "display_name": "col_a", "description": "销售交易发生的具体日期", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-03T20:13:44.893869", "updated_at": "2025-08-03T20:13:44.913271"}, "col_b": {"name": "col_b", "display_name": "col_b", "description": "销售产品的具体名称或型号", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本电脑", "台式电脑"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T20:13:44.893869", "updated_at": "2025-08-03T20:13:44.932470"}, "col_c": {"name": "col_c", "display_name": "col_c", "description": "单笔销售交易的总金额", "data_type": "int64", "business_meaning": "反映业务收入情况的核心KPI指标", "examples": ["8500", "6200"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "收入", "KPI"], "created_at": "2025-08-03T20:13:44.893869", "updated_at": "2025-08-03T20:13:44.952656"}, "col_d": {"name": "col_d", "display_name": "col_d", "description": "单笔交易中产品的销售数量", "data_type": "int64", "business_meaning": "反映产品市场接受度和需求量", "examples": ["2", "3"], "constraints": {"min": 0, "unit": "元"}, "tags": ["销售", "数量", "市场"], "created_at": "2025-08-03T20:13:44.893869", "updated_at": "2025-08-03T20:13:44.966726"}}, "relationships": {}, "primary_keys": ["col_a", "col_b"], "created_at": "2025-08-03T20:13:44.893869", "updated_at": "2025-08-03T20:13:44.966726", "version": "1.0.0"}, "ui_test_table": {"table_name": "ui_test_table", "description": "ui_test_table数据表，包含3个字段和2条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"客户编号": {"name": "客户编号", "display_name": "客户编号", "description": "客户编号字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["C001", "C002"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T20:17:59.033599", "updated_at": "2025-08-03T20:17:59.033599"}, "客户名称": {"name": "客户名称", "display_name": "客户名称", "description": "客户名称字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["张三公司", "李四企业"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T20:17:59.033599", "updated_at": "2025-08-03T20:17:59.033599"}, "销售金额": {"name": "销售金额", "display_name": "销售金额", "description": "销售金额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["10000", "15000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T20:17:59.033599", "updated_at": "2025-08-03T20:17:59.033599"}}, "relationships": {}, "primary_keys": ["客户编号", "客户名称"], "created_at": "2025-08-03T20:17:59.033599", "updated_at": "2025-08-03T20:17:59.033599", "version": "1.0.0"}, "customer_sales": {"table_name": "customer_sales", "description": "customer_sales数据表，包含5个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"客户编号": {"name": "客户编号", "display_name": "客户编号", "description": "客户的唯一编号，格式为C+三位数字", "data_type": "object", "business_meaning": "客户唯一标识，用于客户关系管理和数据关联", "examples": ["C001", "C002", "C003"], "constraints": {}, "tags": ["客户", "标识", "主键", "核心字段", "客户相关"], "created_at": "2025-08-03T20:38:29.286673", "updated_at": "2025-08-03T20:38:29.426140"}, "客户名称": {"name": "客户名称", "display_name": "客户名称", "description": "客户名称字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["张三公司", "李四企业", "王五集团"], "constraints": {}, "tags": ["核心字段", "客户相关", "未分类"], "created_at": "2025-08-03T20:38:29.286673", "updated_at": "2025-08-03T20:38:29.461214"}, "销售金额": {"name": "销售金额", "display_name": "销售金额", "description": "客户购买产品的总金额，以人民币计算", "data_type": "int64", "business_meaning": "客户单次交易的总金额，用于收入分析和业绩评估", "examples": ["10000", "15000", "8000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T20:38:29.286673", "updated_at": "2025-08-03T20:38:29.388412"}, "销售数量": {"name": "销售数量", "display_name": "销售数量", "description": "客户单次购买的产品数量，用于销量分析和库存管理", "data_type": "int64", "business_meaning": "反映客户购买意愿和产品受欢迎程度的重要指标，用于市场分析和库存规划", "examples": ["5", "8", "3", "10"], "constraints": {"min": 1, "unit": "件"}, "tags": ["销售", "数量", "市场", "库存"], "created_at": "2025-08-03T20:38:29.286673", "updated_at": "2025-08-03T20:38:29.529620"}, "地区": {"name": "地区", "display_name": "地区", "description": "地区字段，表示地理位置或区域信息，每个值都是唯一的", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["核心字段", "空间", "客户相关", "区域", "地理"], "created_at": "2025-08-03T20:38:29.286673", "updated_at": "2025-08-03T20:38:29.496228"}}, "relationships": {}, "primary_keys": ["客户编号", "客户名称"], "created_at": "2025-08-03T20:38:29.286673", "updated_at": "2025-08-03T20:38:29.529620", "version": "1.0.0"}, "product_inventory": {"table_name": "product_inventory", "description": "product_inventory数据表，包含3个字段和2条记录，属于库存管理领域", "business_domain": "库存管理", "columns": {"产品ID": {"name": "产品ID", "display_name": "产品ID", "description": "产品ID字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["P001", "P002"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T20:38:29.319507", "updated_at": "2025-08-03T20:38:29.319507"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本", "手机"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T20:38:29.319507", "updated_at": "2025-08-03T20:38:29.319507"}, "库存": {"name": "库存", "display_name": "库存", "description": "库存字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["100", "200"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-03T20:38:29.319507", "updated_at": "2025-08-03T20:38:29.319507"}}, "relationships": {}, "primary_keys": ["产品ID", "产品名称"], "created_at": "2025-08-03T20:38:29.319507", "updated_at": "2025-08-03T20:38:29.319507", "version": "1.0.0"}, "save_test_table": {"table_name": "save_test_table", "description": "save_test_table数据表，包含3个字段和2条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"客户编号": {"name": "客户编号", "display_name": "客户编号", "description": "客户的唯一标识编号，用于客户管理和数据关联", "data_type": "object", "business_meaning": "客户关系管理的核心标识，用于客户数据的唯一识别和业务分析", "examples": ["C001", "C002", "C003"], "constraints": {"format": "C+数字", "unique": true}, "tags": ["标识", "客户", "核心字段", "主键"], "created_at": "2025-08-03T20:49:47.073056", "updated_at": "2025-08-03T20:49:47.096980"}, "客户名称": {"name": "客户名称", "display_name": "客户名称", "description": "客户名称字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["张三公司", "李四企业"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T20:49:47.073056", "updated_at": "2025-08-03T20:49:47.073056"}, "销售金额": {"name": "销售金额", "display_name": "销售金额", "description": "销售金额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["10000", "15000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T20:49:47.073056", "updated_at": "2025-08-03T20:49:47.073056"}}, "relationships": {}, "primary_keys": ["客户编号", "客户名称"], "created_at": "2025-08-03T20:49:47.073056", "updated_at": "2025-08-03T20:49:47.096980", "version": "1.0.0"}, "sales_data.csv": {"table_name": "sales_data.csv", "description": "sales_data.csv数据表，包含6个字段和20条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"日期": {"name": "日期", "display_name": "日期", "description": "日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02", "2024-01-03"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-03T20:52:55.028237", "updated_at": "2025-08-03T20:52:55.028237"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，包含9种不同的值", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本电脑", "台式电脑", "平板电脑"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T20:52:55.028237", "updated_at": "2025-08-03T20:52:55.028237"}, "销售额": {"name": "销售额", "display_name": "销售额", "description": "销售额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["8500", "6200", "3200"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T20:52:55.028237", "updated_at": "2025-08-03T20:52:55.028237"}, "销量": {"name": "销量", "display_name": "销量", "description": "销量字段，表示相关的数量或计数", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["5", "3", "8"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-03T20:52:55.028237", "updated_at": "2025-08-03T20:52:55.028237"}, "地区": {"name": "地区", "display_name": "地区", "description": "地区字段，表示地理位置或区域信息，包含4种不同的值", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["地理", "区域", "空间"], "created_at": "2025-08-03T20:52:55.028237", "updated_at": "2025-08-03T20:52:55.028237"}, "销售员": {"name": "销售员", "display_name": "销售员", "description": "销售员字段，表示相关人员的标识信息，包含4种不同的值", "data_type": "object", "business_meaning": "人员管理和绩效分析的主体标识", "examples": ["张三", "李四", "王五"], "constraints": {}, "tags": ["人员", "标识", "管理"], "created_at": "2025-08-03T20:52:55.028237", "updated_at": "2025-08-03T20:52:55.028237"}}, "relationships": {}, "primary_keys": ["日期", "销售额"], "created_at": "2025-08-03T20:52:55.028237", "updated_at": "2025-08-03T20:52:55.028237", "version": "1.0.0"}, "customer_data": {"table_name": "customer_data", "description": "customer_data数据表，包含5个字段和3条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"客户编号": {"name": "客户编号", "display_name": "客户编号", "description": "客户的唯一编号，格式为C+三位数字", "data_type": "object", "business_meaning": "客户唯一标识，用于客户关系管理和数据关联", "examples": ["C001", "C002", "C003"], "constraints": {}, "tags": ["标识", "客户", "主键"], "created_at": "2025-08-03T20:58:00.766189", "updated_at": "2025-08-03T20:58:00.850367"}, "客户名称": {"name": "客户名称", "display_name": "客户名称", "description": "客户名称字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["张三公司", "李四企业", "王五集团"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T20:58:00.766189", "updated_at": "2025-08-03T20:58:00.766189"}, "销售金额": {"name": "销售金额", "display_name": "销售金额", "description": "客户购买产品的总金额，以人民币计算", "data_type": "int64", "business_meaning": "客户单次交易的总金额，用于收入分析和业绩评估", "examples": ["10000", "15000", "8000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T20:58:00.766189", "updated_at": "2025-08-03T20:58:00.874539"}, "销售数量": {"name": "销售数量", "display_name": "销售数量", "description": "销售数量字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["5", "8", "3"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-03T20:58:00.766189", "updated_at": "2025-08-03T20:58:00.766189"}, "地区": {"name": "地区", "display_name": "地区", "description": "地区字段，表示地理位置或区域信息，每个值都是唯一的", "data_type": "object", "business_meaning": "地理分析和区域策略的空间维度", "examples": ["北京", "上海", "广州"], "constraints": {}, "tags": ["地理", "区域", "空间"], "created_at": "2025-08-03T20:58:00.766189", "updated_at": "2025-08-03T20:58:00.766189"}}, "relationships": {}, "primary_keys": ["客户编号", "客户名称"], "created_at": "2025-08-03T20:58:00.766189", "updated_at": "2025-08-03T20:58:00.874539", "version": "1.0.0"}, "product_data": {"table_name": "product_data", "description": "product_data数据表，包含3个字段和2条记录，属于库存管理领域", "business_domain": "库存管理", "columns": {"产品ID": {"name": "产品ID", "display_name": "产品ID", "description": "产品的唯一编号，格式为P+三位数字", "data_type": "object", "business_meaning": "产品唯一标识符，用于产品管理和库存跟踪", "examples": ["P001", "P002", "P003"], "constraints": {}, "tags": ["标识", "产品", "主键"], "created_at": "2025-08-03T20:58:00.792034", "updated_at": "2025-08-03T20:58:00.899607"}, "产品名称": {"name": "产品名称", "display_name": "产品名称", "description": "产品名称字段，表示产品或商品的标识信息，每个值都是唯一的", "data_type": "object", "business_meaning": "产品分析和库存管理的核心标识", "examples": ["笔记本", "手机"], "constraints": {}, "tags": ["产品", "标识", "分类"], "created_at": "2025-08-03T20:58:00.792034", "updated_at": "2025-08-03T20:58:00.792034"}, "库存": {"name": "库存", "display_name": "库存", "description": "库存字段，表示相关的数量或计数，每个值都是唯一的", "data_type": "int64", "business_meaning": "业务量和规模的重要度量指标", "examples": ["100", "200"], "constraints": {"min": 0, "unit": "件"}, "tags": ["数量", "规模", "业务量"], "created_at": "2025-08-03T20:58:00.792034", "updated_at": "2025-08-03T20:58:00.792034"}}, "relationships": {}, "primary_keys": ["产品ID", "产品名称"], "created_at": "2025-08-03T20:58:00.792034", "updated_at": "2025-08-03T20:58:00.899607", "version": "1.0.0"}, "order_data": {"table_name": "order_data", "description": "order_data数据表，包含4个字段和2条记录，属于销售管理领域", "business_domain": "销售管理", "columns": {"订单号": {"name": "订单号", "display_name": "订单号", "description": "订单号字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["O001", "O002"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T20:58:00.827824", "updated_at": "2025-08-03T20:58:00.827824"}, "订单日期": {"name": "订单日期", "display_name": "订单日期", "description": "订单日期字段，记录事件发生的时间信息，每个值都是唯一的", "data_type": "object", "business_meaning": "时间序列分析和趋势预测的基础维度", "examples": ["2024-01-01", "2024-01-02"], "constraints": {}, "tags": ["时间", "维度", "趋势"], "created_at": "2025-08-03T20:58:00.827824", "updated_at": "2025-08-03T20:58:00.827824"}, "订单金额": {"name": "订单金额", "display_name": "订单金额", "description": "订单金额字段，表示相关的金额数值，每个值都是唯一的", "data_type": "int64", "business_meaning": "财务分析和业绩评估的核心指标", "examples": ["5000", "8000"], "constraints": {"min": 0, "unit": "元"}, "tags": ["财务", "金额", "KPI"], "created_at": "2025-08-03T20:58:00.827824", "updated_at": "2025-08-03T20:58:00.827824"}, "客户ID": {"name": "客户ID", "display_name": "客户ID", "description": "客户ID字段的数据信息，每个值都是唯一的", "data_type": "object", "business_meaning": "需要进一步定义业务含义", "examples": ["C001", "C002"], "constraints": {}, "tags": ["未分类"], "created_at": "2025-08-03T20:58:00.827824", "updated_at": "2025-08-03T20:58:00.827824"}}, "relationships": {}, "primary_keys": ["订单号", "订单日期"], "created_at": "2025-08-03T20:58:00.827824", "updated_at": "2025-08-03T20:58:00.827824", "version": "1.0.0"}}, "templates": {"销售相关": {"销售额": {"description": "产品或服务的销售金额", "business_meaning": "反映业务收入情况的核心指标", "data_type": "float", "constraints": {"min": 0}, "tags": ["财务", "收入", "KPI"]}, "销量": {"description": "产品销售的数量", "business_meaning": "反映产品市场接受度和需求量", "data_type": "int", "constraints": {"min": 0}, "tags": ["销售", "数量", "市场"]}, "销售员": {"description": "负责销售的员工姓名", "business_meaning": "用于分析个人销售业绩和团队管理", "data_type": "string", "tags": ["人员", "业绩", "管理"]}}, "产品相关": {"产品名称": {"description": "产品的具体名称或型号", "business_meaning": "用于产品分析和库存管理的标识", "data_type": "string", "tags": ["产品", "标识", "分类"]}, "价格": {"description": "产品的单价或售价", "business_meaning": "定价策略和利润分析的基础数据", "data_type": "float", "constraints": {"min": 0}, "tags": ["定价", "财务", "策略"]}, "库存": {"description": "产品的库存数量", "business_meaning": "库存管理和供应链优化的关键指标", "data_type": "int", "constraints": {"min": 0}, "tags": ["库存", "供应链", "管理"]}}, "地理相关": {"地区": {"description": "销售或业务发生的地理区域", "business_meaning": "用于区域分析和市场策略制定", "data_type": "string", "tags": ["地理", "区域", "市场"]}, "城市": {"description": "具体的城市名称", "business_meaning": "城市级别的市场分析和布局", "data_type": "string", "tags": ["地理", "城市", "市场"]}}, "时间相关": {"日期": {"description": "事件发生的具体日期", "business_meaning": "时间序列分析和趋势预测的基础", "data_type": "datetime", "tags": ["时间", "趋势", "分析"]}, "月份": {"description": "事件发生的月份", "business_meaning": "月度业绩分析和季节性趋势识别", "data_type": "string", "tags": ["时间", "月度", "季节性"]}}}, "export_time": "2025-08-03T21:02:56.983835", "version": "1.0.0"}