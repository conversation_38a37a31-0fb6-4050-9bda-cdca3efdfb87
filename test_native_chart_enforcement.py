#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Streamlit原生图表强制使用功能
"""

from perfect_tongyi_integration import TongyiQianwenLLM
import pandas as pd

def test_code_conversion():
    """测试代码转换功能"""
    print("🔄 测试matplotlib到Streamlit原生的转换")
    print("=" * 50)
    
    llm = TongyiQianwenLLM()
    
    # 模拟matplotlib代码（类似您遇到的问题代码）
    matplotlib_code = """import matplotlib.pyplot as plt
product_sales = df.groupby('产品名称')['销售额'].sum().sort_values(ascending=False)
print(product_sales)
plt.figure(figsize=(12, 8))
ax = product_sales.plot(kind='bar', color='skyblue')
plt.title('各产品总销售额对比', fontsize=16, fontweight='bold')
plt.xlabel('产品名称', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.grid(True, alpha=0.3)
for i, v in enumerate(product_sales):
    ax.text(i, v + 0.02*v, f'{v}', ha='center', va='bottom', fontsize=10)
plt.tight_layout()"""

    print("原始matplotlib代码:")
    print(matplotlib_code)
    print()
    
    # 测试转换
    converted_code = llm.enforce_streamlit_native_charts(matplotlib_code, "请分析各产品销售额，按照柱状图展示")
    
    print("转换后的代码:")
    print(converted_code)
    print()
    
    # 检查转换结果
    if 'st.bar_chart' in converted_code:
        print("✅ 成功转换为Streamlit原生柱状图")
        print("💡 这样可以避免matplotlib的渲染和缩进问题！")
    else:
        print("❌ 转换失败")

def create_comparison_demo():
    """创建对比演示"""
    demo_code = '''import streamlit as st
import pandas as pd
import matplotlib.pyplot as plt

st.title("📊 图表方法对比演示")

# 创建示例数据
data = {
    "产品名称": ["iPhone 15", "iPad Air", "MacBook Pro", "AirPods Pro", "Apple Watch"],
    "销售额": [8000, 4500, 15000, 1800, 3200]
}
df = pd.DataFrame(data)

st.subheader("📋 数据")
st.dataframe(df)

# 方法1：Streamlit原生（推荐）
st.subheader("✅ 方法1：Streamlit原生柱状图（推荐）")
sales_data = df.set_index("产品名称")["销售额"]
st.bar_chart(sales_data)

st.success("""
**优势：**
- 渲染速度快
- 自动适应容器宽度  
- 内置交互功能
- 不会出现字体问题
- 与Streamlit主题一致
""")

# 方法2：Matplotlib（问题较多）
st.subheader("❌ 方法2：Matplotlib（容易出问题）")
st.warning("""
**问题：**
- 可能出现缩进错误
- 字体显示问题
- 渲染速度慢
- 需要额外的保存和显示步骤
- 在服务器环境中可能无法显示
""")

# 显示matplotlib代码示例（不执行）
st.code("""
# 容易出问题的matplotlib代码
import matplotlib.pyplot as plt
plt.figure(figsize=(12, 8))
ax = sales_data.plot(kind='bar')
for i, v in enumerate(sales_data):  # 这里容易出现缩进错误
ax.text(i, v, f'{v}')  # ❌ 缺少缩进
plt.show()
""", language='python')

st.info("💡 建议：优先使用Streamlit原生图表方法，避免matplotlib的各种问题！")
'''
    
    with open("chart_comparison_demo.py", "w", encoding="utf-8") as f:
        f.write(demo_code)
    
    print("📝 已创建图表对比演示文件: chart_comparison_demo.py")
    print("运行命令: streamlit run chart_comparison_demo.py")

if __name__ == "__main__":
    test_code_conversion()
    create_comparison_demo()
    
    print("\n" + "=" * 60)
    print("🎯 解决方案总结")
    print("=" * 60)
    print("问题：为什么不使用Streamlit原生图表方法？")
    print()
    print("原因分析：")
    print("1. AI模型习惯生成matplotlib代码")
    print("2. 提示词执行不够强制")
    print("3. 缺少自动转换机制")
    print()
    print("解决方案：")
    print("1. ✅ 添加了强制转换机制")
    print("2. ✅ 自动检测matplotlib代码并转换为Streamlit原生")
    print("3. ✅ 避免了matplotlib的渲染和缩进问题")
    print("4. ✅ 提高了图表显示的可靠性")
    print()
    print("💡 现在系统会优先使用st.bar_chart、st.line_chart等原生方法！")
