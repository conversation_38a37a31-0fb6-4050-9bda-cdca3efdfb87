#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通义千问 + PandasAI V2 工作集成
使用自定义LLM类实现完整集成
"""

import os
import pandas as pd
from dotenv import load_dotenv
import requests
import json
import re

# 加载环境变量
load_dotenv()

from pandasai.llm.base import LLM

class TongyiQianwenLLM(LLM):
    """通义千问LLM类 - 兼容PandasAI V2"""

    def __init__(self, api_key=None, model="qwen-plus", temperature=0.1, max_tokens=2000):
        """初始化通义千问LLM"""
        self.api_key = api_key or os.getenv('DASHSCOPE_API_KEY')
        if not self.api_key:
            raise ValueError("未找到DASHSCOPE_API_KEY，请设置环境变量或传入api_key参数")

        self.model = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"

        print(f"✅ 通义千问LLM初始化成功")
        print(f"   模型: {self.model}")
        print(f"   API密钥: {self.api_key[:10]}...{self.api_key[-4:]}")
    
    def call(self, instruction, value):
        """调用通义千问API生成代码"""
        # 构建数据分析提示词
        prompt = f"""你是一个专业的数据分析师。请根据以下数据和用户指令，生成准确的Python代码来回答问题。

数据信息:
{value}

用户指令: {instruction}

要求:
1. 生成可执行的Python代码
2. 使用pandas DataFrame，变量名为'df'
3. 代码应该直接计算并返回结果
4. 只返回Python代码，不要包含解释文字
5. 如果需要打印结果，使用print()函数
6. 确保代码语法正确且可执行

Python代码:"""

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                code = result['choices'][0]['message']['content']
                
                # 清理代码，移除markdown标记
                code = re.sub(r'```python\n?', '', code)
                code = re.sub(r'```\n?', '', code)
                code = code.strip()
                
                return code
            else:
                error_msg = f"API调用失败: {response.status_code} - {response.text}"
                print(f"❌ {error_msg}")
                return f"# {error_msg}\nprint('API调用失败')"
                
        except Exception as e:
            error_msg = f"API调用异常: {e}"
            print(f"❌ {error_msg}")
            return f"# {error_msg}\nprint('API调用异常')"
    
    @property
    def type(self):
        """返回LLM类型"""
        return "tongyi_qianwen"

def create_smart_dataframe_with_tongyi(df, model="qwen-plus", temperature=0.1):
    """创建使用通义千问的SmartDataframe"""
    from pandasai import SmartDataframe
    
    # 创建通义千问LLM
    llm = TongyiQianwenLLM(model=model, temperature=temperature)
    
    # 创建SmartDataframe
    smart_df = SmartDataframe(df, config={
        "llm": llm,
        "verbose": True,
        "conversational": False,
        "save_charts": True,
        "save_charts_path": "./charts/"
    })
    
    return smart_df

def demo_basic_analysis():
    """演示基本数据分析"""
    print("📊 基本数据分析演示")
    print("=" * 40)
    
    # 创建示例数据
    data = {
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '价格': [6999, 4599, 14999, 1899, 3199],
        '销量': [1200, 800, 400, 1500, 1000],
        '类别': ['手机', '平板', '笔记本', '配件', '配件'],
        '评分': [4.8, 4.6, 4.9, 4.7, 4.5],
        '库存': [150, 200, 80, 300, 250]
    }
    
    df = pd.DataFrame(data)
    print("✅ 示例数据创建成功:")
    print(df)
    print()
    
    # 创建SmartDataframe
    try:
        smart_df = create_smart_dataframe_with_tongyi(df)
        print("✅ SmartDataframe创建成功")
    except Exception as e:
        print(f"❌ SmartDataframe创建失败: {e}")
        return
    
    # 执行查询
    queries = [
        "总销售额是多少？（价格×销量）",
        "哪个产品的销量最高？",
        "平均价格是多少？",
        "配件类产品有哪些？",
        "评分最高的产品是什么？"
    ]
    
    print("\n🔍 执行数据查询:")
    print("-" * 40)
    
    for i, query in enumerate(queries, 1):
        print(f"\n{i}. 查询: {query}")
        try:
            result = smart_df.chat(query)
            print(f"   ✅ 结果: {result}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")

def demo_advanced_analysis():
    """演示高级数据分析"""
    print("\n🚀 高级数据分析演示")
    print("=" * 40)
    
    # 创建更复杂的数据
    import numpy as np
    
    # 生成月度销售数据
    months = ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06']
    products = ['产品A', '产品B', '产品C', '产品D']
    
    data = []
    for month in months:
        for product in products:
            data.append({
                '月份': month,
                '产品': product,
                '销量': np.random.randint(100, 1000),
                '收入': np.random.randint(10000, 100000),
                '成本': np.random.randint(5000, 50000)
            })
    
    df = pd.DataFrame(data)
    df['利润'] = df['收入'] - df['成本']
    
    print("✅ 高级示例数据创建成功:")
    print(df.head(10))
    print(f"数据形状: {df.shape}")
    print()
    
    # 创建SmartDataframe
    try:
        smart_df = create_smart_dataframe_with_tongyi(df, model="qwen-plus")
        print("✅ 高级SmartDataframe创建成功")
    except Exception as e:
        print(f"❌ SmartDataframe创建失败: {e}")
        return
    
    # 高级查询
    advanced_queries = [
        "每个月的总收入是多少？",
        "哪个产品的总利润最高？",
        "计算每个产品的平均利润率（利润/收入）",
        "找出利润率最高的月份和产品组合",
        "按月份统计总销量的趋势"
    ]
    
    print("\n🔍 执行高级查询:")
    print("-" * 40)
    
    for i, query in enumerate(advanced_queries, 1):
        print(f"\n{i}. 高级查询: {query}")
        try:
            result = smart_df.chat(query)
            print(f"   ✅ 结果: {result}")
        except Exception as e:
            print(f"   ❌ 错误: {e}")

def test_different_models():
    """测试不同的通义千问模型"""
    print("\n🔄 测试不同模型性能")
    print("=" * 40)
    
    # 简单测试数据
    test_data = {
        '数值': [10, 20, 30, 40, 50],
        '类别': ['A', 'B', 'A', 'B', 'A']
    }
    df = pd.DataFrame(test_data)
    
    models = ['qwen-turbo', 'qwen-plus', 'qwen-max']
    test_query = "计算每个类别的平均数值"
    
    for model in models:
        print(f"\n测试模型: {model}")
        try:
            smart_df = create_smart_dataframe_with_tongyi(df, model=model)
            result = smart_df.chat(test_query)
            print(f"✅ {model} 结果: {result}")
        except Exception as e:
            print(f"❌ {model} 失败: {e}")

def main():
    """主函数"""
    print("🎯 通义千问 + PandasAI V2 完整集成演示")
    print("=" * 60)
    
    # 检查API密钥
    api_key = os.getenv('DASHSCOPE_API_KEY')
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY环境变量")
        print("请在.env文件中配置: DASHSCOPE_API_KEY=your-api-key")
        return
    
    print(f"✅ API密钥已加载: {api_key[:10]}...{api_key[-4:]}")
    
    try:
        # 1. 基本分析演示
        demo_basic_analysis()
        
        # 2. 高级分析演示
        demo_advanced_analysis()
        
        # 3. 测试不同模型
        test_different_models()
        
        print("\n" + "=" * 60)
        print("🎉 通义千问集成演示完成!")
        print("\n✅ 集成特点:")
        print("- 完全兼容PandasAI V2")
        print("- 支持中文自然语言查询")
        print("- 支持多种通义千问模型")
        print("- 支持复杂数据分析")
        print("- 错误处理完善")
        
        print("\n📖 使用方法:")
        print("```python")
        print("smart_df = create_smart_dataframe_with_tongyi(df)")
        print("result = smart_df.chat('你的中文问题')")
        print("```")
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
