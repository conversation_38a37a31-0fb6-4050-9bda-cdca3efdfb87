#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 Infinite extent 错误
"""

import pandas as pd
import numpy as np
from perfect_tongyi_integration import analyze_data

def debug_infinite_extent_error():
    """调试 Infinite extent 错误"""
    print("🔍 调试 Infinite extent 错误")
    print("=" * 60)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro', 'AirPods Pro', 'Apple Watch'],
        '销售额': [25500, 20200, 15000, 9700, 8000],
        '销量': [120, 80, 40, 150, 100],
        '日期': ['2024-01-01', '2024-01-02', '2024-01-03', '2024-01-04', '2024-01-05']
    })
    
    print("原始数据:")
    print(df)
    print()
    
    # 测试查询
    query = "请分析各产品销售额，用柱状图展示"
    
    print(f"📋 测试查询: {query}")
    print("-" * 40)
    
    try:
        # 调用分析函数
        result = analyze_data(df, query, "sales_data", use_metadata=True)
        
        if result.get('success'):
            print("✅ 分析成功")
            
            # 检查生成的代码
            code = result.get('code', '')
            plotly_code = result.get('plotly_code', '')
            
            print("生成的代码:")
            print(code)
            print()
            
            if plotly_code:
                print("保存给前端的代码:")
                print(plotly_code)
                print()
                
                # 分析代码中的数据处理
                analyze_data_processing(plotly_code, df)
            
        else:
            print(f"❌ 分析失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        import traceback
        traceback.print_exc()

def analyze_data_processing(code, df):
    """分析代码中的数据处理"""
    print("🔍 分析数据处理过程")
    print("=" * 30)
    
    # 模拟执行代码中的数据处理部分
    try:
        import plotly.express as px
        
        # 创建执行环境
        exec_globals = {
            'df': df,
            'pd': pd,
            'px': px,
            'print': print
        }
        
        # 提取数据处理部分（不包含图表显示）
        lines = code.split('\n')
        data_processing_lines = []
        
        for line in lines:
            if (not line.strip().startswith('st.') and 
                not line.strip().startswith('fig =') and
                not line.strip().startswith('fig.') and
                line.strip() and
                not line.strip().startswith('#')):
                data_processing_lines.append(line)
        
        data_processing_code = '\n'.join(data_processing_lines)
        
        print("数据处理代码:")
        print(data_processing_code)
        print()
        
        # 执行数据处理代码
        exec(data_processing_code, exec_globals)
        
        # 检查生成的变量
        print("执行后的变量:")
        for var_name in ['grouped_data', 'data', 'product_sales']:
            if var_name in exec_globals:
                var_value = exec_globals[var_name]
                print(f"{var_name}:")
                print(var_value)
                print(f"类型: {type(var_value)}")
                
                # 检查是否有异常值
                if hasattr(var_value, 'isnull'):
                    has_null = var_value.isnull().any().any() if hasattr(var_value.isnull().any(), 'any') else var_value.isnull().any()
                    print(f"包含NaN: {has_null}")
                
                if hasattr(var_value, 'values'):
                    values = var_value.values if hasattr(var_value, 'values') else var_value
                    if hasattr(values, 'flatten'):
                        flat_values = values.flatten()
                        has_inf = np.isinf(flat_values).any()
                        print(f"包含无穷大: {has_inf}")
                        if has_inf:
                            print(f"无穷大值位置: {np.where(np.isinf(flat_values))}")
                
                print()
        
    except Exception as e:
        print(f"❌ 数据处理分析失败: {e}")

def test_plotly_chart_creation():
    """测试Plotly图表创建"""
    print("\n🔍 测试Plotly图表创建")
    print("=" * 40)
    
    # 创建测试数据
    df = pd.DataFrame({
        '产品名称': ['iPhone 15', 'iPad Air', 'MacBook Pro'],
        '销售额': [25500, 20200, 15000]
    })
    
    print("测试数据:")
    print(df)
    print()
    
    try:
        import plotly.express as px
        
        # 测试不同的数据处理方式
        test_cases = [
            {
                'name': '方式1: 直接使用原始数据',
                'code': 'fig = px.bar(df, x="产品名称", y="销售额", title="销售额对比")'
            },
            {
                'name': '方式2: 使用groupby处理',
                'code': '''grouped_data = df.groupby('产品名称')['销售额'].sum().reset_index()
fig = px.bar(grouped_data, x='产品名称', y='销售额', title='销售额对比')'''
            },
            {
                'name': '方式3: 使用set_index处理',
                'code': '''data = df.set_index('产品名称')['销售额']
fig = px.bar(x=data.index, y=data.values, title='销售额对比')'''
            }
        ]
        
        for test_case in test_cases:
            print(f"📋 {test_case['name']}")
            print("-" * 20)
            print("代码:")
            print(test_case['code'])
            
            try:
                exec_globals = {
                    'df': df,
                    'pd': pd,
                    'px': px,
                    'print': print
                }
                
                exec(test_case['code'], exec_globals)
                
                if 'fig' in exec_globals:
                    fig = exec_globals['fig']
                    print("✅ 图表创建成功")
                    
                    # 检查图表数据
                    if hasattr(fig, 'data') and fig.data:
                        chart_data = fig.data[0]
                        if hasattr(chart_data, 'x') and hasattr(chart_data, 'y'):
                            x_data = chart_data.x
                            y_data = chart_data.y
                            
                            print(f"X轴数据: {x_data}")
                            print(f"Y轴数据: {y_data}")
                            
                            # 检查是否有异常值
                            if hasattr(y_data, '__iter__'):
                                y_array = np.array(y_data)
                                has_inf = np.isinf(y_array).any()
                                has_nan = np.isnan(y_array).any()
                                print(f"Y轴包含无穷大: {has_inf}")
                                print(f"Y轴包含NaN: {has_nan}")
                else:
                    print("❌ 未找到图表对象")
                    
            except Exception as e:
                print(f"❌ 图表创建失败: {e}")
            
            print()
    
    except ImportError:
        print("❌ Plotly不可用")

def test_data_cleaning():
    """测试数据清理"""
    print("\n🔍 测试数据清理")
    print("=" * 30)
    
    # 创建包含异常值的测试数据
    problematic_data = pd.DataFrame({
        '产品名称': ['A', 'B', 'C', 'D'],
        '销售额': [1000, np.inf, 2000, -np.inf],
        '销量': [10, 20, np.nan, 30]
    })
    
    print("包含异常值的数据:")
    print(problematic_data)
    print()
    
    # 清理数据
    cleaned_data = problematic_data.copy()
    
    # 替换无穷大值
    cleaned_data = cleaned_data.replace([np.inf, -np.inf], np.nan)
    
    # 填充NaN值
    numeric_columns = cleaned_data.select_dtypes(include=[np.number]).columns
    cleaned_data[numeric_columns] = cleaned_data[numeric_columns].fillna(0)
    
    print("清理后的数据:")
    print(cleaned_data)
    print()
    
    # 测试图表创建
    try:
        import plotly.express as px
        fig = px.bar(cleaned_data, x='产品名称', y='销售额', title='清理后的数据图表')
        print("✅ 清理后的数据图表创建成功")
    except Exception as e:
        print(f"❌ 清理后的数据图表创建失败: {e}")

if __name__ == "__main__":
    debug_infinite_extent_error()
    test_plotly_chart_creation()
    test_data_cleaning()
    
    print("\n" + "=" * 60)
    print("🎯 Infinite extent 错误分析总结")
    print("=" * 60)
    print("问题：图表显示后闪退，控制台显示 Infinite extent 警告")
    print()
    print("可能原因：")
    print("1. 数据处理过程中产生了无穷大值")
    print("2. 字段命名异常（出现_start和_end后缀）")
    print("3. Plotly图表配置问题")
    print("4. 数据类型转换错误")
    print()
    print("解决方向：")
    print("1. 添加数据清理机制，处理无穷大值和NaN")
    print("2. 检查数据处理代码，避免产生异常字段")
    print("3. 优化Plotly图表配置")
    print("4. 添加数据验证步骤")
