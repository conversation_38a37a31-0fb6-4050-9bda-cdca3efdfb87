#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的通义千问集成测试
"""

import os
import pandas as pd
from dotenv import load_dotenv
import requests
import json

def test_api_directly():
    """直接测试通义千问API"""
    print("🔗 直接测试通义千问API...")
    
    # 加载环境变量
    load_dotenv()
    api_key = os.getenv('DASHSCOPE_API_KEY')
    
    if not api_key:
        print("❌ 未找到DASHSCOPE_API_KEY")
        return False
    
    print(f"✅ API密钥: {api_key[:10]}...{api_key[-4:]}")
    
    # 测试API调用
    url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "qwen-plus",
        "messages": [
            {"role": "user", "content": "请回答：1+1等于多少？"}
        ],
        "temperature": 0.1,
        "max_tokens": 100
    }
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            print(f"✅ API调用成功: {content}")
            return True
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API调用异常: {e}")
        return False

def test_pandasai_with_custom_llm():
    """使用自定义LLM类测试PandasAI"""
    print("\n🤖 测试PandasAI自定义LLM...")
    
    from pandasai.llm.base import LLM
    from pandasai import SmartDataframe
    
    class TongyiQianwenLLM(LLM):
        """通义千问自定义LLM类"""
        
        def __init__(self, api_key):
            self.api_key = api_key
            self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
            self.model = "qwen-plus"
        
        def call(self, instruction, value):
            """调用通义千问API"""
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # 构建提示词
            prompt = f"""
你是一个数据分析专家。请根据以下数据和指令生成Python代码。

数据信息:
{value}

用户指令:
{instruction}

请生成可执行的Python代码来回答用户的问题。代码应该使用pandas DataFrame，变量名为'df'。
只返回Python代码，不要包含任何解释。
"""
            
            data = {
                "model": self.model,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "temperature": 0.1,
                "max_tokens": 1000
            }
            
            try:
                response = requests.post(self.base_url, headers=headers, json=data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    return result['choices'][0]['message']['content']
                else:
                    return f"# API调用失败: {response.status_code}"
                    
            except Exception as e:
                return f"# API调用异常: {e}"
        
        @property
        def type(self):
            return "tongyi_qianwen"
    
    try:
        # 加载API密钥
        load_dotenv()
        api_key = os.getenv('DASHSCOPE_API_KEY')
        
        # 创建自定义LLM
        llm = TongyiQianwenLLM(api_key)
        print("✅ 自定义LLM创建成功")
        
        # 创建测试数据
        data = {
            '姓名': ['张三', '李四', '王五'],
            '年龄': [25, 30, 35],
            '工资': [8000, 12000, 15000]
        }
        df = pd.DataFrame(data)
        print("✅ 测试数据创建成功")
        
        # 创建SmartDataframe
        smart_df = SmartDataframe(df, config={
            "llm": llm,
            "verbose": True,
            "conversational": False
        })
        print("✅ SmartDataframe创建成功")
        
        # 测试查询
        print("\n执行测试查询...")
        result = smart_df.chat("计算平均工资")
        print(f"✅ 查询结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ PandasAI测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("通义千问集成简单测试")
    print("=" * 30)
    
    # 1. 直接测试API
    api_ok = test_api_directly()
    
    if not api_ok:
        print("\n❌ API测试失败，请检查配置")
        return
    
    # 2. 测试PandasAI集成
    print("\n" + "=" * 30)
    pandasai_ok = test_pandasai_with_custom_llm()
    
    # 3. 总结
    print("\n" + "=" * 30)
    print("测试总结:")
    print(f"API连接: {'✅ 成功' if api_ok else '❌ 失败'}")
    print(f"PandasAI集成: {'✅ 成功' if pandasai_ok else '❌ 失败'}")
    
    if api_ok and pandasai_ok:
        print("\n🎉 通义千问集成测试完全成功!")
        print("现在可以使用通义千问进行数据分析了!")
    else:
        print("\n⚠️  部分功能存在问题，请检查配置")

if __name__ == "__main__":
    main()
