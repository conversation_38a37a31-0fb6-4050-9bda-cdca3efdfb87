#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时追踪销售额_start和销售额_end字段的来源
在Streamlit应用运行时进行动态追踪
"""

import pandas as pd
import numpy as np
import sys
import traceback
from pathlib import Path

def hook_dataframe_operations():
    """钩子DataFrame操作，追踪字段创建"""
    print("🔍 设置DataFrame操作钩子")
    
    # 保存原始方法
    original_setitem = pd.DataFrame.__setitem__
    original_assign = pd.DataFrame.assign
    original_concat = pd.concat
    
    def traced_setitem(self, key, value):
        """追踪DataFrame列赋值"""
        if isinstance(key, str) and ('销售额_start' in key or '销售额_end' in key):
            print(f"🚨 检测到问题字段创建: {key}")
            print(f"📍 调用栈:")
            for line in traceback.format_stack()[-5:]:
                print(f"  {line.strip()}")
            print(f"📊 值类型: {type(value)}")
            if hasattr(value, 'dtype'):
                print(f"📊 数据类型: {value.dtype}")
            if hasattr(value, '__len__') and len(value) < 10:
                print(f"📊 值内容: {value}")
        return original_setitem(self, key, value)
    
    def traced_assign(self, **kwargs):
        """追踪DataFrame.assign操作"""
        for key in kwargs.keys():
            if '销售额_start' in key or '销售额_end' in key:
                print(f"🚨 检测到assign创建问题字段: {key}")
                print(f"📍 调用栈:")
                for line in traceback.format_stack()[-5:]:
                    print(f"  {line.strip()}")
        return original_assign(self, **kwargs)
    
    def traced_concat(*args, **kwargs):
        """追踪pandas.concat操作"""
        result = original_concat(*args, **kwargs)
        if hasattr(result, 'columns'):
            problematic_cols = [col for col in result.columns if '销售额_start' in str(col) or '销售额_end' in str(col)]
            if problematic_cols:
                print(f"🚨 检测到concat创建问题字段: {problematic_cols}")
                print(f"📍 调用栈:")
                for line in traceback.format_stack()[-5:]:
                    print(f"  {line.strip()}")
        return result
    
    # 应用钩子
    pd.DataFrame.__setitem__ = traced_setitem
    pd.DataFrame.assign = traced_assign
    pd.concat = traced_concat
    
    print("✅ DataFrame操作钩子已设置")

def check_current_session_state():
    """检查当前session_state"""
    print("🔍 检查当前session_state")
    print("=" * 50)
    
    try:
        import streamlit as st
        if hasattr(st, 'session_state'):
            if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
                df = st.session_state.current_data
                print(f"📊 session_state数据形状: {df.shape}")
                print(f"📋 session_state列名: {list(df.columns)}")
                
                problematic_fields = ['销售额_start', '销售额_end']
                for field in problematic_fields:
                    if field in df.columns:
                        print(f"❌ session_state包含问题字段: {field}")
                        field_data = df[field]
                        print(f"   数据类型: {field_data.dtype}")
                        print(f"   无穷大值: {np.isinf(field_data).sum()}")
                        print(f"   示例值: {field_data.head().tolist()}")
                        return True
                
                print("✅ session_state数据正常")
                return False
            else:
                print("⚠️ session_state.current_data为空")
                return False
        else:
            print("⚠️ 不在Streamlit环境中")
            return False
    except Exception as e:
        print(f"❌ 检查session_state失败: {e}")
        return False

def trace_llm_code_generation():
    """追踪LLM代码生成过程"""
    print("\n🔍 追踪LLM代码生成过程")
    print("=" * 50)
    
    try:
        # 检查enhanced_tongyi_integration是否被导入
        if 'enhanced_tongyi_integration' in sys.modules:
            print("✅ enhanced_tongyi_integration已导入")
            
            # 获取模块
            llm_module = sys.modules['enhanced_tongyi_integration']
            
            # 检查是否有当前数据
            if hasattr(llm_module, 'EnhancedTongyiQianwenLLM'):
                print("✅ EnhancedTongyiQianwenLLM类可用")
                
                # 尝试创建实例并检查
                try:
                    llm = llm_module.EnhancedTongyiQianwenLLM()
                    if hasattr(llm, 'current_dataframe') and llm.current_dataframe is not None:
                        df = llm.current_dataframe
                        print(f"📊 LLM当前数据形状: {df.shape}")
                        print(f"📋 LLM当前数据列名: {list(df.columns)}")
                        
                        problematic_fields = ['销售额_start', '销售额_end']
                        for field in problematic_fields:
                            if field in df.columns:
                                print(f"❌ LLM数据包含问题字段: {field}")
                                return True
                        
                        print("✅ LLM数据正常")
                    else:
                        print("⚠️ LLM当前数据为空")
                except Exception as e:
                    print(f"⚠️ 创建LLM实例失败: {e}")
            else:
                print("❌ EnhancedTongyiQianwenLLM类不可用")
        else:
            print("⚠️ enhanced_tongyi_integration未导入")
        
        return False
    except Exception as e:
        print(f"❌ 追踪LLM失败: {e}")
        return False

def check_uploaded_files():
    """检查上传文件目录"""
    print("\n🔍 检查上传文件目录")
    print("=" * 50)
    
    upload_dir = Path("uploaded_files")
    if not upload_dir.exists():
        print("❌ 上传目录不存在")
        return False
    
    csv_files = list(upload_dir.glob("*.csv"))
    print(f"📁 发现CSV文件: {len(csv_files)}")
    
    for csv_file in csv_files:
        print(f"\n📄 检查文件: {csv_file.name}")
        try:
            df = pd.read_csv(csv_file)
            print(f"  形状: {df.shape}")
            print(f"  列名: {list(df.columns)}")
            
            problematic_fields = ['销售额_start', '销售额_end']
            for field in problematic_fields:
                if field in df.columns:
                    print(f"  ❌ 文件包含问题字段: {field}")
                    field_data = df[field]
                    inf_count = np.isinf(field_data).sum()
                    print(f"     无穷大值: {inf_count}")
                    print(f"     示例值: {field_data.head().tolist()}")
                    return True
            
            print("  ✅ 文件正常")
        except Exception as e:
            print(f"  ❌ 读取失败: {e}")
    
    return False

def trace_code_execution():
    """追踪代码执行过程"""
    print("\n🔍 追踪代码执行过程")
    print("=" * 50)
    
    # 检查是否有正在执行的代码
    frame = sys._getframe()
    while frame:
        filename = frame.f_code.co_filename
        function_name = frame.f_code.co_name
        line_number = frame.f_lineno
        
        # 检查是否在关键模块中
        if any(module in filename for module in ['enhanced_tongyi', 'perfect_tongyi', 'result_formatter', 'metadata']):
            print(f"📍 执行位置: {filename}:{line_number} in {function_name}")
            
            # 检查局部变量
            local_vars = frame.f_locals
            for var_name, var_value in local_vars.items():
                if isinstance(var_value, pd.DataFrame):
                    problematic_fields = ['销售额_start', '销售额_end']
                    for field in problematic_fields:
                        if field in var_value.columns:
                            print(f"❌ 发现问题字段在变量 {var_name}: {field}")
                            return True
        
        frame = frame.f_back
    
    return False

def monitor_vega_lite_warnings():
    """监控Vega-Lite警告"""
    print("\n🔍 监控Vega-Lite警告")
    print("=" * 50)
    
    # 检查是否有JavaScript控制台输出
    print("💡 请检查浏览器控制台是否有以下警告:")
    print("   - WARN Infinite extent for field \"销售额_start\"")
    print("   - WARN Infinite extent for field \"销售额_end\"")
    print("   - WARN Scale bindings are currently only supported")
    
    print("\n💡 如果看到这些警告，说明:")
    print("   1. 数据仍然包含这些字段")
    print("   2. 字段中仍有无穷大值")
    print("   3. 需要进一步追踪数据来源")

def create_runtime_monitor():
    """创建运行时监控脚本"""
    print("\n📝 创建运行时监控脚本")
    print("=" * 50)
    
    monitor_script = '''
import streamlit as st
import pandas as pd
import numpy as np

def monitor_data_changes():
    """监控数据变化"""
    st.write("🔍 实时数据监控")
    
    if hasattr(st.session_state, 'current_data') and st.session_state.current_data is not None:
        df = st.session_state.current_data
        st.write(f"📊 当前数据形状: {df.shape}")
        st.write(f"📋 当前列名: {list(df.columns)}")
        
        problematic_fields = ['销售额_start', '销售额_end']
        for field in problematic_fields:
            if field in df.columns:
                st.error(f"❌ 发现问题字段: {field}")
                field_data = df[field]
                st.write(f"数据类型: {field_data.dtype}")
                st.write(f"无穷大值数量: {np.isinf(field_data).sum()}")
                st.write("示例值:")
                st.write(field_data.head())
            else:
                st.success(f"✅ 未发现字段: {field}")
    else:
        st.info("⚠️ 当前无数据")

if __name__ == "__main__":
    st.title("🔍 数据监控工具")
    monitor_data_changes()
    
    if st.button("刷新监控"):
        st.rerun()
'''
    
    with open("runtime_monitor.py", "w", encoding="utf-8") as f:
        f.write(monitor_script)
    
    print("✅ 运行时监控脚本已创建: runtime_monitor.py")
    print("💡 您可以在Streamlit中运行此脚本来实时监控数据")

def main():
    """主函数"""
    print("🔍 实时追踪销售额_start和销售额_end字段来源")
    print("=" * 60)
    
    # 设置钩子
    hook_dataframe_operations()
    
    # 执行各种检查
    issues_found = []
    
    # 1. 检查session_state
    if check_current_session_state():
        issues_found.append("session_state包含问题字段")
    
    # 2. 追踪LLM
    if trace_llm_code_generation():
        issues_found.append("LLM数据包含问题字段")
    
    # 3. 检查文件
    if check_uploaded_files():
        issues_found.append("上传文件包含问题字段")
    
    # 4. 追踪执行
    if trace_code_execution():
        issues_found.append("执行过程中发现问题字段")
    
    # 5. 监控警告
    monitor_vega_lite_warnings()
    
    # 6. 创建监控工具
    create_runtime_monitor()
    
    print(f"\n📊 追踪结果:")
    if issues_found:
        print("❌ 发现问题:")
        for issue in issues_found:
            print(f"  - {issue}")
    else:
        print("✅ 未发现明显问题")
    
    print(f"\n💡 下一步建议:")
    print("1. 在Streamlit应用中运行 runtime_monitor.py")
    print("2. 检查浏览器控制台的具体警告信息")
    print("3. 在生成图表时观察钩子输出")
    print("4. 如果仍有问题，可能需要检查代码生成过程")

if __name__ == "__main__":
    main()
